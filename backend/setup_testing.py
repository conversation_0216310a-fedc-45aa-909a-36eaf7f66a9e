#!/usr/bin/env python3
"""
Testing Setup Script
====================

This script sets up the improved testing infrastructure for the incident management backend.
It installs dependencies, validates the setup, and runs a test suite to verify everything works.
"""

import os
import subprocess
import sys
from pathlib import Path


def run_command(command, description, check=True):
    """Run a shell command with error handling."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(f"✅ {description} completed successfully")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr}")
        if not check:
            return e
        sys.exit(1)


def check_python_version():
    """Check if Python version is compatible."""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 10):
        print(f"❌ Python {version.major}.{version.minor} is not supported. Please use Python 3.10+")
        sys.exit(1)
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")


def install_dependencies():
    """Install testing dependencies."""
    print("\n📦 Installing testing dependencies...")
    
    # Install development requirements
    run_command("pip install -r requirements-dev.txt", "Installing development requirements")
    
    # Verify key packages are installed
    key_packages = ["pytest", "pytest-mock", "pytest-asyncio", "pytest-cov", "fakeredis"]
    for package in key_packages:
        result = run_command(f"pip show {package}", f"Verifying {package} installation", check=False)
        if result.returncode != 0:
            print(f"⚠️  {package} not found, installing separately...")
            run_command(f"pip install {package}", f"Installing {package}")


def setup_environment():
    """Setup test environment."""
    print("\n🔧 Setting up test environment...")
    
    # Check if .env.test exists
    env_test_path = Path(".env.test")
    if env_test_path.exists():
        print("✅ .env.test file already exists")
    else:
        print("⚠️  .env.test file not found - this should have been created by the setup")
    
    # Create test directories
    test_dirs = ["tests/data", "htmlcov", "test_uploads"]
    for test_dir in test_dirs:
        Path(test_dir).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {test_dir}")


def validate_mock_infrastructure():
    """Validate that mock infrastructure is working."""
    print("\n🧪 Validating mock infrastructure...")
    
    try:
        # Test imports
        sys.path.append(".")
        from tests.mocks import (
            MockQdrantConnector,
            MockVectorSearchService,
            MockCeleryApp,
            MockRedis,
            MockGitHubConnector,
            MockGoogleAI
        )
        print("✅ Mock imports successful")
        
        # Test basic mock functionality
        redis_mock = MockRedis()
        redis_mock.set("test_key", "test_value")
        assert redis_mock.get("test_key") == b"test_value"
        print("✅ Redis mock working")
        
        celery_mock = MockCeleryApp()
        assert celery_mock.name == "test_app"
        print("✅ Celery mock working")
        
        google_ai_mock = MockGoogleAI()
        embedding = google_ai_mock.generate_embedding("test text")
        assert len(embedding) == 768
        print("✅ Google AI mock working")
        
        print("✅ All mock infrastructure validated successfully")
        
    except Exception as e:
        print(f"❌ Mock validation failed: {e}")
        sys.exit(1)


def run_test_suite():
    """Run the test suite to verify everything works."""
    print("\n🧪 Running test suite...")
    
    # Run a subset of tests first to check basic functionality
    print("\n📋 Running basic test validation...")
    result = run_command(
        "python -m pytest tests/routes/incidents/test_incidents_improved.py -v --tb=short",
        "Running improved incident tests",
        check=False
    )
    
    if result.returncode == 0:
        print("✅ Improved tests are working!")
    else:
        print("⚠️  Some improved tests failed, but this is expected if the full application isn't set up")
    
    # Run all tests with coverage
    print("\n📊 Running full test suite with coverage...")
    result = run_command(
        "python -m pytest tests/ -v --tb=short --cov=app --cov-report=term-missing --cov-report=html",
        "Running full test suite with coverage",
        check=False
    )
    
    if result.returncode == 0:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed - this is expected during migration")
        print("Check the output above for specific failures")


def generate_report():
    """Generate a setup report."""
    print("\n📋 Setup Report")
    print("=" * 50)
    
    # Check file structure
    important_files = [
        ".env.test",
        "pytest.ini", 
        "requirements-dev.txt",
        "tests/conftest.py",
        "tests/mocks/__init__.py",
        "tests/utils.py",
        "tests/README.md"
    ]
    
    print("\n📁 File Structure:")
    for file_path in important_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
    
    # Check directories
    important_dirs = [
        "tests/mocks",
        "tests/routes",
        "htmlcov"
    ]
    
    print("\n📂 Directories:")
    for dir_path in important_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} - MISSING")
    
    print("\n🎯 Next Steps:")
    print("1. Review the test output above for any failures")
    print("2. Migrate existing tests using the patterns in tests/routes/incidents/test_incidents_improved.py")
    print("3. Run 'pytest --cov=app --cov-report=html' to generate coverage reports")
    print("4. Check tests/README.md for detailed usage guide")
    print("5. Use 'pytest -m unit' to run only unit tests")
    print("6. Use 'pytest -m integration' to run only integration tests")


def main():
    """Main setup function."""
    print("🚀 Setting up Improved Testing Infrastructure")
    print("=" * 60)
    
    # Change to backend directory if not already there
    if not Path("app").exists() and Path("backend/app").exists():
        os.chdir("backend")
        print("📁 Changed to backend directory")
    
    # Validate environment
    check_python_version()
    
    # Setup steps
    install_dependencies()
    setup_environment()
    validate_mock_infrastructure()
    run_test_suite()
    generate_report()
    
    print("\n🎉 Testing infrastructure setup complete!")
    print("\n💡 Pro tip: Run 'pytest -v' to see all available tests")
    print("💡 Pro tip: Check htmlcov/index.html for detailed coverage report")


if __name__ == "__main__":
    main()

[pytest]
timeout = 30
asyncio_mode = auto
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -p no:warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::RuntimeWarning
    ignore::pytest.PytestUnraisableExceptionWarning
    ignore:.*crypt.*:DeprecationWarning
    ignore:.*Deprecated.*:DeprecationWarning
    ignore:.*deprecated.*:DeprecationWarning
asyncio_default_fixture_loop_scope = function

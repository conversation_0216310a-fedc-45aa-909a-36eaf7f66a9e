[pytest]
# Test discovery
python_files = test_*.py
python_classes = Test*
python_functions = test_*
testpaths = tests

# Async configuration
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Test execution
addopts =
    -v
    --tb=short
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80
    -p no:warnings

# Environment variables for tests
env =
    TESTING=true
    MOCK_EXTERNAL_SERVICES=true
    SECRET_KEY=test-secret-key-for-jwt-tokens-in-testing-environment-only
    CELERY_TASK_ALWAYS_EAGER=true
    CELERY_TASK_EAGER_PROPAGATES=true

# Warning filters
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::RuntimeWarning
    ignore::pytest.PytestUnraisableExceptionWarning
    ignore:.*crypt.*:DeprecationWarning
    ignore:.*Deprecated.*:DeprecationWarning
    ignore:.*deprecated.*:DeprecationWarning
    ignore::sqlalchemy.exc.SAWarning
    ignore::requests.packages.urllib3.exceptions.InsecureRequestWarning

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    external: Tests that require external services
    mock: Tests using mocks

[pytest]
python_files = test_*.py
python_classes = Test*
python_functions = test_*
testpaths = tests
asyncio_mode = auto

addopts =
    -v
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-fail-under=30
    -p no:warnings

env =
    TESTING=true
    SECRET_KEY=test-secret-key-for-jwt-tokens-in-testing-environment-only

filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning

markers =
    unit: Unit tests
    integration: Integration tests

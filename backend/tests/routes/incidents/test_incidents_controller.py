import pytest
from fastapi.testclient import TestClient
from routes.incidents.models import (
    IncidentTypeEnum,
    PriorityEnum,
    SeverityEnum,
    StatusEnum,
)


@pytest.fixture
def incident_auth_token(client: TestClient):
    """Fixture to register a test user and get auth token"""
    # Register a user
    register_data = {
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "password": "testpassword123",
    }
    response = client.post("/auth/register", json=register_data)
    assert response.status_code == 201

    # Get token
    login_response = client.post(
        "/auth/token",
        data={
            "username": register_data["email"],
            "password": register_data["password"],
            "grant_type": "password",
        },
    )
    assert login_response.status_code == 200
    token_data = login_response.json()
    return token_data["access_token"]


@pytest.fixture
def sample_incident_data():
    """Fixture to create sample incident data"""
    return {
        "title": "Test Incident",
        "summary": "This is a test incident",
        "priority": PriorityEnum.P1.value,
        "severity": SeverityEnum.HIGH.value,
        "incident_type": IncidentTypeEnum.OUTAGE.value,
        "affected_services": ["API Gateway"],
        "tags": ["test", "api"],
        "incident_details": "Service is down. Expected behavior: Service should be up. Possible cause: Network issue. Suggested fix: Restart service. Additional context: No additional context",
    }


@pytest.fixture
def created_incident(client: TestClient, incident_auth_token, sample_incident_data):
    """Fixture to create a test incident and return its data"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}
    response = client.post(
        "/incidents/create", json=sample_incident_data, headers=headers
    )
    assert response.status_code == 201
    return response.json()


def test_create_incident(client: TestClient, incident_auth_token, sample_incident_data):
    """Test creating a new incident"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    response = client.post(
        "/incidents/create", json=sample_incident_data, headers=headers
    )

    assert response.status_code == 201
    data = response.json()
    assert data["title"] == sample_incident_data["title"]
    assert data["summary"] == sample_incident_data["summary"]
    assert data["priority"] == sample_incident_data["priority"]
    assert data["severity"] == sample_incident_data["severity"]
    assert data["incident_type"] == sample_incident_data["incident_type"]
    assert "id" in data
    assert "incident_number" in data
    assert "reporter" in data
    assert "reported_at" in data


def test_get_incident(client: TestClient, incident_auth_token, created_incident):
    """Test getting a specific incident by ID"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    response = client.get(f"/incidents/{created_incident['id']}", headers=headers)

    assert response.status_code == 200
    data = response.json()
    assert data["id"] == created_incident["id"]
    assert data["title"] == created_incident["title"]


def test_update_incident(client: TestClient, incident_auth_token, created_incident):
    """Test updating an existing incident"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    update_data = {
        "title": "Updated Incident Title",
        "status": StatusEnum.ACTIVE.value,
    }

    response = client.put(
        f"/incidents/{created_incident['id']}", json=update_data, headers=headers
    )

    assert response.status_code == 200
    data = response.json()
    assert data["id"] == created_incident["id"]
    assert data["title"] == update_data["title"]
    assert data["status"] == update_data["status"]


def test_delete_incident(client: TestClient, incident_auth_token, created_incident):
    """Test deleting an incident"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    response = client.delete(f"/incidents/{created_incident['id']}", headers=headers)

    assert response.status_code == 200

    # Verify the incident is deleted
    get_response = client.get(f"/incidents/{created_incident['id']}", headers=headers)
    assert get_response.status_code == 404


def test_get_incidents_pagination(client: TestClient, incident_auth_token):
    """Test getting paginated list of incidents"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Create multiple incidents
    for i in range(3):
        incident_data = {
            "title": f"Test Incident {i}",
            "summary": f"This is test incident {i}",
            "priority": PriorityEnum.P2.value,
            "severity": SeverityEnum.MEDIUM.value,
            "incident_type": IncidentTypeEnum.OUTAGE.value,
            "affected_services": ["API Gateway"],
            "tags": ["test", f"incident-{i}"],
            "incident_details": "Service is slow. Expected behavior: Service should be fast. Possible cause: High load. Suggested fix: Scale up.",
        }
        response = client.post("/incidents/create", json=incident_data, headers=headers)
        assert response.status_code == 201

    # Test pagination
    response = client.get("/incidents?offset=0&limit=2", headers=headers)

    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "limit" in data
    assert "pages" in data
    assert len(data["items"]) <= 2
    assert data["limit"] == 2


def test_get_incidents_with_filters(client: TestClient, incident_auth_token):
    """Test getting incidents with filters"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Create incidents with different types
    incident_types = [IncidentTypeEnum.OUTAGE.value, IncidentTypeEnum.DEGRADATION.value]

    for i, incident_type in enumerate(incident_types):
        incident_data = {
            "title": f"Test Incident {i}",
            "summary": f"This is test incident {i}",
            "priority": PriorityEnum.P1.value,
            "severity": SeverityEnum.HIGH.value,
            "incident_type": incident_type,
            "affected_services": ["Database"],
            "tags": ["test"],
            "incident_details": "Service has issues. Expected behavior: Service should work properly.",
        }
        response = client.post("/incidents/create", json=incident_data, headers=headers)
        assert response.status_code == 201

    # Test filtering by incident type
    response = client.get(
        f"/incidents?incident_type={IncidentTypeEnum.OUTAGE.value}", headers=headers
    )

    assert response.status_code == 200
    data = response.json()
    assert all(
        item["incident_type"] == IncidentTypeEnum.OUTAGE.value for item in data["items"]
    )


def test_unauthorized_access(client: TestClient, created_incident):
    """Test accessing endpoints without authentication"""
    # Try to get incidents without token
    response = client.get("/incidents")
    assert response.status_code == 401

    # Try to create incident without token
    incident_data = {
        "title": "Unauthorized Incident",
        "summary": "This should fail",
        "priority": PriorityEnum.P4.value,
        "severity": SeverityEnum.LOW.value,
        "incident_type": IncidentTypeEnum.OUTAGE.value,
        "affected_services": ["API"],
        "tags": ["test"],
        "incident_details": "Service is down. Expected behavior: Service should be up. Possible cause: Network issue. Suggested fix: Restart service. Additional context: No additional context",
    }
    response = client.post("/incidents/create", json=incident_data)
    assert response.status_code == 401


def test_get_incident_by_number(
    client: TestClient, incident_auth_token, created_incident
):
    """Test getting an incident by incident number instead of ID"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    response = client.get(
        f"/incidents/{created_incident['incident_number']}", headers=headers
    )

    assert response.status_code == 200
    data = response.json()
    assert data["incident_number"] == created_incident["incident_number"]
    assert data["title"] == created_incident["title"]


def test_get_incident_details(
    client: TestClient, incident_auth_token, created_incident
):
    """Test getting incident details"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    response = client.get(
        f"/incidents/{created_incident['id']}/details", headers=headers
    )

    assert response.status_code == 200
    data = response.json()
    assert data["incident_id"] == created_incident["id"]
    assert "affected_services" in data
    assert "tags" in data
    assert "incident_details" in data
    assert "attachments" in data


def test_update_incident_details(
    client: TestClient, incident_auth_token, created_incident
):
    """Test updating incident details"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    update_data = {
        "affected_services": ["Database", "API Gateway"],
        "tags": ["updated", "critical"],
        "attachments": ["screenshot.png", "logs.txt"],
    }

    response = client.put(
        f"/incidents/{created_incident['id']}/details",
        json=update_data,
        headers=headers,
    )

    assert response.status_code == 200
    data = response.json()
    assert data["affected_services"] == update_data["affected_services"]
    assert data["tags"] == update_data["tags"]
    assert data["attachments"] == update_data["attachments"]


def test_get_incident_ai_analysis(
    client: TestClient, incident_auth_token, created_incident
):
    """Test getting AI analysis for an incident"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    response = client.get(
        f"/incidents/{created_incident['id']}/ai-analysis", headers=headers
    )

    # This endpoint might return 200 with analysis or generate one
    # The exact behavior depends on whether analysis exists or needs to be generated
    assert response.status_code in [
        200,
        500,
    ]  # 500 if AI service not available in tests

    if response.status_code == 200:
        data = response.json()
        assert data["incident_id"] == created_incident["id"]
        assert "root_cause" in data
        assert "immediate_action" in data
        assert "impact_forecast" in data
        assert "cascading_risks" in data


def test_get_incidents_with_priority_filter(client: TestClient, incident_auth_token):
    """Test getting incidents filtered by priority"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Create incidents with different priorities
    priorities = [PriorityEnum.P1.value, PriorityEnum.P2.value, PriorityEnum.P3.value]

    for i, priority in enumerate(priorities):
        incident_data = {
            "title": f"Priority Test Incident {i}",
            "summary": f"This is priority {priority} incident",
            "priority": priority,
            "severity": SeverityEnum.MEDIUM.value,
            "incident_type": IncidentTypeEnum.OUTAGE.value,
            "affected_services": ["Service"],
            "tags": ["priority-test"],
            "incident_details": "Test incident for priority filtering",
        }
        response = client.post("/incidents/create", json=incident_data, headers=headers)
        assert response.status_code == 201

    # Test filtering by P1 priority
    response = client.get(
        f"/incidents?priority={PriorityEnum.P1.value}", headers=headers
    )

    assert response.status_code == 200
    data = response.json()
    assert all(item["priority"] == PriorityEnum.P1.value for item in data["items"])


def test_get_incidents_with_severity_filter(client: TestClient, incident_auth_token):
    """Test getting incidents filtered by severity"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Create incidents with different severities
    severities = [SeverityEnum.HIGH.value, SeverityEnum.LOW.value]

    for i, severity in enumerate(severities):
        incident_data = {
            "title": f"Severity Test Incident {i}",
            "summary": f"This is {severity} severity incident",
            "priority": PriorityEnum.P2.value,
            "severity": severity,
            "incident_type": IncidentTypeEnum.PERFORMANCE.value,
            "affected_services": ["Service"],
            "tags": ["severity-test"],
            "incident_details": "Test incident for severity filtering",
        }
        response = client.post("/incidents/create", json=incident_data, headers=headers)
        assert response.status_code == 201

    # Test filtering by HIGH severity
    response = client.get(
        f"/incidents?severity={SeverityEnum.HIGH.value}", headers=headers
    )

    assert response.status_code == 200
    data = response.json()
    assert all(item["severity"] == SeverityEnum.HIGH.value for item in data["items"])


def test_get_incidents_with_status_filter(client: TestClient, incident_auth_token):
    """Test getting incidents filtered by status"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Create an incident and update its status
    incident_data = {
        "title": "Status Test Incident",
        "summary": "This is for status filtering",
        "priority": PriorityEnum.P2.value,
        "severity": SeverityEnum.MEDIUM.value,
        "incident_type": IncidentTypeEnum.OUTAGE.value,
        "affected_services": ["Service"],
        "tags": ["status-test"],
        "incident_details": "Test incident for status filtering",
    }
    create_response = client.post(
        "/incidents/create", json=incident_data, headers=headers
    )
    assert create_response.status_code == 201
    incident = create_response.json()

    # Update status to ACTIVE
    update_data = {"status": StatusEnum.ACTIVE.value}
    client.put(f"/incidents/{incident['id']}", json=update_data, headers=headers)

    # Test filtering by ACTIVE status
    response = client.get(
        f"/incidents?status={StatusEnum.ACTIVE.value}", headers=headers
    )

    assert response.status_code == 200
    data = response.json()
    assert any(item["status"] == StatusEnum.ACTIVE.value for item in data["items"])


def test_create_incident_validation_errors(client: TestClient, incident_auth_token):
    """Test validation errors when creating incidents"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Test missing required fields
    invalid_data = {
        "title": "",  # Empty title
        "incident_type": "INVALID_TYPE",  # Invalid enum value
    }

    response = client.post("/incidents/create", json=invalid_data, headers=headers)
    assert response.status_code == 422  # Validation error


def test_create_incident_with_minimal_data(client: TestClient, incident_auth_token):
    """Test creating incident with only required fields"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    minimal_data = {
        "title": "Minimal Incident",
        "incident_type": IncidentTypeEnum.OUTAGE.value,
    }

    response = client.post("/incidents/create", json=minimal_data, headers=headers)

    assert response.status_code == 201
    data = response.json()
    assert data["title"] == minimal_data["title"]
    assert data["incident_type"] == minimal_data["incident_type"]
    assert "id" in data
    assert "incident_number" in data


def test_update_incident_partial(
    client: TestClient, incident_auth_token, created_incident
):
    """Test partial update of incident (only updating specific fields)"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Update only priority
    partial_update = {"priority": PriorityEnum.P3.value}

    response = client.put(
        f"/incidents/{created_incident['id']}", json=partial_update, headers=headers
    )

    assert response.status_code == 200
    data = response.json()
    assert data["priority"] == PriorityEnum.P3.value
    assert data["title"] == created_incident["title"]  # Should remain unchanged


def test_get_nonexistent_incident(client: TestClient, incident_auth_token):
    """Test getting an incident that doesn't exist"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}
    fake_uuid = "00000000-0000-0000-0000-000000000000"

    response = client.get(f"/incidents/{fake_uuid}", headers=headers)

    assert response.status_code == 404


def test_get_nonexistent_incident_details(client: TestClient, incident_auth_token):
    """Test getting details for an incident that doesn't exist"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}
    fake_uuid = "00000000-0000-0000-0000-000000000000"

    response = client.get(f"/incidents/{fake_uuid}/details", headers=headers)

    assert response.status_code == 404


def test_update_nonexistent_incident(client: TestClient, incident_auth_token):
    """Test updating an incident that doesn't exist"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}
    fake_uuid = "00000000-0000-0000-0000-000000000000"

    update_data = {"title": "Updated Title"}
    response = client.put(f"/incidents/{fake_uuid}", json=update_data, headers=headers)

    assert response.status_code == 404


def test_update_nonexistent_incident_details(client: TestClient, incident_auth_token):
    """Test updating details for an incident that doesn't exist"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}
    fake_uuid = "00000000-0000-0000-0000-000000000000"

    update_data = {"tags": ["test"]}
    response = client.put(
        f"/incidents/{fake_uuid}/details", json=update_data, headers=headers
    )

    assert response.status_code == 404


def test_delete_nonexistent_incident(client: TestClient, incident_auth_token):
    """Test deleting an incident that doesn't exist"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}
    fake_uuid = "00000000-0000-0000-0000-000000000000"

    response = client.delete(f"/incidents/{fake_uuid}", headers=headers)

    assert response.status_code == 404


def test_get_incidents_with_multiple_filters(client: TestClient, incident_auth_token):
    """Test getting incidents with multiple filters applied"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Create a specific incident that matches multiple criteria
    specific_incident = {
        "title": "Multi-filter Test Incident",
        "summary": "This incident matches specific criteria",
        "priority": PriorityEnum.P1.value,
        "severity": SeverityEnum.HIGH.value,
        "incident_type": IncidentTypeEnum.SECURITY.value,
        "affected_services": ["Auth Service"],
        "tags": ["security", "auth"],
        "incident_details": "Security incident requiring immediate attention",
    }
    response = client.post("/incidents/create", json=specific_incident, headers=headers)
    assert response.status_code == 201

    # Test filtering with multiple parameters
    response = client.get(
        f"/incidents?priority={PriorityEnum.P1.value}&severity={SeverityEnum.HIGH.value}&incident_type={IncidentTypeEnum.SECURITY.value}",
        headers=headers,
    )

    assert response.status_code == 200
    data = response.json()
    # Should find at least our created incident
    matching_incidents = [
        item
        for item in data["items"]
        if (
            item["priority"] == PriorityEnum.P1.value
            and item["severity"] == SeverityEnum.HIGH.value
            and item["incident_type"] == IncidentTypeEnum.SECURITY.value
        )
    ]
    assert len(matching_incidents) >= 1


def test_pagination_edge_cases(client: TestClient, incident_auth_token):
    """Test pagination with edge cases"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    # Test with offset larger than total items
    response = client.get("/incidents?offset=1000&limit=10", headers=headers)

    assert response.status_code == 200
    data = response.json()
    assert data["items"] == []
    assert data["total"] >= 0

    # Test with limit of 1
    response = client.get("/incidents?offset=0&limit=1", headers=headers)

    assert response.status_code == 200
    data = response.json()
    assert len(data["items"]) <= 1
    assert data["limit"] == 1


def test_create_incident_with_all_fields(client: TestClient, incident_auth_token):
    """Test creating incident with all possible fields populated"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}

    comprehensive_data = {
        "title": "Comprehensive Test Incident",
        "summary": "This incident has all fields populated for testing",
        "priority": PriorityEnum.P2.value,
        "severity": SeverityEnum.MEDIUM.value,
        "incident_type": IncidentTypeEnum.PERFORMANCE.value,
        "affected_services": ["Database", "API Gateway", "Auth Service"],
        "tags": ["performance", "database", "critical"],
        "incident_details": "Detailed description of the incident including timeline, impact, and initial investigation findings",
        "attachments": [
            "error_logs.txt",
            "monitoring_screenshot.png",
            "network_trace.pcap",
        ],
    }

    response = client.post(
        "/incidents/create", json=comprehensive_data, headers=headers
    )

    assert response.status_code == 201
    data = response.json()
    assert data["title"] == comprehensive_data["title"]
    assert data["summary"] == comprehensive_data["summary"]
    assert data["priority"] == comprehensive_data["priority"]
    assert data["severity"] == comprehensive_data["severity"]
    assert data["incident_type"] == comprehensive_data["incident_type"]
    assert "id" in data
    assert "incident_number" in data
    assert "reported_at" in data
    assert "reporter" in data


def test_invalid_uuid_format(client: TestClient, incident_auth_token):
    """Test endpoints with invalid UUID format"""
    headers = {"Authorization": f"Bearer {incident_auth_token}"}
    invalid_uuid = "not-a-valid-uuid"

    # Test get incident with invalid UUID (should try as incident number)
    response = client.get(f"/incidents/{invalid_uuid}", headers=headers)
    assert response.status_code == 404  # Should not find incident with this number

    # Test get incident details with invalid UUID format
    response = client.get(f"/incidents/{invalid_uuid}/details", headers=headers)
    assert response.status_code == 422  # Should fail UUID validation

    # Test update incident with invalid UUID format
    update_data = {"title": "Updated"}
    response = client.put(
        f"/incidents/{invalid_uuid}", json=update_data, headers=headers
    )
    assert response.status_code == 422  # Should fail UUID validation

import pytest
from fastapi.testclient import Test<PERSON>lient
from routes.users.models import PasswordChange


@pytest.fixture
def user_auth_token(client: TestClient):
    """Fixture to register a test user and get auth token"""
    # Register a user
    register_data = {
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "password": "testpassword123",
    }
    response = client.post("/auth/register", json=register_data)
    assert response.status_code == 201

    # Get token
    login_response = client.post(
        "/auth/token",
        data={
            "username": register_data["email"],
            "password": register_data["password"],
            "grant_type": "password",
        },
    )
    assert login_response.status_code == 200
    token_data = login_response.json()
    return token_data["access_token"]


def test_get_current_user(client: TestClient, user_auth_token):
    """Test getting the current user profile"""
    headers = {"Authorization": f"Bearer {user_auth_token}"}
    response = client.get("/users/me", headers=headers)

    assert response.status_code == 200
    user_data = response.json()
    assert "id" in user_data
    assert "email" in user_data
    assert user_data["email"] == "<EMAIL>"
    assert user_data["first_name"] == "Test"
    assert user_data["last_name"] == "User"


def test_change_password_success(client: TestClient, user_auth_token):
    """Test successful password change"""
    headers = {"Authorization": f"Bearer {user_auth_token}"}

    password_change = PasswordChange(
        current_password="testpassword123",
        new_password="newpassword456",
        new_password_confirm="newpassword456",
    )

    response = client.put(
        "/users/change-password", json=password_change.model_dump(), headers=headers
    )

    assert response.status_code == 200

    # Verify we can login with the new password
    login_response = client.post(
        "/auth/token",
        data={
            "username": "<EMAIL>",
            "password": "newpassword456",
            "grant_type": "password",
        },
    )
    assert login_response.status_code == 200


def test_change_password_wrong_current(client: TestClient, user_auth_token):
    """Test password change with wrong current password"""
    headers = {"Authorization": f"Bearer {user_auth_token}"}

    password_change = PasswordChange(
        current_password="wrongpassword",
        new_password="newpassword456",
        new_password_confirm="newpassword456",
    )

    response = client.put(
        "/users/change-password", json=password_change.model_dump(), headers=headers
    )

    assert response.status_code == 401  # Unauthorized for invalid password


def test_change_password_mismatch(client: TestClient, user_auth_token):
    """Test password change with mismatched new passwords"""
    headers = {"Authorization": f"Bearer {user_auth_token}"}

    response = client.put(
        "/users/change-password",
        json={
            "current_password": "testpassword123",
            "new_password": "newpassword456",
            "new_password_confirm": "differentpassword",
        },
        headers=headers,
    )

    assert response.status_code == 422  # Validation error for password mismatch
    assert "Passwords do not match" in response.json()["detail"][0]["msg"]


def test_delete_user(client: TestClient, user_auth_token):
    """Test deleting a user"""
    # Get a valid user ID first
    headers = {"Authorization": f"Bearer {user_auth_token}"}
    user_response = client.get("/users/me", headers=headers)
    user_id = user_response.json()["id"]

    # Delete the user
    response = client.delete(f"/users/{user_id}", headers=headers)

    assert response.status_code == 200
    assert response.json()["message"] == "User deleted successfully"


def test_unauthorized_access(client: TestClient):
    """Test accessing endpoints without authentication"""
    # Try to access user profile without token
    response = client.get("/users/me")
    assert response.status_code == 401

    # Try to change password without token
    password_change = PasswordChange(
        current_password="testpassword123",
        new_password="newpassword456",
        new_password_confirm="newpassword456",
    )
    response = client.put("/users/change-password", json=password_change.model_dump())
    assert response.status_code == 401

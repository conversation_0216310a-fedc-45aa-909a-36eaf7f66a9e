import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from routes.auth.models import CreateUserR<PERSON>quest


@pytest.fixture
def registered_user(client: TestClient):
    """Fixture to register a test user and return the user data"""
    register_data = CreateUserRequest(
        first_name="Test",
        last_name="User",
        email="<EMAIL>",
        password="testpassword123",
    )
    response = client.post("/auth/register", json=register_data.model_dump())
    assert response.status_code == 201
    return register_data


@pytest.fixture
def auth_token(client: TestClient, registered_user):
    """Fixture to get an authentication token for the registered user"""
    login_response = client.post(
        "/auth/token",
        data={
            "username": registered_user.email,
            "password": registered_user.password,
            "grant_type": "password",
        },
    )
    assert login_response.status_code == 200
    token_data = login_response.json()
    assert "access_token" in token_data
    return token_data["access_token"]


def test_register_user(client: TestClient):
    # Test registration
    register_data = CreateUserRequest(
        first_name="Test",
        last_name="User",
        email="<EMAIL>",
        password="testpassword123",
    )

    response = client.post("/auth/register", json=register_data.model_dump())
    assert response.status_code == 201


def test_register_duplicate_email(client: TestClient, registered_user):
    """Test registration with an email that already exists"""
    # Try to register with the same email
    response = client.post(
        "/auth/register",
        json={
            "email": registered_user.email,  # Already registered email
            "password": "differentpassword123",
            "first_name": "Another",
            "last_name": "User",
        },
    )
    # Should return an error (either 400 or 409 depending on your implementation)
    assert response.status_code in (400, 409)


def test_register_with_invalid_password(client: TestClient):
    """Test registration with an invalid password (too short)"""
    response = client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "short",  # Too short password
            "first_name": "Test",
            "last_name": "User",
        },
    )
    assert response.status_code == 422  # Validation error


def test_register_with_invalid_email(client: TestClient):
    """Test registration with an invalid email format"""
    response = client.post(
        "/auth/register",
        json={
            "email": "not-an-email",  # Invalid email format
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
        },
    )
    assert response.status_code == 422  # Validation error
    assert "value is not a valid email address" in response.json()["detail"][0]["msg"]


def test_login(client: TestClient, registered_user):
    # Test successful login
    login_response = client.post(
        "/auth/token",
        data={
            "username": registered_user.email,
            "password": registered_user.password,
            "grant_type": "password",
        },
    )
    assert login_response.status_code == 200
    token_data = login_response.json()
    assert "access_token" in token_data
    assert token_data["token_type"] == "bearer"


def test_login_nonexistent_user(client: TestClient, registered_user):
    response = client.post(
        "/auth/token",
        data={
            "username": "<EMAIL>",
            "password": "wrongpassword",
            "grant_type": "password",
        },
    )
    assert response.status_code == 401


def test_login_wrong_password(client: TestClient, registered_user):
    response = client.post(
        "/auth/token",
        data={
            "username": registered_user.email,
            "password": "wrongpassword",
            "grant_type": "password",
        },
    )
    assert response.status_code == 401


def test_verify_token(client: TestClient, auth_token):
    # Test token verification
    verify_response = client.get(f"/auth/verify-token/{auth_token}")
    assert verify_response.status_code == 200
    import uuid

    user_id = verify_response.json()["user_id"]
    assert user_id, "User ID should be present in response"
    assert uuid.UUID(user_id), f"Expected valid UUID, got {user_id}"


def test_rate_limiting(client: TestClient):
    # Test rate limiting on registration
    for _ in range(6):  # Attempt 6 registrations (limit is 5/hour)
        response = client.post(
            "/auth/register",
            json={
                "email": f"test{_}@example.com",
                "password": "testpassword123",
                "first_name": "Test",
                "last_name": "User",
            },
        )
    assert response.status_code == 429  # Too Many Requests


def test_forgot_password(client: TestClient):
    # Test forgot password endpoint
    response = client.post(
        "/auth/forgot-password",
        json={"email": "<EMAIL>"},
    )
    assert response.status_code == 200
    assert (
        "An email with a password reset link will be sent if the user exists in our system."
        in response.json()["message"]
    )


def test_forgot_password_with_invalid_email(client: TestClient):
    # Test forgot password with invalid email
    response = client.post(
        "/auth/forgot-password",
        json={"email": "not-an-email"},
    )
    assert response.status_code == 422
    assert "value is not a valid email address" in response.json()["detail"][0]["msg"]


def test_reset_password(client: TestClient):
    # Test reset password endpoint
    response = client.post(
        "/auth/reset-password",
        json={
            "secret_token": "test-reset-token",
            "new_password": "newpassword123",
            "confirm_password": "newpassword123",
        },
    )
    assert response.status_code == 200
    assert "Password reset successfully" in response.json()["message"]


def test_reset_password_with_short_password(client: TestClient):
    """Test reset password with a password that's too short"""
    response = client.post(
        "/auth/reset-password",
        json={
            "secret_token": "test-reset-token",
            "new_password": "short",  # Too short password
            "confirm_password": "short",
        },
    )
    assert response.status_code == 422  # Validation error


def test_reset_password_with_mismatched_passwords(client: TestClient):
    """Test reset password with mismatched passwords"""
    response = client.post(
        "/auth/reset-password",
        json={
            "secret_token": "test-reset-token",
            "new_password": "newpassword123",
            "confirm_password": "differentpassword123",  # Doesn't match
        },
    )
    assert response.status_code == 422  # Validation error

"""
Vector Database Mock Implementation
==================================

Provides mock implementations for Qdrant vector database operations
to eliminate external dependencies during testing.
"""

from enum import Enum
from typing import Any, Dict, List, Optional
from unittest.mock import MagicMock
from uuid import UUID

# Try to import the real classes, fall back to mocks if not available
try:
    from vector_db.base_connector import CollectionType, VectorDBConnector
except ImportError:
    # Create fallback classes if the real ones aren't available
    class CollectionType(Enum):
        INCIDENTS = "incidents"
        DOCUMENTS = "documents"

    class VectorDBConnector:
        """Fallback base class for vector database connector."""
        def __init__(self, collections: Dict[CollectionType, str]):
            self._collections = collections
            self._client = None
            self._initialized = False


class MockQdrantConnector(VectorDBConnector):
    """Mock implementation of QdrantConnector for testing."""
    
    def __init__(self, collections: Dict[CollectionType, str]):
        super().__init__(collections)
        self._mock_data = {}  # Store mock vector data
        self._client = MagicMock()
        self._initialized = True
    
    def _create_client(self):
        """Mock client creation - always succeeds."""
        return MagicMock()
    
    def _create_collections_if_not_exist(self):
        """Mock collection creation - always succeeds."""
        pass
    
    def upsert_vector(
        self,
        collection_type: CollectionType,
        vector_id: UUID,
        embedding: List[float],
        metadata: Dict[str, Any]
    ) -> bool:
        """Mock vector upsert operation."""
        collection_name = self._collections[collection_type]
        if collection_name not in self._mock_data:
            self._mock_data[collection_name] = {}
        
        self._mock_data[collection_name][str(vector_id)] = {
            "vector": embedding,
            "metadata": metadata
        }
        return True
    
    def search_similar_vectors(
        self,
        collection_type: CollectionType,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Mock vector similarity search."""
        collection_name = self._collections[collection_type]
        
        if collection_name not in self._mock_data:
            return []
        
        # Return mock results with dummy similarity scores
        results = []
        for vector_id, data in list(self._mock_data[collection_name].items())[:limit]:
            results.append({
                "id": vector_id,
                "score": 0.9,  # Mock high similarity score
                "payload": data["metadata"]
            })
        
        return results
    
    def delete_vector(self, collection_type: CollectionType, vector_id: UUID) -> bool:
        """Mock vector deletion."""
        collection_name = self._collections[collection_type]
        if collection_name in self._mock_data:
            self._mock_data[collection_name].pop(str(vector_id), None)
        return True
    
    def get_vector(
        self, collection_type: CollectionType, vector_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """Mock vector retrieval."""
        collection_name = self._collections[collection_type]
        if collection_name in self._mock_data:
            return self._mock_data[collection_name].get(str(vector_id))
        return None
    
    def collection_exists(self, collection_type: CollectionType) -> bool:
        """Mock collection existence check."""
        return True
    
    def get_collection_info(self, collection_type: CollectionType) -> Dict[str, Any]:
        """Mock collection info retrieval."""
        collection_name = self._collections[collection_type]
        return {
            "name": collection_name,
            "vectors_count": len(self._mock_data.get(collection_name, {})),
            "status": "green"
        }

    def _upsert_vector(
        self,
        collection_type: CollectionType,
        vector_id: UUID,
        embedding: List[float],
        metadata: Dict[str, Any]
    ) -> bool:
        """Internal method for vector upsert - delegates to public method."""
        return self.upsert_vector(collection_type, vector_id, embedding, metadata)

    def _find_similar_vectors(
        self,
        collection_type: CollectionType,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Internal method for similarity search - delegates to public method."""
        return self.search_similar_vectors(collection_type, query_vector, limit, score_threshold)

    def _delete_vector(self, collection_type: CollectionType, vector_id: UUID) -> bool:
        """Internal method for vector deletion - delegates to public method."""
        return self.delete_vector(collection_type, vector_id)


class MockVectorSearchService:
    """Mock implementation of VectorSearchService for testing."""
    
    def __init__(self):
        self.connector = MockQdrantConnector({
            CollectionType.INCIDENTS: "test_incidents",
            CollectionType.DOCUMENTS: "test_documents"
        })
    
    def upsert_incident_embedding(self, incident_id: UUID, db_session) -> bool:
        """Mock incident embedding upsert."""
        # Mock embedding vector (768 dimensions for Gemini)
        mock_embedding = [0.1] * 768
        mock_metadata = {
            "incident_id": str(incident_id),
            "title": "Mock Incident",
            "summary": "Mock incident summary"
        }
        
        return self.connector.upsert_vector(
            CollectionType.INCIDENTS,
            incident_id,
            mock_embedding,
            mock_metadata
        )
    
    def upsert_document_embedding(self, document_id: UUID, db_session) -> bool:
        """Mock document embedding upsert."""
        # Mock embedding vector (768 dimensions for Gemini)
        mock_embedding = [0.2] * 768
        mock_metadata = {
            "document_id": str(document_id),
            "title": "Mock Document",
            "content": "Mock document content"
        }
        
        return self.connector.upsert_vector(
            CollectionType.DOCUMENTS,
            document_id,
            mock_embedding,
            mock_metadata
        )
    
    def search_similar_incidents(
        self, query_text: str, limit: int = 10, score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Mock incident similarity search."""
        # Mock query embedding
        mock_query_embedding = [0.15] * 768
        
        return self.connector.search_similar_vectors(
            CollectionType.INCIDENTS,
            mock_query_embedding,
            limit,
            score_threshold
        )
    
    def search_similar_documents(
        self, query_text: str, limit: int = 10, score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Mock document similarity search."""
        # Mock query embedding
        mock_query_embedding = [0.25] * 768
        
        return self.connector.search_similar_vectors(
            CollectionType.DOCUMENTS,
            mock_query_embedding,
            limit,
            score_threshold
        )
    
    def delete_incident_embedding(self, incident_id: UUID) -> bool:
        """Mock incident embedding deletion."""
        return self.connector.delete_vector(CollectionType.INCIDENTS, incident_id)
    
    def delete_document_embedding(self, document_id: UUID) -> bool:
        """Mock document embedding deletion."""
        return self.connector.delete_vector(CollectionType.DOCUMENTS, document_id)


# Mock embedding generation function
def mock_generate_embedding(text: str) -> List[float]:
    """Mock embedding generation that returns consistent fake embeddings."""
    # Generate a deterministic fake embedding based on text hash
    import hashlib
    text_hash = hashlib.md5(text.encode()).hexdigest()
    
    # Convert hash to float values between -1 and 1
    embedding = []
    for i in range(0, len(text_hash), 2):
        hex_pair = text_hash[i:i+2]
        # Convert hex to float between -1 and 1
        value = (int(hex_pair, 16) / 255.0) * 2 - 1
        embedding.append(value)
    
    # Pad or truncate to 768 dimensions (Gemini embedding size)
    while len(embedding) < 768:
        embedding.extend(embedding[:min(len(embedding), 768 - len(embedding))])
    
    return embedding[:768]

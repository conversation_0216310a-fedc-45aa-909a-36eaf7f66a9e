"""
File System Mock Implementation
==============================

Provides mock implementations for file system operations
to eliminate external dependencies during testing.
"""

import os
import tempfile
from typing import Any, Dict, List, Optional, BinaryIO
from unittest.mock import MagicMock
from uuid import uuid4
from pathlib import Path


class MockFileManager:
    """Mock implementation of FileManager for testing."""
    
    def __init__(self):
        self._files = {}  # Store mock file data
        self._temp_dir = tempfile.mkdtemp()
    
    def save_uploaded_file(self, file: BinaryIO, filename: str, project_id: str = None) -> Dict[str, Any]:
        """Mock file upload and save operation."""
        file_id = str(uuid4())
        file_path = f"mock_uploads/{project_id or 'default'}/{file_id}_{filename}"
        
        # Read file content for mock storage
        content = file.read()
        file.seek(0)  # Reset file pointer
        
        self._files[file_id] = {
            "id": file_id,
            "filename": filename,
            "path": file_path,
            "size": len(content),
            "content": content,
            "project_id": project_id,
            "mime_type": self._guess_mime_type(filename)
        }
        
        return {
            "file_id": file_id,
            "filename": filename,
            "path": file_path,
            "size": len(content),
            "mime_type": self._guess_mime_type(filename)
        }
    
    def get_file_path(self, file_id: str) -> Optional[str]:
        """Mock getting file path by ID."""
        if file_id in self._files:
            return self._files[file_id]["path"]
        return None
    
    def get_file_content(self, file_id: str) -> Optional[bytes]:
        """Mock getting file content by ID."""
        if file_id in self._files:
            return self._files[file_id]["content"]
        return None
    
    def delete_file(self, file_id: str) -> bool:
        """Mock file deletion."""
        if file_id in self._files:
            del self._files[file_id]
            return True
        return False
    
    def file_exists(self, file_id: str) -> bool:
        """Mock file existence check."""
        return file_id in self._files
    
    def get_file_info(self, file_id: str) -> Optional[Dict[str, Any]]:
        """Mock getting file information."""
        return self._files.get(file_id)
    
    def list_files(self, project_id: str = None) -> List[Dict[str, Any]]:
        """Mock listing files."""
        files = []
        for file_data in self._files.values():
            if project_id is None or file_data.get("project_id") == project_id:
                files.append({
                    "id": file_data["id"],
                    "filename": file_data["filename"],
                    "path": file_data["path"],
                    "size": file_data["size"],
                    "mime_type": file_data["mime_type"],
                    "project_id": file_data.get("project_id")
                })
        return files
    
    def cleanup_temp_files(self):
        """Mock cleanup of temporary files."""
        self._files.clear()
    
    def _guess_mime_type(self, filename: str) -> str:
        """Mock MIME type guessing."""
        ext = Path(filename).suffix.lower()
        mime_types = {
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.json': 'application/json',
            '.csv': 'text/csv',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xls': 'application/vnd.ms-excel',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif'
        }
        return mime_types.get(ext, 'application/octet-stream')


class MockContentExtractor:
    """Mock implementation of ContentExtractor for testing."""
    
    def __init__(self):
        pass
    
    def extract_from_file(self, file_path: str, mime_type: str = None) -> Dict[str, Any]:
        """Mock content extraction from file."""
        filename = os.path.basename(file_path)
        
        # Mock extracted content based on file type
        if file_path.endswith('.pdf'):
            content = f"Mock PDF content extracted from {filename}"
        elif file_path.endswith('.docx'):
            content = f"Mock DOCX content extracted from {filename}"
        elif file_path.endswith('.txt'):
            content = f"Mock text content from {filename}"
        elif file_path.endswith('.md'):
            content = f"# Mock Markdown Content\n\nExtracted from {filename}"
        else:
            content = f"Mock content extracted from {filename}"
        
        return {
            "content": content,
            "metadata": {
                "filename": filename,
                "file_type": mime_type or "application/octet-stream",
                "extracted_at": "2024-01-01T00:00:00Z",
                "word_count": len(content.split()),
                "char_count": len(content)
            },
            "chunks": self._create_mock_chunks(content)
        }
    
    def extract_from_url(self, url: str) -> Dict[str, Any]:
        """Mock content extraction from URL."""
        content = f"Mock web content extracted from {url}"
        
        return {
            "content": content,
            "metadata": {
                "url": url,
                "title": f"Mock Page Title for {url}",
                "extracted_at": "2024-01-01T00:00:00Z",
                "word_count": len(content.split()),
                "char_count": len(content)
            },
            "chunks": self._create_mock_chunks(content)
        }
    
    def _create_mock_chunks(self, content: str, chunk_size: int = 500) -> List[Dict[str, Any]]:
        """Create mock content chunks."""
        chunks = []
        words = content.split()
        
        for i in range(0, len(words), chunk_size // 10):  # Approximate chunk by words
            chunk_words = words[i:i + chunk_size // 10]
            chunk_text = " ".join(chunk_words)
            
            chunks.append({
                "text": chunk_text,
                "chunk_index": len(chunks),
                "start_char": i * 10,  # Approximate
                "end_char": (i + len(chunk_words)) * 10,
                "word_count": len(chunk_words),
                "char_count": len(chunk_text)
            })
        
        return chunks


class MockUploadFile:
    """Mock implementation of FastAPI UploadFile for testing."""
    
    def __init__(self, filename: str, content: bytes, content_type: str = "application/octet-stream"):
        self.filename = filename
        self.content = content
        self.content_type = content_type
        self.size = len(content)
        self._position = 0
    
    async def read(self, size: int = -1) -> bytes:
        """Mock read operation."""
        if size == -1:
            result = self.content[self._position:]
            self._position = len(self.content)
        else:
            result = self.content[self._position:self._position + size]
            self._position += len(result)
        return result
    
    async def seek(self, position: int):
        """Mock seek operation."""
        self._position = max(0, min(position, len(self.content)))
    
    async def close(self):
        """Mock close operation."""
        pass
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


def create_mock_upload_file(filename: str, content: str, content_type: str = "text/plain") -> MockUploadFile:
    """Create a mock upload file for testing."""
    return MockUploadFile(filename, content.encode(), content_type)


def create_mock_pdf_file(filename: str = "test.pdf") -> MockUploadFile:
    """Create a mock PDF file for testing."""
    # Mock PDF content (not a real PDF, just for testing)
    content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
    return MockUploadFile(filename, content, "application/pdf")


def create_mock_text_file(filename: str = "test.txt", content: str = "Mock text content") -> MockUploadFile:
    """Create a mock text file for testing."""
    return MockUploadFile(filename, content.encode(), "text/plain")


def create_mock_docx_file(filename: str = "test.docx") -> MockUploadFile:
    """Create a mock DOCX file for testing."""
    # Mock DOCX content (not a real DOCX, just for testing)
    content = b"PK\x03\x04\x14\x00\x00\x00\x08\x00"  # ZIP header for DOCX
    return MockUploadFile(filename, content, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")

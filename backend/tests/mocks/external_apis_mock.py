"""
External APIs Mock Implementation
================================

Provides mock implementations for external API services
to eliminate external dependencies during testing.
"""

from typing import Any, Dict, List, Optional
from datetime import datetime, timezone
from unittest.mock import MagicMock
from uuid import uuid4


class MockGitHubConnector:
    """Mock implementation of GitHub connector."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.github = MagicMock()
        self.repo = MagicMock()
        
    def read_open_issues(self, since: datetime = None, until: datetime = None) -> List[Dict]:
        """Mock reading open issues from GitHub."""
        return [
            {
                "id": 1,
                "number": 123,
                "title": "Mock Open Issue 1",
                "body": "This is a mock open issue",
                "state": "open",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "labels": [{"name": "bug"}, {"name": "high-priority"}],
                "assignees": [{"login": "test-user"}],
                "user": {"login": "reporter-user"}
            },
            {
                "id": 2,
                "number": 124,
                "title": "Mock Open Issue 2",
                "body": "This is another mock open issue",
                "state": "open",
                "created_at": "2024-01-02T00:00:00Z",
                "updated_at": "2024-01-02T00:00:00Z",
                "labels": [{"name": "enhancement"}],
                "assignees": [],
                "user": {"login": "another-user"}
            }
        ]
    
    def read_closed_issues(self, since: datetime = None, until: datetime = None) -> List[Dict]:
        """Mock reading closed issues from GitHub."""
        return [
            {
                "id": 3,
                "number": 125,
                "title": "Mock Closed Issue 1",
                "body": "This is a mock closed issue",
                "state": "closed",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-03T00:00:00Z",
                "closed_at": "2024-01-03T00:00:00Z",
                "labels": [{"name": "bug"}, {"name": "resolved"}],
                "assignees": [{"login": "test-user"}],
                "user": {"login": "reporter-user"}
            }
        ]
    
    def sync_issues(self, start_time: datetime, end_time: datetime, incident_type: str = "both") -> List[Dict]:
        """Mock syncing issues from GitHub."""
        synced_issues = []
        if incident_type in ("open", "both"):
            synced_issues.extend(self.read_open_issues(start_time, end_time))
        if incident_type in ("closed", "both"):
            synced_issues.extend(self.read_closed_issues(start_time, end_time))
        return synced_issues
    
    def import_issues(self, start_time: datetime, end_time: datetime, incident_type: str) -> List[Dict]:
        """Mock importing issues from GitHub."""
        return self.sync_issues(start_time, end_time, incident_type)
    
    def create_issue(self, title: str, body: str, labels: List[str] = None) -> Dict:
        """Mock creating an issue in GitHub."""
        return {
            "id": 999,
            "number": 999,
            "title": title,
            "body": body,
            "state": "open",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "labels": [{"name": label} for label in (labels or [])],
            "user": {"login": "test-user"}
        }


class MockJiraConnector:
    """Mock implementation of Jira connector."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.jira = MagicMock()
    
    def read_issues(self, project_key: str, start_date: datetime = None, end_date: datetime = None) -> List[Dict]:
        """Mock reading issues from Jira."""
        return [
            {
                "id": "10001",
                "key": f"{project_key}-1",
                "fields": {
                    "summary": "Mock Jira Issue 1",
                    "description": "This is a mock Jira issue",
                    "status": {"name": "Open"},
                    "priority": {"name": "High"},
                    "issuetype": {"name": "Bug"},
                    "created": "2024-01-01T00:00:00.000+0000",
                    "updated": "2024-01-01T00:00:00.000+0000",
                    "assignee": {"displayName": "Test User"},
                    "reporter": {"displayName": "Reporter User"}
                }
            },
            {
                "id": "10002",
                "key": f"{project_key}-2",
                "fields": {
                    "summary": "Mock Jira Issue 2",
                    "description": "This is another mock Jira issue",
                    "status": {"name": "In Progress"},
                    "priority": {"name": "Medium"},
                    "issuetype": {"name": "Task"},
                    "created": "2024-01-02T00:00:00.000+0000",
                    "updated": "2024-01-02T00:00:00.000+0000",
                    "assignee": {"displayName": "Another User"},
                    "reporter": {"displayName": "Reporter User"}
                }
            }
        ]
    
    def sync_issues(self, project_key: str, start_time: datetime, end_time: datetime) -> List[Dict]:
        """Mock syncing issues from Jira."""
        return self.read_issues(project_key, start_time, end_time)
    
    def create_issue(self, project_key: str, summary: str, description: str, issue_type: str = "Bug") -> Dict:
        """Mock creating an issue in Jira."""
        return {
            "id": "10999",
            "key": f"{project_key}-999",
            "fields": {
                "summary": summary,
                "description": description,
                "status": {"name": "Open"},
                "priority": {"name": "Medium"},
                "issuetype": {"name": issue_type},
                "created": datetime.now(timezone.utc).isoformat(),
                "assignee": None,
                "reporter": {"displayName": "Test User"}
            }
        }


class MockServiceNowConnector:
    """Mock implementation of ServiceNow connector."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.client = MagicMock()
    
    def read_incidents(self, start_date: datetime = None, end_date: datetime = None) -> List[Dict]:
        """Mock reading incidents from ServiceNow."""
        return [
            {
                "sys_id": "abc123",
                "number": "INC0000001",
                "short_description": "Mock ServiceNow Incident 1",
                "description": "This is a mock ServiceNow incident",
                "state": "2",  # In Progress
                "priority": "2",  # High
                "category": "Software",
                "sys_created_on": "2024-01-01 00:00:00",
                "sys_updated_on": "2024-01-01 00:00:00",
                "assigned_to": {"display_value": "Test User"},
                "caller_id": {"display_value": "Reporter User"}
            },
            {
                "sys_id": "def456",
                "number": "INC0000002",
                "short_description": "Mock ServiceNow Incident 2",
                "description": "This is another mock ServiceNow incident",
                "state": "1",  # New
                "priority": "3",  # Medium
                "category": "Hardware",
                "sys_created_on": "2024-01-02 00:00:00",
                "sys_updated_on": "2024-01-02 00:00:00",
                "assigned_to": {"display_value": "Another User"},
                "caller_id": {"display_value": "Reporter User"}
            }
        ]
    
    def sync_incidents(self, start_time: datetime, end_time: datetime) -> List[Dict]:
        """Mock syncing incidents from ServiceNow."""
        return self.read_incidents(start_time, end_time)
    
    def create_incident(self, short_description: str, description: str, priority: str = "3") -> Dict:
        """Mock creating an incident in ServiceNow."""
        return {
            "sys_id": "xyz999",
            "number": "INC0000999",
            "short_description": short_description,
            "description": description,
            "state": "1",  # New
            "priority": priority,
            "category": "Software",
            "sys_created_on": datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S"),
            "assigned_to": None,
            "caller_id": {"display_value": "Test User"}
        }


class MockGoogleAI:
    """Mock implementation of Google AI services."""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or "mock-api-key"
    
    def generate_embedding(self, text: str, model: str = "text-embedding-004") -> List[float]:
        """Mock embedding generation."""
        # Generate a deterministic fake embedding based on text hash
        import hashlib
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        # Convert hash to float values between -1 and 1
        embedding = []
        for i in range(0, len(text_hash), 2):
            hex_pair = text_hash[i:i+2]
            # Convert hex to float between -1 and 1
            value = (int(hex_pair, 16) / 255.0) * 2 - 1
            embedding.append(value)
        
        # Pad or truncate to 768 dimensions (Gemini embedding size)
        while len(embedding) < 768:
            embedding.extend(embedding[:min(len(embedding), 768 - len(embedding))])
        
        return embedding[:768]
    
    def generate_text(self, prompt: str, model: str = "gemini-pro") -> str:
        """Mock text generation."""
        return f"Mock AI response to: {prompt[:50]}..."
    
    def analyze_incident(self, incident_data: Dict[str, Any]) -> Dict[str, Any]:
        """Mock incident analysis."""
        return {
            "root_cause": "Mock root cause analysis",
            "immediate_action": "Mock immediate action recommendation",
            "impact_forecast": "Mock impact forecast",
            "cascading_risks": ["Mock risk 1", "Mock risk 2"],
            "confidence_score": 0.85
        }

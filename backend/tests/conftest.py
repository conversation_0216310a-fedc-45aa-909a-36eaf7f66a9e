import os
import sys
from uuid import uuid4

import pytest
from dotenv import load_dotenv
from sqlalchemy import MetaData, create_engine
from sqlalchemy.orm import sessionmaker

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
env_path = os.path.join(ROOT_DIR, ".env.dev")
load_dotenv(env_path)
DATABASE_URL = "sqlite:///./test.db"
os.environ["DATABASE_URL"] = DATABASE_URL

sys.path.append(os.path.join(ROOT_DIR, "app"))
from database.core import Base
from entities.user import User
from routes.auth.models import TokenData
from routes.auth.service import get_password_hash
from utils.rate_limiter import limiter


@pytest.fixture(scope="session", autouse=True)
def cleanup_db():
    """Cleanup the test database file after all tests are done."""
    yield  # Run all tests
    # Get the database file path from the URL
    DB_FILE = DATABASE_URL.replace("sqlite:///", "")
    # After all tests, remove the database file if it exists
    if os.path.exists(DB_FILE) and "test.db" in DB_FILE:  # Safety check
        os.remove(DB_FILE)
        print(f"Removed test database: {DB_FILE}")


@pytest.fixture(scope="function")
def db_session():
    # Use a unique database URL for testing
    engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # Create all tables except incident_metrics
    metadata = MetaData()
    for table in Base.metadata.sorted_tables:
        if table.name != "incident_metrics":
            table.tometadata(metadata)

    metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_user():
    password_hash = get_password_hash("password123")
    return User(
        id=uuid4(),
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        password=password_hash,
    )


@pytest.fixture(scope="function")
def test_token_data():
    return TokenData(user_id=str(uuid4()))


@pytest.fixture(scope="function")
def client(db_session):
    from app.database.core import get_db
    from app.main import app

    # Disable rate limiting for tests
    limiter.reset()

    def override_get_db():
        try:
            yield db_session
        finally:
            db_session.close()

    app.dependency_overrides[get_db] = override_get_db

    from fastapi.testclient import TestClient

    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def auth_headers(client, db_session):
    # Register a test user
    response = client.post(
        "/auth/",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
        },
    )
    assert response.status_code == 201

    # Login to get access token
    response = client.post(
        "/auth/token",
        data={
            "username": "<EMAIL>",
            "password": "testpassword123",
            "grant_type": "password",
        },
    )
    assert response.status_code == 200
    token = response.json()["access_token"]

    return {"Authorization": f"Bearer {token}"}

import os
import sys
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from dotenv import load_dotenv
from sqlalchemy import MetaData, create_engine
from sqlalchemy.orm import sessionmaker

# Load test environment configuration
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
test_env_path = os.path.join(ROOT_DIR, ".env.test")
load_dotenv(test_env_path)

# Override DATABASE_URL for tests
DATABASE_URL = "sqlite:///./test.db"
os.environ["DATABASE_URL"] = DATABASE_URL

# Set test environment flag
os.environ["TESTING"] = "true"
os.environ["MOCK_EXTERNAL_SERVICES"] = "true"

sys.path.append(os.path.join(ROOT_DIR, "app"))
from database.core import Base
from entities.user import User
from routes.auth.models import TokenData
from routes.auth.service import get_password_hash
from utils.rate_limiter import limiter

# Import mocks
from tests.mocks import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ckContentExtractor,
    MockFileManager,
    MockGitHubConnector,
    MockGoogleAI,
    MockJiraConnector,
    MockQdrantConnector,
    MockRedis,
    MockServiceNowConnector,
    MockVectorSearchService,
    mock_generate_embedding,
)


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment with all necessary mocks."""
    # Patch external services at the session level
    with patch("vector_db.qdrant_connector.QdrantConnector", MockQdrantConnector), \
         patch("vector_db.search_service.VectorSearchService", MockVectorSearchService), \
         patch("utils.celery_worker.celery_app", MockCeleryApp()), \
         patch("vector_db.embeddings.generate_embedding", mock_generate_embedding), \
         patch("redis.Redis", MockRedis), \
         patch("redis.from_url", lambda url, **kwargs: MockRedis()):

        yield  # Run all tests

    # Cleanup after all tests
    cleanup_test_files()


@pytest.fixture(scope="session", autouse=True)
def cleanup_db():
    """Cleanup the test database file after all tests are done."""
    yield  # Run all tests
    # Get the database file path from the URL
    DB_FILE = DATABASE_URL.replace("sqlite:///", "")
    # After all tests, remove the database file if it exists
    if os.path.exists(DB_FILE) and "test.db" in DB_FILE:  # Safety check
        os.remove(DB_FILE)
        print(f"Removed test database: {DB_FILE}")


def cleanup_test_files():
    """Cleanup test files and directories."""
    import shutil
    test_dirs = ["./test_uploads", "./test_static"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"Removed test directory: {test_dir}")


@pytest.fixture(scope="function")
def db_session():
    # Use a unique database URL for testing
    engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # Create all tables except incident_metrics
    metadata = MetaData()
    for table in Base.metadata.sorted_tables:
        if table.name != "incident_metrics":
            table.tometadata(metadata)

    metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_user():
    password_hash = get_password_hash("password123")
    return User(
        id=uuid4(),
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        password=password_hash,
    )


@pytest.fixture(scope="function")
def test_token_data():
    return TokenData(user_id=str(uuid4()))


@pytest.fixture(scope="function")
def client(db_session):
    from app.database.core import get_db
    from app.main import app

    # Disable rate limiting for tests
    limiter.reset()

    def override_get_db():
        try:
            yield db_session
        finally:
            db_session.close()

    app.dependency_overrides[get_db] = override_get_db

    from fastapi.testclient import TestClient

    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def auth_headers(client, db_session):
    # Register a test user
    response = client.post(
        "/auth/",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
        },
    )
    assert response.status_code == 201

    # Login to get access token
    response = client.post(
        "/auth/token",
        data={
            "username": "<EMAIL>",
            "password": "testpassword123",
            "grant_type": "password",
        },
    )
    assert response.status_code == 200
    token = response.json()["access_token"]

    return {"Authorization": f"Bearer {token}"}


# Mock Service Fixtures
@pytest.fixture
def mock_vector_search_service():
    """Provide a mock vector search service."""
    return MockVectorSearchService()


@pytest.fixture
def mock_qdrant_connector():
    """Provide a mock Qdrant connector."""
    from vector_db.base_connector import CollectionType
    return MockQdrantConnector({
        CollectionType.INCIDENTS: "test_incidents",
        CollectionType.DOCUMENTS: "test_documents"
    })


@pytest.fixture
def mock_celery_app():
    """Provide a mock Celery app."""
    return MockCeleryApp()


@pytest.fixture
def mock_redis():
    """Provide a mock Redis client."""
    return MockRedis()


@pytest.fixture
def mock_github_connector():
    """Provide a mock GitHub connector."""
    return MockGitHubConnector({"token": "test-token"})


@pytest.fixture
def mock_google_ai():
    """Provide a mock Google AI service."""
    return MockGoogleAI("test-api-key")


@pytest.fixture
def mock_jira_connector():
    """Provide a mock Jira connector."""
    return MockJiraConnector({
        "url": "https://test.atlassian.net",
        "username": "<EMAIL>",
        "api_token": "test-token"
    })


@pytest.fixture
def mock_servicenow_connector():
    """Provide a mock ServiceNow connector."""
    return MockServiceNowConnector({
        "instance": "test-instance",
        "username": "test-user",
        "password": "test-password"
    })


@pytest.fixture
def mock_file_manager():
    """Provide a mock file manager."""
    return MockFileManager()


@pytest.fixture
def mock_content_extractor():
    """Provide a mock content extractor."""
    return MockContentExtractor()


# Patch fixtures for specific modules
@pytest.fixture
def patch_vector_db_operations(mock_vector_search_service, mock_qdrant_connector):
    """Patch vector database operations."""
    with patch("vector_db.search_service.VectorSearchService", return_value=mock_vector_search_service), \
         patch("vector_db.qdrant_connector.QdrantConnector", return_value=mock_qdrant_connector), \
         patch("vector_db.embeddings.generate_embedding", mock_generate_embedding):
        yield


@pytest.fixture
def patch_celery_tasks(mock_celery_app):
    """Patch Celery tasks."""
    with patch("utils.celery_worker.celery_app", mock_celery_app), \
         patch("tasks.vector_db.celery_app", mock_celery_app), \
         patch("tasks.github.celery_app", mock_celery_app), \
         patch("tasks.jira.celery_app", mock_celery_app), \
         patch("tasks.service_now.celery_app", mock_celery_app):
        yield


@pytest.fixture
def patch_external_apis(mock_github_connector, mock_google_ai, mock_jira_connector, mock_servicenow_connector):
    """Patch external API connectors."""
    with patch("connectors.github.github_connector.GitHubConnector", return_value=mock_github_connector), \
         patch("connectors.jira.jira_connector.JiraConnector", return_value=mock_jira_connector), \
         patch("connectors.servicenow.servicenow_connector.ServiceNowConnector", return_value=mock_servicenow_connector):
        yield


@pytest.fixture
def patch_file_operations(mock_file_manager, mock_content_extractor):
    """Patch file operations."""
    with patch("services.file_manager.FileManager", return_value=mock_file_manager), \
         patch("services.content_extractor.ContentExtractor", return_value=mock_content_extractor):
        yield


@pytest.fixture
def patch_all_external_services(
    patch_vector_db_operations,
    patch_celery_tasks,
    patch_external_apis,
    patch_file_operations,
    mock_redis
):
    """Patch all external services for comprehensive testing."""
    with patch("redis.Redis", return_value=mock_redis), \
         patch("redis.from_url", return_value=mock_redis):
        yield

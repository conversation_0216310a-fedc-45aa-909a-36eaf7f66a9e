import os
import sys
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from dotenv import load_dotenv
from sqlalchemy import MetaData, create_engine
from sqlalchemy.orm import sessionmaker

# Load test environment configuration
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
test_env_path = os.path.join(ROOT_DIR, ".env.test")
load_dotenv(test_env_path)

# Override DATABASE_URL for tests
DATABASE_URL = "sqlite:///./test.db"
os.environ["DATABASE_URL"] = DATABASE_URL

# Set test environment flag
os.environ["TESTING"] = "true"
os.environ["MOCK_EXTERNAL_SERVICES"] = "true"

# Early patching to prevent external service connections during import
import unittest.mock

unittest.mock.patch("qdrant_client.QdrantClient").start()
unittest.mock.patch("redis.Redis").start()

sys.path.append(os.path.join(ROOT_DIR, "app"))
from database.core import Base
from entities.user import User
from routes.auth.models import TokenData
from routes.auth.service import get_password_hash
from utils.rate_limiter import limiter

# Import mocks
try:
    from tests.mocks import (
        MockCeleryApp,
        MockQdrantConnector,
        MockRedis,
        MockVectorSearchService,
        mock_generate_embedding,
    )
except ImportError as e:
    print(f"Warning: Could not import mocks: {e}")
    # Create minimal mocks as fallback
    from unittest.mock import MagicMock
    MockQdrantConnector = MagicMock
    MockVectorSearchService = MagicMock
    MockCeleryApp = MagicMock
    MockRedis = MagicMock
    mock_generate_embedding = lambda text: [0.1] * 768


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment with all necessary mocks."""
    # Patch external services at the session level
    with patch("vector_db.qdrant_connector.QdrantConnector", MockQdrantConnector), \
         patch("vector_db.search_service.VectorSearchService", MockVectorSearchService), \
         patch("utils.celery_worker.celery_app", MockCeleryApp()), \
         patch("vector_db.embeddings.generate_embedding", mock_generate_embedding), \
         patch("redis.Redis", MockRedis), \
         patch("redis.from_url", lambda url, **kwargs: MockRedis()):

        yield  # Run all tests

    # Cleanup after all tests
    cleanup_test_files()


@pytest.fixture(scope="session", autouse=True)
def cleanup_db():
    """Cleanup the test database file after all tests are done."""
    yield  # Run all tests
    # Get the database file path from the URL
    DB_FILE = DATABASE_URL.replace("sqlite:///", "")
    # After all tests, remove the database file if it exists
    if os.path.exists(DB_FILE) and "test.db" in DB_FILE:  # Safety check
        os.remove(DB_FILE)
        print(f"Removed test database: {DB_FILE}")


def cleanup_test_files():
    """Cleanup test files and directories."""
    import shutil
    test_dirs = ["./test_uploads", "./test_static"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"Removed test directory: {test_dir}")


@pytest.fixture(scope="function")
def db_session():
    # Use a unique database URL for testing
    engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # Create all tables except incident_metrics
    metadata = MetaData()
    for table in Base.metadata.sorted_tables:
        if table.name != "incident_metrics":
            table.tometadata(metadata)

    metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_user():
    password_hash = get_password_hash("password123")
    return User(
        id=uuid4(),
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        password=password_hash,
    )


@pytest.fixture(scope="function")
def test_token_data():
    return TokenData(user_id=str(uuid4()))


@pytest.fixture(scope="function")
def client(db_session):
    from app.database.core import get_db
    from app.main import app

    # Disable rate limiting for tests
    limiter.reset()

    def override_get_db():
        try:
            yield db_session
        finally:
            db_session.close()

    app.dependency_overrides[get_db] = override_get_db

    from fastapi.testclient import TestClient

    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def auth_headers(client, db_session):
    # Register a test user
    response = client.post(
        "/auth/",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
        },
    )
    assert response.status_code == 201

    # Login to get access token
    response = client.post(
        "/auth/token",
        data={
            "username": "<EMAIL>",
            "password": "testpassword123",
            "grant_type": "password",
        },
    )
    assert response.status_code == 200
    token = response.json()["access_token"]

    return {"Authorization": f"Bearer {token}"}


# Mock Service Fixtures
@pytest.fixture
def mock_vector_search_service():
    """Provide a mock vector search service."""
    return MockVectorSearchService()


@pytest.fixture
def mock_qdrant_connector():
    """Provide a mock Qdrant connector."""
    collections = {"INCIDENTS": "test_incidents", "DOCUMENTS": "test_documents"}
    return MockQdrantConnector(collections)


@pytest.fixture
def mock_celery_app():
    """Provide a mock Celery app."""
    return MockCeleryApp()


@pytest.fixture
def mock_redis():
    """Provide a mock Redis client."""
    return MockRedis()


# Simple mock for file operations
@pytest.fixture
def mock_file_manager():
    """Provide a mock file manager."""
    from unittest.mock import MagicMock
    mock = MagicMock()
    mock.save_uploaded_file.return_value = {
        "file_id": "test-file-id",
        "filename": "test.txt",
        "size": 100,
        "mime_type": "text/plain"
    }
    mock.get_file_info.return_value = {
        "id": "test-file-id",
        "filename": "test.txt",
        "size": 100
    }
    return mock





@pytest.fixture
def patch_all_external_services(mock_redis):
    """Patch all external services for comprehensive testing."""
    patches = [
        # Redis patches
        patch("redis.Redis", return_value=mock_redis),
        patch("redis.from_url", return_value=mock_redis),

        # Vector DB patches
        patch("vector_db.qdrant_connector.QdrantClient", return_value=MagicMock()),
        patch("vector_db.embeddings.generate_embedding", return_value=[0.1] * 768),

        # Celery patches
        patch("utils.celery_worker.celery_app.delay", return_value=MagicMock()),
        patch("celery.Celery", return_value=MagicMock()),

        # External API patches
        patch("github.Github", return_value=MagicMock()),
        patch("requests.get", return_value=MagicMock()),
        patch("requests.post", return_value=MagicMock()),
    ]

    # Start all patches
    for p in patches:
        p.start()

    yield

    # Stop all patches
    for p in patches:
        p.stop()

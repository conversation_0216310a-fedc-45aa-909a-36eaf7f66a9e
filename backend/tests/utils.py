"""
Test Utilities
==============

Common utilities and helpers for testing the incident management backend.
"""

import json
import tempfile
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import uuid4

from fastapi.testclient import TestClient


class TestDataFactory:
    """Factory for creating test data objects."""
    
    @staticmethod
    def create_user_data(
        email: str = None,
        first_name: str = "Test",
        last_name: str = "User",
        password: str = "testpassword123"
    ) -> Dict[str, Any]:
        """Create test user data."""
        return {
            "email": email or f"test.{uuid4().hex[:8]}@example.com",
            "first_name": first_name,
            "last_name": last_name,
            "password": password
        }
    
    @staticmethod
    def create_incident_data(
        title: str = None,
        summary: str = None,
        priority: str = "p2",
        severity: str = "medium",
        incident_type: str = "outage",
        affected_services: List[str] = None,
        tags: List[str] = None
    ) -> Dict[str, Any]:
        """Create test incident data."""
        return {
            "title": title or f"Test Incident {uuid4().hex[:8]}",
            "summary": summary or "This is a test incident for testing purposes",
            "priority": priority,
            "severity": severity,
            "incident_type": incident_type,
            "affected_services": affected_services or ["Test Service"],
            "tags": tags or ["test", "automated"],
            "incident_details": "Test incident details with expected behavior and possible causes"
        }
    
    @staticmethod
    def create_project_data(
        name: str = None,
        description: str = None
    ) -> Dict[str, Any]:
        """Create test project data."""
        return {
            "name": name or f"Test Project {uuid4().hex[:8]}",
            "description": description or "This is a test project for testing purposes"
        }
    
    @staticmethod
    def create_knowledge_base_data(
        name: str = None,
        description: str = None,
        kb_type: str = "GENERAL"
    ) -> Dict[str, Any]:
        """Create test knowledge base data."""
        return {
            "name": name or f"Test KB {uuid4().hex[:8]}",
            "description": description or "This is a test knowledge base",
            "kb_type": kb_type
        }
    
    @staticmethod
    def create_document_data(
        title: str = None,
        content: str = None,
        document_type: str = "TEXT",
        url: str = None
    ) -> Dict[str, Any]:
        """Create test document data."""
        return {
            "title": title or f"Test Document {uuid4().hex[:8]}",
            "content": content or "This is test document content for testing purposes",
            "document_type": document_type,
            "url": url,
            "metadata": {
                "created_at": datetime.now(timezone.utc).isoformat(),
                "test_document": True
            }
        }


class AuthHelper:
    """Helper for authentication in tests."""
    
    @staticmethod
    def register_and_login(client: TestClient, user_data: Dict[str, Any] = None) -> Dict[str, str]:
        """Register a user and return authentication headers."""
        if user_data is None:
            user_data = TestDataFactory.create_user_data()
        
        # Register user
        register_response = client.post("/auth/register", json=user_data)
        assert register_response.status_code == 201, f"Registration failed: {register_response.text}"
        
        # Login to get token
        login_response = client.post(
            "/auth/token",
            data={
                "username": user_data["email"],
                "password": user_data["password"],
                "grant_type": "password"
            }
        )
        assert login_response.status_code == 200, f"Login failed: {login_response.text}"
        
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    @staticmethod
    def create_auth_headers(token: str) -> Dict[str, str]:
        """Create authorization headers from token."""
        return {"Authorization": f"Bearer {token}"}


class APITestHelper:
    """Helper for API testing."""
    
    @staticmethod
    def assert_response_success(response, expected_status: int = 200):
        """Assert that a response is successful."""
        assert response.status_code == expected_status, (
            f"Expected status {expected_status}, got {response.status_code}. "
            f"Response: {response.text}"
        )
    
    @staticmethod
    def assert_response_error(response, expected_status: int = 400):
        """Assert that a response is an error."""
        assert response.status_code == expected_status, (
            f"Expected error status {expected_status}, got {response.status_code}. "
            f"Response: {response.text}"
        )
    
    @staticmethod
    def assert_json_contains(response_json: Dict[str, Any], expected_fields: List[str]):
        """Assert that JSON response contains expected fields."""
        for field in expected_fields:
            assert field in response_json, f"Expected field '{field}' not found in response"
    
    @staticmethod
    def assert_pagination_response(response_json: Dict[str, Any]):
        """Assert that response has proper pagination structure."""
        required_fields = ["items", "total", "page", "limit", "pages"]
        APITestHelper.assert_json_contains(response_json, required_fields)
        
        assert isinstance(response_json["items"], list), "Items should be a list"
        assert isinstance(response_json["total"], int), "Total should be an integer"
        assert isinstance(response_json["page"], int), "Page should be an integer"
        assert isinstance(response_json["limit"], int), "Limit should be an integer"
        assert isinstance(response_json["pages"], int), "Pages should be an integer"


class FileTestHelper:
    """Helper for file testing."""
    
    @staticmethod
    def create_temp_file(content: str, filename: str = None, suffix: str = ".txt") -> str:
        """Create a temporary file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as f:
            f.write(content)
            return f.name
    
    @staticmethod
    def create_mock_upload_file(filename: str, content: bytes, content_type: str = "text/plain"):
        """Create a mock upload file for testing."""
        from tests.mocks.file_system_mock import MockUploadFile
        return MockUploadFile(filename, content, content_type)


class DatabaseTestHelper:
    """Helper for database testing."""
    
    @staticmethod
    def create_test_user(db_session, user_data: Dict[str, Any] = None):
        """Create a test user in the database."""
        from entities.user import User
        from routes.auth.service import get_password_hash
        
        if user_data is None:
            user_data = TestDataFactory.create_user_data()
        
        user = User(
            id=uuid4(),
            email=user_data["email"],
            first_name=user_data["first_name"],
            last_name=user_data["last_name"],
            password=get_password_hash(user_data["password"])
        )
        
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user
    
    @staticmethod
    def create_test_incident(db_session, incident_data: Dict[str, Any] = None, user_id: str = None):
        """Create a test incident in the database."""
        from entities.incident import Incident
        
        if incident_data is None:
            incident_data = TestDataFactory.create_incident_data()
        
        if user_id is None:
            user = DatabaseTestHelper.create_test_user(db_session)
            user_id = user.id
        
        incident = Incident(
            id=uuid4(),
            title=incident_data["title"],
            summary=incident_data.get("summary"),
            priority=incident_data["priority"],
            severity=incident_data["severity"],
            incident_type=incident_data["incident_type"],
            reporter_id=user_id,
            incident_number=f"INC-{uuid4().hex[:8].upper()}"
        )
        
        db_session.add(incident)
        db_session.commit()
        db_session.refresh(incident)
        return incident


class MockDataHelper:
    """Helper for working with mock data."""
    
    @staticmethod
    def create_mock_vector_search_results(count: int = 3) -> List[Dict[str, Any]]:
        """Create mock vector search results."""
        results = []
        for i in range(count):
            results.append({
                "id": str(uuid4()),
                "score": 0.9 - (i * 0.1),  # Decreasing similarity scores
                "payload": {
                    "title": f"Mock Result {i + 1}",
                    "content": f"Mock content for result {i + 1}",
                    "type": "incident" if i % 2 == 0 else "document"
                }
            })
        return results
    
    @staticmethod
    def create_mock_celery_task_result(task_id: str = None, status: str = "SUCCESS", result: Any = None):
        """Create mock Celery task result."""
        from tests.mocks.celery_mock import MockAsyncResult
        return MockAsyncResult(
            task_id or str(uuid4()),
            result or {"status": "completed"},
            status
        )


def assert_dict_subset(subset: Dict[str, Any], full_dict: Dict[str, Any]):
    """Assert that subset is contained in full_dict."""
    for key, value in subset.items():
        assert key in full_dict, f"Key '{key}' not found in dictionary"
        assert full_dict[key] == value, f"Expected {key}={value}, got {full_dict[key]}"


def load_test_json(filename: str) -> Dict[str, Any]:
    """Load JSON test data from file."""
    import os
    test_data_dir = os.path.join(os.path.dirname(__file__), "data")
    file_path = os.path.join(test_data_dir, filename)
    
    with open(file_path, 'r') as f:
        return json.load(f)


def create_test_environment_variables() -> Dict[str, str]:
    """Create test environment variables."""
    return {
        "TESTING": "true",
        "MOCK_EXTERNAL_SERVICES": "true",
        "SECRET_KEY": "test-secret-key-for-jwt-tokens-in-testing-environment-only",
        "DATABASE_URL": "sqlite:///./test.db",
        "CELERY_TASK_ALWAYS_EAGER": "true",
        "CELERY_TASK_EAGER_PROPAGATES": "true",
        "QDRANT_URL": "http://localhost:6333",
        "GEMINI_API_KEY": "test-gemini-api-key",
        "GITHUB_ACCESS_TOKEN": "test-github-token"
    }

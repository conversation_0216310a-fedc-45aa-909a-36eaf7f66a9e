"""Test utilities for the incident management backend."""

from typing import Any, Dict, List
from uuid import uuid4

from fastapi.testclient import TestClient


class TestDataFactory:
    """Factory for creating test data objects."""
    
    @staticmethod
    def create_user_data(
        email: str = None,
        first_name: str = "Test",
        last_name: str = "User",
        password: str = "testpassword123"
    ) -> Dict[str, Any]:
        """Create test user data."""
        return {
            "email": email or f"test.{uuid4().hex[:8]}@example.com",
            "first_name": first_name,
            "last_name": last_name,
            "password": password
        }
    
    @staticmethod
    def create_incident_data(
        title: str = None,
        summary: str = None,
        priority: str = "p2",
        severity: str = "medium",
        incident_type: str = "outage",
        affected_services: List[str] = None,
        tags: List[str] = None
    ) -> Dict[str, Any]:
        """Create test incident data."""
        return {
            "title": title or f"Test Incident {uuid4().hex[:8]}",
            "summary": summary or "This is a test incident for testing purposes",
            "priority": priority,
            "severity": severity,
            "incident_type": incident_type,
            "affected_services": affected_services or ["Test Service"],
            "tags": tags or ["test", "automated"],
            "incident_details": "Test incident details with expected behavior and possible causes"
        }
    



class AuthHelper:
    """Helper for authentication in tests."""

    @staticmethod
    def register_and_login(client: TestClient, user_data: Dict[str, Any] = None) -> Dict[str, str]:
        """Register a user and return authentication headers."""
        if user_data is None:
            user_data = TestDataFactory.create_user_data()

        # Register user
        register_response = client.post("/auth/register", json=user_data)
        assert register_response.status_code == 201, f"Registration failed: {register_response.text}"

        # Login to get token
        login_response = client.post(
            "/auth/token",
            data={
                "username": user_data["email"],
                "password": user_data["password"],
                "grant_type": "password"
            }
        )
        assert login_response.status_code == 200, f"Login failed: {login_response.text}"

        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}


class APITestHelper:
    """Helper for API testing."""

    @staticmethod
    def assert_response_success(response, expected_status: int = 200):
        """Assert that a response is successful."""
        assert response.status_code == expected_status, (
            f"Expected status {expected_status}, got {response.status_code}. "
            f"Response: {response.text}"
        )

    @staticmethod
    def assert_response_error(response, expected_status: int = 400):
        """Assert that a response is an error."""
        assert response.status_code == expected_status, (
            f"Expected error status {expected_status}, got {response.status_code}. "
            f"Response: {response.text}"
        )

    @staticmethod
    def assert_json_contains(response_json: Dict[str, Any], expected_fields: List[str]):
        """Assert that JSON response contains expected fields."""
        for field in expected_fields:
            assert field in response_json, f"Expected field '{field}' not found in response"

    @staticmethod
    def assert_pagination_response(response_json: Dict[str, Any]):
        """Assert that response has proper pagination structure."""
        required_fields = ["items", "total", "page", "limit", "pages"]
        APITestHelper.assert_json_contains(response_json, required_fields)

        assert isinstance(response_json["items"], list), "Items should be a list"
        assert isinstance(response_json["total"], int), "Total should be an integer"
        assert isinstance(response_json["page"], int), "Page should be an integer"
        assert isinstance(response_json["limit"], int), "Limit should be an integer"
        assert isinstance(response_json["pages"], int), "Pages should be an integer"


# Minimal helpers for compatibility
class DatabaseTestHelper:
    pass


class MockDataHelper:
    pass

# Test Environment Configuration
# This file contains environment variables specifically for testing

# Database Configuration (SQLite for tests)
DATABASE_URL=sqlite:///./test.db

# JWT Configuration
SECRET_KEY=test-secret-key-for-jwt-tokens-in-testing-environment-only
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Celery Configuration (Disabled for tests)
CELERY_BROKER_URL=memory://
CELERY_RESULT_BACKEND=cache+memory://
CELERY_TASK_ALWAYS_EAGER=true
CELERY_TASK_EAGER_PROPAGATES=true

# Vector Database Configuration (Mocked)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=test-api-key
QDRANT_COLLECTION_INCIDENTS=test_incidents
QDRANT_COLLECTION_DOCUMENTS=test_documents

# External API Keys (Mocked)
GEMINI_API_KEY=test-gemini-api-key
GOOGLE_GENAI_USE_VERTEXAI=false

# GitHub Configuration (Mocked)
GITHUB_ACCESS_TOKEN=test-github-token
GITHUB_APP_ID=test-app-id
GITHUB_APP_CLIENT_ID=test-client-id
GITHUB_APP_CLIENT_SECRET=test-client-secret
GITHUB_APP_PRIVATE_KEY=test-private-key

# Jira Configuration (Mocked)
JIRA_URL=https://test.atlassian.net
JIRA_USERNAME=<EMAIL>
JIRA_API_TOKEN=test-jira-token

# ServiceNow Configuration (Mocked)
SERVICENOW_INSTANCE=test-instance
SERVICENOW_USERNAME=test-user
SERVICENOW_PASSWORD=test-password

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FORMAT=json

# Rate Limiting (Disabled for tests)
RATE_LIMIT_ENABLED=false

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./test_uploads

# Test-specific flags
TESTING=true
MOCK_EXTERNAL_SERVICES=true

from google.adk.tools.agent_tool import Agent<PERSON><PERSON>

from .code_executor_agent.agent import root_agent as code_executor_agent
from .google_search_agent.agent import root_agent as google_search_agent
from .memory_recall_agent.agent import root_agent as memory_recall_agent

# Agents with Inbuilt Tools can only be used as AgentTools
code_executor_tool = AgentTool(agent=code_executor_agent, skip_summarization=False)
google_search_tool = AgentTool(agent=google_search_agent, skip_summarization=False)
memory_recall_tool = AgentTool(agent=memory_recall_agent, skip_summarization=False)

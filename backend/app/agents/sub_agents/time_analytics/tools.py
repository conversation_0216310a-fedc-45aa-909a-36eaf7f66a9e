from datetime import datetime


def get_current_time(**kwargs: dict):
    """Gets the current time.
    Use this tool when the user asks for the current time.

    Args:
        kwargs: The keyword arguments passed to the function.

    Returns:
        dict: A dictionary containing:
            - "status": "success" or "error"
            - If success: "message" containing the current time
            - If error: "error_message" explaining what went wrong
    """

    return {"status": "success", "message": f"Current time is {datetime.now()}"}

from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

from . import prompt, tools

# AGENT_MODEL = "ollama/qwen3:4b"
AGENT_MODEL = "gemini/gemini-2.0-flash"

time_agent = Agent(
    name="time_agent",
    model=LiteLlm(AGENT_MODEL),
    description="The time agent is responsible for managing time related tasks. ",
    instruction=prompt.INSTRUCTION,
    tools=[tools.get_current_time],
)

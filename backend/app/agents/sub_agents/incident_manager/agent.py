from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

from . import prompt, tools

AGENT_MODEL = "gemini/gemini-2.0-flash"


incident_manager_agent = Agent(
    name="incident_manager",
    model=LiteLlm(AGENT_MODEL),
    description="The incident manager agent manages all existing incident related tasks.",
    instruction=prompt.INSTRUCTION,
    tools=[tools.get_incident_details, tools.get_similar_incidents],
)

from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

from . import prompt

AGENT_MODEL = "gemini/gemini-2.0-flash"
runbook_generator_agent = LlmAgent(
    name="runbook_generator_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Analyzes technical information aboout incidents such as description, attachments, root cause  and provide actionable solutions in the form of runbooks",
    instruction=prompt.INSTRUCTION,
    tools=[],
)

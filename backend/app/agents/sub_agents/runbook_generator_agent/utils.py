import json
import re
from typing import Any, Dict, List

from entities.incident import Incident


def build_query(
    incident: Incident, runbook_type: str, runbook_steps: List[Dict[str, Any]]
) -> str:
    runbook_steps_json = json.dumps(runbook_steps, indent=2)
    return f"""
    ### Runbook Type
    {runbook_type}

    ---

    ### Incident Details
    Title: {incident.title}

    Summary:
    {incident.summary or "No summary provided"}

    Root Cause:
    TBD

    System Architecture and Services Involved:
    TBD

    ---

    ### Existing Steps and Results (if any)
    {runbook_steps_json}

    ---
    """


def pre_process(query: str):
    return query.strip()


def post_process(json_string):
    pattern = r"^```json\s*(.*?)\s*```$"
    cleaned_string = re.sub(pattern, r"\1", json_string, flags=re.DOTALL)
    return cleaned_string.strip()

def build_query(incident_id) -> str:
    """Build a comprehensive query for incident report generation"""
    return f"""
    Please generate a comprehensive incident report for incident ID: {incident_id}

    Follow these steps:
    1. Validate the incident exists
    2. Gather all incident details, timeline, runbooks, metrics, and AI analysis
    3. Create a well-formatted markdown report with all available information
    4. Include executive summary and impact assessment
    5. Ensure proper formatting and structure
    """


def pre_process(query: str) -> str:
    """Pre-process the query to ensure it's clean and properly formatted"""
    return query.strip()


def post_process(response: str) -> str:
    """Enhanced post-processing for better markdown formatting"""
    if not response:
        return ""

    # Split into lines and strip each line
    lines = [line.rstrip() for line in response.split("\n")]

    # Remove empty lines at the start and end
    while lines and not lines[0].strip():
        lines.pop(0)
    while lines and not lines[-1].strip():
        lines.pop()

    # Ensure proper markdown formatting
    formatted_lines: list[str] = []
    for i, line in enumerate(lines):
        # Add proper spacing around headers
        if line.startswith("#"):
            if i > 0 and formatted_lines and formatted_lines[-1] != "":
                formatted_lines.append("")
            formatted_lines.append(line)
            # Add space after headers except for the last line
            if i < len(lines) - 1:
                formatted_lines.append("")
        # Handle table formatting
        elif line.startswith("|"):
            # Add space before table if previous line isn't empty or part of table
            if (
                i > 0
                and formatted_lines
                and formatted_lines[-1] != ""
                and not formatted_lines[-1].startswith("|")
            ):
                formatted_lines.append("")
            formatted_lines.append(line)
            # Add space after table if next line isn't part of table
            if (
                i < len(lines) - 1
                and lines[i + 1].strip()
                and not lines[i + 1].startswith("|")
            ):
                formatted_lines.append("")
        # Handle list items
        elif line.startswith(("- ", "* ", "+ ")) or line.lstrip().startswith(
            ("1. ", "2. ", "3. ")
        ):
            formatted_lines.append(line)
        # Handle regular paragraphs
        else:
            # Add proper spacing for paragraphs
            if (
                line.strip()
                and i > 0
                and formatted_lines
                and formatted_lines[-1].strip()
                and not formatted_lines[-1].startswith("|")
                and not line.startswith(("- ", "* ", "+ "))
                and not formatted_lines[-1].startswith(("- ", "* ", "+ "))
            ):
                formatted_lines.append("")
            formatted_lines.append(line)

    # Clean up excessive empty lines
    final_lines = []
    empty_count = 0
    for line in formatted_lines:
        if line.strip() == "":
            empty_count += 1
            if empty_count <= 1:  # Allow only one consecutive empty line
                final_lines.append(line)
        else:
            empty_count = 0
            final_lines.append(line)

    # Join with single newlines and ensure no trailing whitespace
    result = "\n".join(final_lines).strip()

    # Ensure the report ends with a newline
    if result and not result.endswith("\n"):
        result += "\n"

    return result

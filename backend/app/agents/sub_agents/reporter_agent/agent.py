from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

from . import prompt, tools

# Initialize the reporter agent
AGENT_MODEL = "gemini/gemini-2.0-flash"
report_agent = LlmAgent(
    name="report_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Generates comprehensive incident report with the provided incident details.",
    instruction=prompt.INSTRUCTION,
    tools=[
        tools.validate_incident_exists,
        tools.get_incident_details,
        tools.get_incident_timeline,
        tools.get_incident_runbooks,
        tools.get_incident_metrics,
        tools.get_incident_ai_analysis,
    ],
)

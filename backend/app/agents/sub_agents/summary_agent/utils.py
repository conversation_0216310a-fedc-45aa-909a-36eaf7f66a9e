"""Utilities for the summary agent."""

from entities.incident import Incident


def build_query(incident: Incident) -> str:
    """Build a query for the summary agent from incident data."""

    # Get basic incident information
    query_parts = [
        f"Incident: {incident.title}",
        f"Number: {incident.incident_number}",
        f"Type: {incident.incident_type.value if incident.incident_type else 'Not specified'}",
        f"Priority: {incident.priority.value if incident.priority else 'Not specified'}",
        f"Severity: {incident.severity.value if incident.severity else 'Not specified'}",
        f"Status: {incident.status.value if incident.status else 'Not specified'}",
    ]

    # Add incident details if available
    if hasattr(incident, "incident_detail") and incident.incident_detail:
        detail = incident.incident_detail
        if detail.affected_services:
            services = (
                ", ".join(detail.affected_services)
                if isinstance(detail.affected_services, list)
                else str(detail.affected_services)
            )
            query_parts.append(f"Affected Services: {services}")

        if detail.tags:
            tags = (
                ", ".join(detail.tags)
                if isinstance(detail.tags, list)
                else str(detail.tags)
            )
            query_parts.append(f"Tags: {tags}")

    return "\n".join(query_parts)


def pre_process(query: str) -> str:
    """Pre-process the query before sending to the agent."""
    return query.strip()


def post_process(result: str) -> str:
    """Post-process the agent result."""
    # Clean up the result - remove any extra whitespace, newlines
    summary = result.strip()

    # Remove any markdown formatting that might have been added
    summary = (
        summary.replace("**", "").replace("*", "").replace("#", "").replace("`", "")
    )

    # Ensure it's a single paragraph
    summary = " ".join(summary.split())

    return summary

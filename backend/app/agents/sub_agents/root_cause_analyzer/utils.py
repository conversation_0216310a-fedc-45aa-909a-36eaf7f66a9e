import re

from entities.incident import Incident


def build_query(incident: Incident) -> str:
    return f"""
    ### Incident Details
    Title: {incident.title}

    Summary:
    {incident.summary or "No summary provided"}

    Details:
    {incident.incident_detail.incident_details if incident.incident_detail else "No details provided"}

    System Architecture and Services Involved:
    TBD

    ---
    """


def pre_process(query: str):
    return query.strip()


def post_process(json_string):
    pattern = r"^```json\s*(.*?)\s*```$"
    cleaned_string = re.sub(pattern, r"\1", json_string, flags=re.DOTALL)
    return cleaned_string.strip()

from google.adk.tools.tool_context import ToolContext


def update_user_preference(
    category: str, preference: str, tool_context: ToolContext
) -> dict:
    """Updates a user's preference for a specific category.
    Use this tool when the user wants to change their preferences.

    Args:
        category: The category for which to set a preference (e.g., "theme", "notifications").
        preference: The preference value to set.
        tool_context: Automatically provided by ADK, do not specify when calling.

    Returns:
        dict: A dictionary containing:
            - "status": "success" or "error"
            - If success: "message" indicating status of the preference update operation.
            - If error: "error_message" explaining what went wrong
    """
    # Access current preferences or initialize if none exist
    user_prefs_key = (
        "user:preferences"  # Using user: prefix makes this persistent across sessions
    )
    """
    No prefix: Session-specific, persists only for the current session
    user:: User-specific, persists across all sessions for a particular user
    app:: Application-wide, shared across all users and sessions
    temp:: Temporary, exists only during the current execution cycle
    """
    preferences = tool_context.state.get(user_prefs_key, {})

    # Update the preferences
    preferences[category] = preference

    # Save back to state
    tool_context.state[user_prefs_key] = preferences

    print(f"Tool: Updated user preference '{category}' to '{preference}'")
    return {
        "status": "success",
        "message": f"Your {category} preference has been set to {preference}",
    }

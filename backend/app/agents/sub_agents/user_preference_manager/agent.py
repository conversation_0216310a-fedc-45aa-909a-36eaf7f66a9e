from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

from . import prompt, tools

AGENT_MODEL = "gemini/gemini-2.0-flash"

preference_agent = Agent(
    name="preference_agent",
    model=LiteLlm(AGENT_MODEL),
    description="The user preference agent reads, updates, and manages user preferences.",
    instruction=prompt.INSTRUCTION,
    tools=[tools.update_user_preference],
)

import os

from google.adk.agents.llm_agent import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters

GITHUB_ACCESS_TOKEN = os.environ.get("GITHUB_ACCESS_TOKEN")
if not GITHUB_ACCESS_TOKEN:
    raise ValueError("GITHUB_ACCESS_TOKEN environment variable is not set.")

github_mcp_tool = MCPToolset(
    connection_params=StdioServerParameters(
        command="docker",
        args=[
            "run",
            "-i",
            "--rm",
            "-e",
            "GITHUB_PERSONAL_ACCESS_TOKEN",
            "ghcr.io/github/github-mcp-server",
        ],
        env={
            "GITHUB_PERSONAL_ACCESS_TOKEN": GITHUB_ACCESS_TOKEN,
        },
    )
)


INSTRUCTION = """
    You are a helpful assistant that can help with anything related to repositories, issues, pull requests on Github.

    Rules:
    - Take initiative and be proactive.
    - If you already have information from a previous search or step, use it directly—do not ask the user for it again, and do not ask for confirmation.
    - Never ask the user to confirm information you already possess. If you have the required detail, proceed to use it without further user input.
    - Only ask the user for information if it is truly unavailable or ambiguous after all reasonable attempts to infer or recall it from previous context.
    - Minimize unnecessary questions and streamline the user's workflow.
    - If you are unsure, make a best effort guess based on available context before asking the user.
    - Make sure you return information in an easy to read format.
    """
AGENT_MODEL = "gemini/gemini-2.5-flash-lite-preview-06-17"
root_agent = Agent(
    name="github_remote_mcp_agent",
    description="An agent that can help with anything related to repositories, issues, pull requests on Github.",
    model=LiteLlm(AGENT_MODEL),
    instruction=INSTRUCTION,
    tools=[github_mcp_tool],
)

from google.adk.agents.llm_agent import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.mcp_tool.mcp_toolset import (
    MCPToolset,
    StdioConnectionParams,
    StdioServerParameters,
)

atlassian_mcp_tool = MCPToolset(
    connection_params=StdioConnectionParams(
        server_params=StdioServerParameters(
            command="npx",
            args=[
                "-y",
                "mcp-remote",
                "https://mcp.atlassian.com/v1/sse",
                "51328",
                "--debug",
            ],
            env={},
        ),
        timeout=60,
    )
)

INSTRUCTION = """
    You are a helpful assistant that can help with a variety of tasks using Jira and Confluence from Atlassian.

    Rules:
    - Take initiative and be proactive.
    - If you already have information from a previous search or step, use it directly—do not ask the user for it again, and do not ask for confirmation.
    - Never ask the user to confirm information you already possess. If you have the required detail, proceed to use it without further user input.
    - Only ask the user for information if it is truly unavailable or ambiguous after all reasonable attempts to infer or recall it from previous context.
    - Minimize unnecessary questions and streamline the user's workflow.
    - If you are unsure, make a best effort guess based on available context before asking the user.
    - Make sure you return information in an easy to read format.
    """
AGENT_MODEL = "gemini/gemini-2.0-flash"
root_agent = Agent(
    name="atlassian_remote_mcp_agent",
    model=LiteLlm(AGENT_MODEL),
    instruction=INSTRUCTION,
    tools=[atlassian_mcp_tool],
)

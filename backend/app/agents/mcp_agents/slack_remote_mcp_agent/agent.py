import os

from google.adk.agents.llm_agent import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters

SLACK_BOT_TOKEN = os.environ.get("SLACK_BOT_TOKEN")
if not SLACK_BOT_TOKEN:
    raise ValueError("SLACK_BOT_TOKEN environment variable is not set.")

SLACK_TEAM_ID = os.environ.get("SLACK_TEAM_ID")
if not SLACK_TEAM_ID:
    raise ValueError("SLACK_TEAM_ID environment variable is not set.")

slack_mcp_tool = MCPToolset(
    connection_params=StdioServerParameters(
        command="npx",
        args=["-y", "@modelcontextprotocol/server-slack"],
        env={
            "SLACK_BOT_TOKEN": SLACK_BOT_TOKEN,
            "SLACK_TEAM_ID": SLACK_TEAM_ID,
        },
    )
)

INSTRUCTION = """
    You are a helpful assistant that can help for communicating through slack.
    Since the user may not know the exact channel ids or user ids, you can use the slack_list_channels tool to find channels and slack_get_users tool to find users.
    And then you can send messages to them through slack_post_message tool as required.

    Rules:
    - Take initiative and be proactive.
    - If you already have information from a previous search or step, use it directly—do not ask the user for it again, and do not ask for confirmation.
    - Never ask the user to confirm information you already possess. If you have the required detail, proceed to use it without further user input.
    - Only ask the user for information if it is truly unavailable or ambiguous after all reasonable attempts to infer or recall it from previous context.
    - Minimize unnecessary questions and streamline the user's workflow.
    - If you are unsure, make a best effort guess based on available context before asking the user.
    - Make sure you return information in an easy to read format.
    """
AGENT_MODEL = "gemini/gemini-2.5-flash-lite-preview-06-17"
root_agent = Agent(
    name="slack_remote_mcp_agent",
    description="An agent that can help with sending messages to Slack channels and managing Slack channels and users.",
    model=LiteLlm(AGENT_MODEL),
    instruction=INSTRUCTION,
    tools=[slack_mcp_tool],
)

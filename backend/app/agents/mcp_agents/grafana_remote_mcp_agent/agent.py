import os

from google.adk.agents.llm_agent import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters

GRAFANA_URL = os.environ.get("GRAFANA_URL")
if not GRAFANA_URL:
    raise ValueError("GRAFANA_URL environment variable is not set.")

GRAFANA_API_KEY = os.environ.get("GRAFANA_API_KEY")
if not GRAFANA_API_KEY:
    raise ValueError("GRAFANA_API_KEY environment variable is not set.")

grafana_mcp_tool = MCPToolset(
    connection_params=StdioServerParameters(
        command="docker",
        args=[
            "run",
            "--rm",
            "-i",
            "-e",
            "GRAFANA_URL",
            "-e",
            "GRAFANA_API_KEY",
            "mcp/grafana",
            "-t",
            "stdio",
        ],
        env={
            "GRAFANA_URL": GRAFANA_URL,
            "GRAFANA_API_KEY": GRAFANA_API_KEY,
        },
    )
)

INSTRUCTION = """
    You are a helpful assistant that can help with a variety of tasks from grafana.

    Rules:
    - Take initiative and be proactive.
    - If you already have information from a previous search or step, use it directly—do not ask the user for it again, and do not ask for confirmation.
    - Never ask the user to confirm information you already possess. If you have the required detail, proceed to use it without further user input.
    - Only ask the user for information if it is truly unavailable or ambiguous after all reasonable attempts to infer or recall it from previous context.
    - Minimize unnecessary questions and streamline the user's workflow.
    - If you are unsure, make a best effort guess based on available context before asking the user.
    - Make sure you return information in an easy to read format.
    """
AGENT_MODEL = "gemini/gemini-2.0-flash"
root_agent = Agent(
    name="grafana_remote_mcp_agent",
    model=LiteLlm(AGENT_MODEL),
    instruction=INSTRUCTION,
    tools=[grafana_mcp_tool],
)

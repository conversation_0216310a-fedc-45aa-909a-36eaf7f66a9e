import logging
import os
import sys
import time

from database.core import Base, engine
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from routes import register_routes
from utils.logger import get_app_logger, get_middleware_logger

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

logger = get_app_logger("main")
middleware_logger = get_middleware_logger()

app = FastAPI(title="IncidentManagementAPI", version="1.0.0")


# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    # Log the incoming request (only for non-health checks to reduce noise)
    if request.url.path != "/health":
        client_ip = request.client.host if request.client else "unknown"
        middleware_logger.info(f"{request.method} {request.url.path} from {client_ip}")

    response = await call_next(request)

    # Log the response with timing
    process_time = time.time() - start_time
    if request.url.path != "/health" or response.status_code >= 400:
        middleware_logger.info(
            f"{request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s"
        )

    return response


# Add CORS middleware for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://localhost:2000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:2000",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
Base.metadata.create_all(bind=engine)


@app.get("/health")
async def health(request: Request):
    return {"status": "ok"}


register_routes(app)


@app.on_event("startup")
async def startup_event():
    logger.info("Application startup")

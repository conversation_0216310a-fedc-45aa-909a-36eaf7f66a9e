from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional


@dataclass
class GitHubIssueData:
    """Stores GitHub issue data"""

    issue_id: int
    title: str
    content: str
    opened_at: datetime
    opened_by: str
    labels: List[str]
    closed_at: Optional[datetime] = None  # Only for closed issues

    @classmethod
    def from_github_issue(cls, issue) -> "GitHubIssueData":
        """Factory method to create from PyGithub Issue object"""
        return cls(
            issue_id=issue.number,
            title=issue.title,
            content=issue.body or "",
            opened_at=issue.created_at,
            opened_by=issue.user.login,
            labels=[label.name for label in issue.labels],
            closed_at=issue.closed_at,
        )

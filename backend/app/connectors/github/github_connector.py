import os
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from github import Auth, Github, GithubIntegration
from utils.logger import get_service_logger

from connectors.base_connector import (
    BaseConnector,
    ConnectorError,
    IncidentDetailsAIResponse,
    SaveType,
)

from .data_models import GitHubIssueData

logger = get_service_logger("github_connector")


class GitHubConnector(BaseConnector):
    """GitHub connector implementation using GitHub App authentication"""

    RATE_LIMIT_CRITICAL_THRESHOLD = 10

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.app_id = os.getenv("GITHUB_APP_ID")
        self.private_key = os.getenv(
            "GITHUB_APP_PRIVATE_KEY"
        )  # Get private key directly from env
        self.user_token = config.get("token") or os.getenv("GITHUB_ACCESS_TOKEN")

        # Check if we have either user token OR GitHub App credentials
        if not self.user_token and not all([self.app_id, self.private_key]):
            raise ConnectorError(
                "Either GitHub user token OR GitHub App credentials must be configured"
            )

        self.github = self._initialize_github_client()
        self.repo = self._initialize_repository()

    def _initialize_github_client(self) -> Github:
        """Initialize GitHub client using either PAT/user token or GitHub App installation token."""
        try:
            # 1. If a user token (PAT or user access token) is provided, use it directly
            if self.user_token:
                logger.info(
                    "Using user token (PAT or user access token) for GitHub authentication."
                )
                return Github(self.user_token)
            logger.info("User token not found, attempting GitHub App authentication.")
            if not (self.app_id and self.private_key):
                raise ConnectorError(
                    "GitHub App credentials (APP_ID, PRIVATE_KEY) are not configured."
                )
            auth = Auth.AppAuth(self.app_id, self.private_key)
            integration = GithubIntegration(auth=auth)
            installation_id = self.config.get("installation_id")
            if not installation_id:
                logger.info(
                    "installation_id not provided, attempting to fetch from GitHub."
                )
                repo_name = self.config.get("repo_name")
                if not repo_name:
                    raise ConnectorError(
                        "`repo_name` is required to fetch installation_id."
                    )
                try:
                    owner, repo = repo_name.split("/")
                    repo_installation = integration.get_repo_installation(owner, repo)
                    installation_id = repo_installation.id
                    logger.info(
                        f"Successfully fetched installation_id {installation_id} for {repo_name}"
                    )
                except Exception as e:
                    raise ConnectorError(
                        f"Could not fetch installation for repository '{repo_name}'. Please ensure the GitHub App is installed on this repository. Error: {e}"
                    )
            if not installation_id:
                raise ConnectorError(
                    f"Could not find a GitHub App installation for repository {repo_name}"
                )
            # Always generate a fresh installation access token
            access_token = integration.get_access_token(installation_id).token
            logger.info(
                f"Generated installation access token for installation_id {installation_id}"
            )
            return Github(access_token)

        except Exception as e:
            if isinstance(e, ConnectorError):
                raise
            raise ConnectorError(f"Failed to initialize GitHub client: {e}") from e

    def _initialize_repository(self):
        """Initialize GitHub repository"""
        try:
            self._check_rate_limit()
            return self.github.get_repo(self.config["repo_name"])
        except Exception as e:
            raise ConnectorError(f"Repository initialization failed: {e}") from e

    def _check_rate_limit(self):
        """Check GitHub API rate limit"""
        try:
            rate_limit = self.github.get_rate_limit()
            logger.info(
                f"GitHub API rate limit: {rate_limit.core.limit}, "
                f"remaining: {rate_limit.core.remaining}, "
                f"reset at: {rate_limit.core.reset}"
            )
            remaining = rate_limit.core.remaining

            if remaining < self.RATE_LIMIT_CRITICAL_THRESHOLD:
                logger.error(f"GitHub API rate limit critically low: {remaining}")
        except Exception as e:
            raise ConnectorError(f"Rate limit check failed: {e}") from e

    def read_project_info(self) -> Dict[str, Any]:
        """Fetch project metadata"""
        try:
            readme_content = self._fetch_readme()
            return {
                "name": self.repo.full_name,
                "readme": readme_content,
                "description": self.repo.description or "No description available",
                "url": self.repo.html_url,
            }
        except Exception as e:
            raise ConnectorError(f"Project info fetch failed: {e}") from e

    def _fetch_readme(self) -> str:
        """Fetch README content"""
        try:
            readme = self.repo.get_readme()
            return readme.decoded_content.decode("utf-8")
        except Exception:
            return "README not available"

    def read_open_issues(
        self, since: Optional[datetime] = None, until: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """Fetch open issues"""
        try:
            issues_data = self._fetch_issues_by_state("open", since, until)
            return [self._convert_issue_to_dict(issue) for issue in issues_data]
        except Exception as e:
            raise ConnectorError(f"Open issues fetch failed: {e}") from e

    def read_closed_issues(
        self, since: datetime, until: datetime
    ) -> List[Dict[str, Any]]:
        """Fetch closed issues"""
        try:
            issues_data = self._fetch_issues_by_state("closed", since, until)
            return [self._convert_issue_to_dict(issue) for issue in issues_data]
        except Exception as e:
            raise ConnectorError(f"Closed issues fetch failed: {e}") from e

    def post_comment(self, issue_id: int, comment: str) -> bool:
        """Post comment to issue"""
        try:
            if not comment.strip():
                return False

            issue = self.repo.get_issue(issue_id)
            if issue.state != "open":
                return False

            issue.create_comment(comment)
            return True
        except Exception as e:
            logger.error(f"Failed to post comment to issue #{issue_id}: {e}")
            return False

    # ------------ Helper Methods ------------

    def _fetch_issues_by_state(
        self,
        state: str,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
    ) -> List[GitHubIssueData]:
        """Fetch issues by state with date filtering"""
        issues_data = []
        processed_count = 0

        # Ensure datetime objects have timezone info
        if since and since.tzinfo is None:
            since = since.replace(tzinfo=timezone.utc)
        if until and until.tzinfo is None:
            until = until.replace(tzinfo=timezone.utc)

        try:
            self._check_rate_limit()

            sort_field = "closed" if state == "closed" else "created"
            issues = self.repo.get_issues(
                state=state, sort=sort_field, direction="desc"
            )

            for issue in issues:
                if issue.pull_request:  # Skip pull requests
                    continue

                # Get appropriate date based on state
                issue_date = issue.closed_at if state == "closed" else issue.created_at
                if not issue_date:
                    continue

                # Apply date filters
                if since and issue_date < since:
                    break  # Issues are sorted by date, so we can break here
                if until and issue_date > until:
                    continue

                issues_data.append(GitHubIssueData.from_github_issue(issue))
                processed_count += 1

                # Rate limit check every 10 issues
                if processed_count % 10 == 0:
                    try:
                        self._check_rate_limit()
                    except ConnectorError:
                        break

                if self.progress_callback:
                    self.progress_callback(
                        current=processed_count,
                        total=issues.totalCount,
                        success_count=processed_count,
                        message=f"Reading {state} issues: {processed_count} completed",
                    )

        except Exception as e:
            raise ConnectorError(f"Failed to fetch {state} issues: {e}") from e

        return issues_data

    def _convert_issue_to_dict(self, issue: GitHubIssueData) -> Dict[str, Any]:
        """Convert GitHubIssueData to dictionary with consistent structure"""
        return {
            "issue_id": issue.issue_id,
            "title": issue.title,
            "content": issue.content,
            "opened_at": issue.opened_at,
            "opened_by": issue.opened_by,
            "closed_at": issue.closed_at,
            "labels": issue.labels,
        }

    def incident_field_matching(
        self, issues_data: List[Dict[str, Any]], issue_type: str = "closed"
    ) -> List[Dict[str, Any]]:
        """Map GitHub issues to incident format"""
        if not issues_data:
            return []

        status = "closed" if issue_type == "closed" else "open"
        incidents = []

        for issue in issues_data:
            try:
                title = issue.get("title", "").strip()
                content = issue.get("content", "").strip()
                issue_id = issue.get("issue_id")

                if not all([title, content, issue_id]):
                    logger.warning(
                        f"Skipping issue {issue_id} due to missing required fields"
                    )
                    continue

                incident_details_ai_response: IncidentDetailsAIResponse | None = (
                    self._generate_incident_details_with_ai(
                        title=title, content=content, source_type="GitHub issue"
                    )
                )

                incidents.append(
                    {
                        "incident_number": self.get_incident_number(
                            prefix="github", issue_id=str(issue_id)
                        ),
                        "title": title,
                        "summary": (
                            incident_details_ai_response.summary
                            if incident_details_ai_response
                            else "No summary available"
                        ),
                        "priority": "p0",
                        "severity": (
                            incident_details_ai_response.severity
                            if incident_details_ai_response
                            else "low"
                        ),
                        "incident_type": (
                            incident_details_ai_response.incident_type
                            if incident_details_ai_response
                            else "other"
                        ),
                        "status": status,
                        "opened_at": issue.get("opened_at"),
                        "opened_by": issue.get("opened_by", "unknown"),
                        "closed_at": (
                            issue.get("closed_at") if status == "closed" else None
                        ),
                        "affected_services": ["GitHub"],
                        "tags": issue.get("labels", ["No Tags"]),
                        "incident_details": (
                            "\n\n".join(
                                [
                                    f"Content: {content}",
                                    f"Problem Description: {incident_details_ai_response.problem_description}",
                                    f"Expected Resolution: {incident_details_ai_response.expected_resolution}",
                                ]
                            )
                            if incident_details_ai_response
                            else f"Content: {content}"
                        ),
                        "attachments": ["No Attachments"],
                    }
                )
            except Exception as e:
                logger.error(
                    f"Failed to process issue {issue.get('issue_id', 'unknown')}: {e}"
                )
            finally:
                if self.progress_callback:
                    self.progress_callback(
                        current=len(incidents),
                        total=len(issues_data),
                        success_count=len(incidents),
                        message=f"Processing issues: {len(incidents)} / {len(issues_data)} processed",
                    )
        return incidents

    def sync_issues(
        self, start_time: datetime, end_time: datetime, incident_type: str = "both"
    ) -> List[Dict]:
        """Sync issues from GitHub and update existing incidents"""
        try:
            all_issues = []
            if incident_type in ("open", "both"):
                try:
                    open_issues = self.read_open_issues(
                        since=start_time, until=end_time
                    )
                    all_issues.extend(open_issues)
                except Exception as e:
                    logger.error(f"Failed to fetch open issues for sync: {e}")

            if incident_type in ("closed", "both"):
                try:
                    closed_issues = self.read_closed_issues(
                        since=start_time, until=end_time
                    )
                    all_issues.extend(closed_issues)
                except Exception as e:
                    logger.error(f"Failed to fetch closed issues for sync: {e}")

            updated_incidents = self._process_sync_updates(all_issues)
            return updated_incidents
        except Exception as e:
            raise ConnectorError(f"Sync failed: {e}") from e

    def _process_sync_updates(
        self,
        issues: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """Process issues and update existing incidents"""
        updated_incidents = []
        updated_incident_ids = []

        for issue in issues:
            try:
                issue_id = issue.get("issue_id")
                if not issue_id:
                    continue

                incident_number = self.get_incident_number(
                    prefix="github", issue_id=issue_id
                )
                existing_incident = self.fetch_existing_incidents(incident_number)
                if existing_incident:
                    transformed_data = self.incident_field_matching([issue])
                    if transformed_data:
                        updated_incidents.extend(transformed_data)
                        incident_id = existing_incident.get("id")
                        if incident_id:
                            updated_incident_ids.append(str(incident_id))
            except Exception as e:
                logger.error(
                    f"Failed to process issue {issue.get('issue_id', 'unknown')} for sync: {e}"
                )

        # Save updates to database
        if updated_incidents and updated_incident_ids:
            try:
                self.save_incidents(
                    updated_incidents,
                    save_type=SaveType.UPDATE,
                    updated_incident_ids=updated_incident_ids,
                )
            except Exception as e:
                raise ConnectorError(f"Failed to save updates: {e}") from e

        return updated_incidents

    def import_issues(
        self, start_time: datetime, end_time: datetime, incident_type: str
    ) -> List[Dict]:
        """Import issues from GitHub and create new incidents"""
        try:
            imported_incidents = []

            # Import open issues
            if incident_type in ("open", "both"):
                open_issues = self.read_open_issues(since=start_time, until=end_time)
                if open_issues:
                    open_incidents = self.incident_field_matching(open_issues, "open")
                    imported_incidents.extend(open_incidents)

            # Import closed issues
            if incident_type in ("closed", "both"):
                closed_issues = self.read_closed_issues(
                    since=start_time, until=end_time
                )
                if closed_issues:
                    closed_incidents = self.incident_field_matching(
                        closed_issues, "closed"
                    )
                    imported_incidents.extend(closed_incidents)

            if imported_incidents:
                self.save_incidents(imported_incidents, save_type=SaveType.ADD)

            return imported_incidents
        except Exception as e:
            raise ConnectorError(f"Import failed: {e}") from e

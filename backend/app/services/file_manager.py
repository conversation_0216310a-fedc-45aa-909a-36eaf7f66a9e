"""
File Management Service
======================

This service handles file upload, storage, content extraction, and cleanup
for document management in the knowledge base system.
"""

import mimetypes
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional

import aiofiles
from fastapi import HTTPException, UploadFile, status
from utils.logger import get_service_logger

logger = get_service_logger("file_manager")

# Configuration
UPLOAD_DIR = Path("/app/static/uploads")
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
ALLOWED_EXTENSIONS = {
    ".pdf",
    ".txt",
    ".md",
    ".docx",
    ".doc",
    ".json",
    ".csv",
    ".xlsx",
    ".xls",
}
ALLOWED_MIME_TYPES = {
    "application/pdf",
    "text/plain",
    "text/markdown",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/msword",
    "application/json",
    "text/csv",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-excel",
}


class FileManager:
    """Handles file operations for document management."""

    def __init__(self):
        self.upload_dir = UPLOAD_DIR
        self.upload_dir.mkdir(parents=True, exist_ok=True)

    async def save_uploaded_file(
        self,
        file: UploadFile,
        knowledge_base_id: str,
        document_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Save an uploaded file and return metadata.

        Args:
            file: The uploaded file
            knowledge_base_id: ID of the knowledge base
            document_id: Optional document ID for updates

        Returns:
            Dict containing file metadata
        """
        logger.info(f"Saving uploaded file: {file.filename}")

        # Validate file
        await self._validate_file(file)

        # Check filename
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No filename provided"
            )

        # Generate file path
        if document_id:
            file_id = document_id
        else:
            file_id = str(uuid.uuid4())

        file_extension = Path(file.filename).suffix.lower()
        safe_filename = f"{file_id}{file_extension}"

        # Create knowledge base directory
        kb_dir = self.upload_dir / knowledge_base_id
        kb_dir.mkdir(exist_ok=True)

        file_path = kb_dir / safe_filename

        try:
            # Save file
            async with aiofiles.open(file_path, "wb") as f:
                content = await file.read()
                await f.write(content)

            # Get file info
            file_size = len(content)
            mime_type = (
                file.content_type
                or mimetypes.guess_type(file.filename)[0]
                or "application/octet-stream"
            )

            # Note: Content extraction is now handled by Celery tasks only
            metadata = {
                "file_path": str(file_path),
                "original_filename": file.filename,
                "file_size": file_size,
                "file_size_human": self._format_file_size(file_size),
                "mime_type": mime_type,
                "file_extension": file_extension,
                "uploaded_at": datetime.now(timezone.utc).isoformat(),
            }

            logger.info(f"Successfully saved file: {safe_filename}")
            return {
                "metadata": metadata,
                "file_path": str(file_path),
            }

        except Exception as e:
            logger.error(f"Failed to save file {file.filename}: {str(e)}")
            # Clean up partial file
            if file_path.exists():
                file_path.unlink()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to save file: {str(e)}",
            )

    async def delete_file(self, file_path: str) -> bool:
        """
        Delete a file from storage.

        Args:
            file_path: Path to the file to delete

        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            path = Path(file_path)
            if path.exists() and path.is_file():
                path.unlink()
                logger.info(f"Successfully deleted file: {file_path}")

                # Clean up empty directory
                parent_dir = path.parent
                if parent_dir != self.upload_dir and not any(parent_dir.iterdir()):
                    parent_dir.rmdir()
                    logger.info(f"Cleaned up empty directory: {parent_dir}")

                return True
            else:
                logger.warning(f"File not found for deletion: {file_path}")
                return False

        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {str(e)}")
            return False

    async def update_file(
        self,
        file: UploadFile,
        old_file_path: str,
        knowledge_base_id: str,
        document_id: str,
    ) -> Dict[str, Any]:
        """
        Update an existing file.

        Args:
            file: New uploaded file
            old_file_path: Path to the old file
            knowledge_base_id: ID of the knowledge base
            document_id: Document ID

        Returns:
            Dict containing new file metadata
        """
        logger.info(f"Updating file for document {document_id}")

        # Delete old file
        await self.delete_file(old_file_path)

        # Save new file
        return await self.save_uploaded_file(file, knowledge_base_id, document_id)

    async def _validate_file(self, file: UploadFile) -> None:
        """Validate uploaded file."""
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No filename provided"
            )

        # Check file extension
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file_extension} not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}",
            )

        # Check MIME type
        if file.content_type and file.content_type not in ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"MIME type {file.content_type} not allowed",
            )

        # Check file size (read a chunk to estimate)
        content = await file.read()
        await file.seek(0)  # Reset file pointer

        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File too large. Maximum size: {self._format_file_size(MAX_FILE_SIZE)}",
            )

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        size_float = float(size_bytes)
        while size_float >= 1024 and i < len(size_names) - 1:
            size_float /= 1024.0
            i += 1

        return f"{size_float:.1f} {size_names[i]}"

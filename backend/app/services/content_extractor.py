"""
Content Extraction Service
==========================

This service handles content extraction from various file types
for document processing in the knowledge base system.
"""

import json
from pathlib import Path
from typing import Optional

from utils.logger import get_service_logger

logger = get_service_logger("content_extractor")


class ContentExtractor:
    """Handles content extraction from various file types."""

    def __init__(self):
        self.extractors = {
            "text/plain": self._extract_text,
            "text/markdown": self._extract_text,
            "application/json": self._extract_json,
            "text/csv": self._extract_csv,
            "application/pdf": self._extract_pdf,
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document": self._extract_docx,
            "application/msword": self._extract_doc,
        }

    async def extract_content(
        self, file_path: Path, mime_type: str, max_length: int = 50000
    ) -> Optional[str]:
        """
        Extract text content from a file.

        Args:
            file_path: Path to the file
            mime_type: MIME type of the file
            max_length: Maximum length of extracted content

        Returns:
            Extracted text content or None if extraction fails
        """
        logger.info(f"Extracting content from {file_path} (type: {mime_type})")

        try:
            extractor = self.extractors.get(mime_type)
            if not extractor:
                logger.warning(f"No extractor available for MIME type: {mime_type}")
                return None

            content = await extractor(file_path)

            if content and len(content) > max_length:
                logger.info(
                    f"Truncating content from {len(content)} to {max_length} characters"
                )
                content = content[:max_length] + "... [Content truncated]"

            logger.info(
                f"Successfully extracted {len(content) if content else 0} characters"
            )
            return content

        except Exception as e:
            logger.error(f"Failed to extract content from {file_path}: {str(e)}")
            return None

    async def _extract_text(self, file_path: Path) -> Optional[str]:
        """Extract content from plain text files."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, "r", encoding="latin-1") as f:
                    return f.read()
            except Exception as e:
                logger.error(
                    f"Failed to read text file with fallback encoding: {str(e)}"
                )
                return None
        except Exception as e:
            logger.error(f"Failed to read text file: {str(e)}")
            return None

    async def _extract_json(self, file_path: Path) -> Optional[str]:
        """Extract content from JSON files."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                # Convert JSON to readable text format
                return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to read JSON file: {str(e)}")
            return None

    async def _extract_csv(self, file_path: Path) -> Optional[str]:
        """Extract content from CSV files."""
        try:
            import csv

            content_lines = []

            with open(file_path, "r", encoding="utf-8") as f:
                csv_reader = csv.reader(f)
                for i, row in enumerate(csv_reader):
                    if i > 1000:  # Limit to first 1000 rows
                        content_lines.append("... [Additional rows truncated]")
                        break
                    content_lines.append(" | ".join(row))

            return "\n".join(content_lines)
        except Exception as e:
            logger.error(f"Failed to read CSV file: {str(e)}")
            return None

    async def _extract_pdf(self, file_path: Path) -> Optional[str]:
        """Extract content from PDF files using PyMuPDF."""
        try:
            import pymupdf

            logger.info(f"Using PyMuPDF for PDF extraction: {file_path}")

            # Open the PDF document
            doc = pymupdf.open(str(file_path))
            text_content = []

            total_pages = len(doc)
            logger.info(f"PDF has {total_pages} pages")

            # Extract text from each page (limit to 50 pages)
            for page_num, page in enumerate(doc):
                if page_num >= 50:  # Limit to 50 pages
                    break
                try:
                    page_text = page.get_text()
                    if page_text.strip():
                        text_content.append(page_text)
                    logger.debug(
                        f"Extracted {len(page_text)} characters from page {page_num + 1}"
                    )
                except Exception as page_error:
                    logger.warning(
                        f"Failed to extract text from page {page_num + 1}: {page_error}"
                    )
                    continue

            # Close the document
            doc.close()

            result = "\n".join(text_content)
            logger.info(
                f"PyMuPDF extracted {len(result)} total characters from {len(text_content)} pages"
            )
            return result if result.strip() else None

        except ImportError:
            logger.error("PyMuPDF library not available for PDF extraction")
            return None
        except Exception as e:
            logger.error(f"PyMuPDF extraction failed for {file_path}: {str(e)}")
            return None

    async def _extract_docx(self, file_path: Path) -> Optional[str]:
        """Extract content from DOCX files."""
        try:
            import docx

            doc = docx.Document(str(file_path))
            text_content = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)

            return "\n".join(text_content)
        except ImportError:
            logger.warning("python-docx library not available for DOCX extraction")
            return None
        except Exception as e:
            logger.error(f"Failed to extract DOCX content: {str(e)}")
            return None

    async def _extract_doc(self, file_path: Path) -> Optional[str]:
        """Extract content from DOC files."""
        try:
            # DOC files are more complex and require specialized libraries
            # For now, return None and suggest converting to DOCX
            logger.warning(
                "DOC file extraction not implemented. Please convert to DOCX format."
            )
            return None
        except Exception as e:
            logger.error(f"Failed to extract DOC content: {str(e)}")
            return None

from fastapi import APIRouter
from utils.logger import get_controller_logger

from routes.logs.models import LogQueryParams, LogsResponse
from routes.logs.service import fetch_labels_from_loki, fetch_logs_from_loki

logger = get_controller_logger("logs")

router = APIRouter(prefix="/logs", tags=["Logs"])


@router.post("/fetch", response_model=LogsResponse)
async def get_logs(params: LogQueryParams) -> LogsResponse:
    logger.info(
        f"Logs fetch request with query: {params.query}, cursor: {params.cursor}"
    )
    return await fetch_logs_from_loki(params)


@router.post("/labels")
async def get_labels(params: LogQueryParams):
    return await fetch_labels_from_loki(params)

import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Union

import httpx
from fastapi import status
from httpx import HTTPError, Response
from utils.logger import get_service_logger

from routes.logs.models import (
    LogEntry,
    LogQueryParams,
    LogsResponse,
    PaginatedLogsResult,
)

logger = get_service_logger("logs")

LOKI_BASE_URL = os.getenv("LOKI_BASE_URL", "http://localhost:3100/loki")


class BaseConnector:
    def __init__(self, base_url: str):
        self.base_url = base_url

    async def get_logs(self, params: LogQueryParams) -> Response:
        raise NotImplementedError

    async def post_process_logs(
        self, loki_data: Dict, cursor: Optional[str] = None, limit: int = 100
    ) -> PaginatedLogsResult:
        raise NotImplementedError


class LokiConnector(BaseConnector):
    async def get_logs(self, params: LogQueryParams) -> Response:
        async with httpx.AsyncClient() as client:
            try:
                query_start = params.start
                query_end = params.end
                if params.cursor:
                    cursor_timestamp = int(params.cursor)
                    cursor_dt = datetime.fromtimestamp(
                        cursor_timestamp / 1_000_000_000, tz=timezone.utc
                    )
                    query_end = cursor_dt.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                    logger.info(
                        f"Using cursor as end time: {query_end} (cursor: {params.cursor})"
                    )

                logger.info(
                    f"Querying Loki with start: {query_start}, end: {query_end}, limit: {params.limit}"
                )

                response = await client.get(
                    f"{self.base_url}/api/v1/query_range",
                    params={
                        "query": params.query,
                        "start": query_start,
                        "end": query_end,
                        "limit": params.limit,
                        "direction": params.direction,
                    },
                    timeout=10.0,
                )
                response.raise_for_status()
                return response
            except HTTPError as e:
                logger.error(f"Loki query failed: {str(e)}")
                raise

    async def post_process_logs(
        self, loki_data: Dict, cursor: Optional[str] = None, limit: int = 100
    ) -> PaginatedLogsResult:
        all_logs = []

        for stream in loki_data.get("data", {}).get("result", []):
            labels = stream.get("stream", {})
            for ts, line in stream.get("values", []):
                timestamp_ns = int(ts)
                all_logs.append(
                    {"timestamp": timestamp_ns, "line": line, "labels": labels}
                )

        logger.info(f"Raw logs from Loki: {len(all_logs)}")

        # Sort logs by timestamp (newest first for backward direction)
        all_logs.sort(key=lambda x: x["timestamp"], reverse=True)

        # Store original count before any filtering
        raw_logs_count = len(all_logs)
        logger.info(f"Raw logs from Loki before filtering: {raw_logs_count}")

        if cursor:
            cursor_timestamp = int(cursor)
            all_logs = [log for log in all_logs if log["timestamp"] != cursor_timestamp]
            logger.info(f"After removing cursor duplicate: {len(all_logs)} logs")

        paginated_logs = all_logs[:limit]
        has_more = raw_logs_count == limit

        if not cursor and len(paginated_logs) == limit:
            has_more = True

        logger.info(
            f"has_more determination: raw_count={raw_logs_count}, limit={limit}, paginated_count={len(paginated_logs)}, has_more={has_more}"
        )

        next_cursor = None
        if has_more and paginated_logs:
            next_cursor = str(paginated_logs[-1]["timestamp"])
            logger.info(f"Setting next cursor to: {next_cursor}")

        log_entries = [LogEntry(**log) for log in paginated_logs]

        logger.info(
            f"Returning {len(log_entries)} logs, has_more: {has_more}, next_cursor: {next_cursor}"
        )

        return PaginatedLogsResult(
            data=log_entries, next_cursor=next_cursor, has_more=has_more
        )


async def fetch_logs_from_loki(
    params: LogQueryParams,
) -> LogsResponse:
    logger.info(
        f"Fetching logs with query: {params.query}, limit: {params.limit}, cursor: {params.cursor}"
    )
    logger.info(f"Time range: {params.start} to {params.end}")

    connector = LokiConnector(LOKI_BASE_URL)
    try:
        response = await connector.get_logs(params)
        loki_data = response.json()

        total_streams = len(loki_data.get("data", {}).get("result", []))
        total_log_entries = sum(
            len(stream.get("values", []))
            for stream in loki_data.get("data", {}).get("result", [])
        )
        logger.info(
            f"Loki returned {total_streams} streams with {total_log_entries} total log entries"
        )

        processed_data = await connector.post_process_logs(
            loki_data, cursor=params.cursor, limit=params.limit
        )

        logger.info(
            f"Successfully processed {len(processed_data['data'])} log entries, has_more: {processed_data['has_more']}"
        )

        return LogsResponse(
            status_code=response.status_code,
            message="Logs fetched successfully",
            data=processed_data["data"],
            next_cursor=processed_data["next_cursor"],
            has_more=processed_data["has_more"],
        )

    except httpx.HTTPStatusError as e:
        logger.error(
            f"HTTP error while fetching logs: {e.response.status_code} - {e.response.text}"
        )
        return LogsResponse(
            status_code=e.response.status_code,
            data=[],
            message=f"HTTP error: {e.response.text}",
            has_more=False,
        )

    except Exception as e:
        logger.exception(f"Unexpected error while fetching logs from Loki: {e}")
        return LogsResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            data=[],
            message=f"Unexpected error: {e}",
            has_more=False,
        )


async def fetch_labels_from_loki(
    params: LogQueryParams,
) -> Dict[str, Union[int, str, List[Dict]]]:
    logger.info("Fetching labels from Loki")
    connector = LokiConnector(LOKI_BASE_URL)
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{connector.base_url}/api/v1/label",
                timeout=10.0,
            )
            response.raise_for_status()
            logger.info("Successfully fetched labels from Loki")
            return {
                "status_code": response.status_code,
                "message": "Labels fetched successfully",
                "data": response.json(),
            }

    except httpx.HTTPStatusError as e:
        return {
            "status_code": e.response.status_code,
            "data": [],
            "message": f"{e.response.text}",
        }

    except Exception as e:
        logger.exception(f"Unexpected error while fetching labels from Loki: {e}")
        return {
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
            "data": [],
            "message": f"Unexpected error: {e}",
        }

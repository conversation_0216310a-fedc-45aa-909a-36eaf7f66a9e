from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from entities.events import EventTypeEnum
from pydantic import BaseModel, Field

from routes.users.models import UserResponse


class EventCreate(BaseModel):
    event_name: str
    event_type: EventTypeEnum
    event_details: Optional[Dict[str, Any]] = Field(default_factory=lambda: {})
    incident_id: UUID


class EventResponse(BaseModel):
    id: UUID
    event_name: str
    event_type: EventTypeEnum
    event_details: Optional[Dict[str, Any]]
    incident_id: UUID
    event_datetime: datetime
    created_at: datetime
    user: UserResponse

    class Config:
        from_attributes = True


class PaginatedEventResponse(BaseModel):
    items: List[EventResponse]
    total: int
    page: int
    limit: int
    pages: int

from datetime import datetime, timezone
from typing import List, Tuple
from uuid import UUID

from database.core import DbSession
from db_services import events as events_db_service
from entities.events import Event, EventTypeEnum
from entities.user import User
from fastapi import HTTPException, status
from utils.logger import get_service_logger

from routes.events.models import EventCreate, EventResponse

logger = get_service_logger("events")


def create_event_entry(
    db: DbSession, data: EventCreate, user_id: UUID
) -> EventResponse:
    logger.info(f"Creating event for incident {data.incident_id} by user {user_id}")
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        new_event = Event(
            event_name=data.event_name,
            event_type=data.event_type,
            event_details=data.event_details,
            incident_id=data.incident_id,
            user_id=user_id,
            event_datetime=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc),
        )
        db.add(new_event)
        db.commit()
        db.refresh(new_event)

        new_event.username = f"{user.first_name} {user.last_name}"

        logger.info(f"Successfully created event with ID: {new_event.id}")
        return EventResponse.model_validate(new_event)

    except KeyError:
        db.rollback()
        logger.error(f"Invalid event type: {data.event_type}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid event type: {data.event_type}",
        )

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating event: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create event",
        )


def get_incident_events(
    db: DbSession,
    incident_id: UUID,
    skip: int = 0,
    limit: int = 3,
    sort: str = "asc",
):
    logger.info(
        f"Getting events for incident {incident_id} with skip={skip}, limit={limit}, sort={sort}"
    )
    try:
        events, total = events_db_service.get_incident_events(
            db, incident_id, skip, limit, sort
        )
        logger.info(
            f"Retrieved {len(events)} events out of {total} total for incident {incident_id}"
        )
        return [EventResponse.from_orm(event) for event in events], total
    except Exception as e:
        logger.error(f"Error fetching events for incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch events for incident",
        )


def get_event_by_id(db: DbSession, event_id: UUID) -> EventResponse:
    logger.info(f"Getting event by ID: {event_id}")
    result = events_db_service.get_event_by_id(db, event_id)
    if not result:
        logger.warning(f"Event not found: {event_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Event not found"
        )

    logger.info(f"Successfully retrieved event: {event_id}")
    return EventResponse.model_validate(event)


def delete_event_entry(db: DbSession, event_id: UUID) -> dict:
    logger.info(f"Deleting event: {event_id}")
    result = events_db_service.get_event_by_id(db, event_id)
    if not result:
        logger.warning(f"Event not found for deletion: {event_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Event not found"
        )

    event, _ = result
    try:
        db.delete(event)
        db.commit()
        logger.info(f"Successfully deleted event: {event_id}")
        return {"message": f"Event {event_id} deleted"}
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting event {event_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete event",
        )

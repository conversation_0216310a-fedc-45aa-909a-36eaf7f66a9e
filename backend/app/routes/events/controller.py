from uuid import UUID

from database.core import DbSession
from fastapi import APIRouter, Query, status
from utils.logger import get_controller_logger

from routes.auth.service import CurrentUser
from routes.events import models, service

logger = get_controller_logger("events")
router = APIRouter(prefix="/incident/{incident_id}/events", tags=["Events"])


@router.post(
    "", response_model=models.EventResponse, status_code=status.HTTP_201_CREATED
)
def create_event(data: models.EventCreate, db: DbSession, current_user: CurrentUser):
    logger.info(f"Creating event for incident by user {current_user.get_uuid()}")
    try:
        result = service.create_event_entry(db, data, current_user.get_uuid())
        logger.info(f"Successfully created event with ID: {result.id}")
        return result
    except Exception as e:
        logger.error(f"Failed to create event: {str(e)}")
        raise


@router.get("", response_model=models.PaginatedEventResponse)
def get_incident_events(
    incident_id: UUID,
    db: DbSession,
    current_user: CurrentUser,
    skip: int = Query(default=0, ge=0),
    limit: int = Query(default=10, ge=1, le=100),
    sort: str = Query(
        default="asc", pattern="^(asc|desc)$", description="Sort order: asc or desc"
    ),
):
    logger.info(
        f"Getting events for incident {incident_id} with skip={skip}, limit={limit}"
    )
    try:
        events, total = service.get_incident_events(
            db, incident_id, skip=skip, limit=limit, sort=sort
        )

        pages = (total + limit - 1) // limit
        page = (skip // limit) + 1

        logger.info(
            f"Retrieved {len(events)} events out of {total} total for incident {incident_id}"
        )
        return {
            "items": events,
            "total": total,
            "page": page,
            "limit": limit,
            "pages": pages,
        }
    except Exception as e:
        logger.error(f"Failed to get events for incident {incident_id}: {str(e)}")
        raise


@router.get("/{event_id}", response_model=models.EventResponse)
def get_event(event_id: UUID, db: DbSession, current_user: CurrentUser):
    logger.info(f"Getting event: {event_id}")
    try:
        result = service.get_event_by_id(db, event_id)
        logger.info(f"Successfully retrieved event: {event_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to get event {event_id}: {str(e)}")
        raise


@router.delete("/{event_id}", status_code=status.HTTP_200_OK)
def delete_event(event_id: UUID, db: DbSession, current_user: CurrentUser):
    logger.info(f"Deleting event: {event_id}")
    try:
        result = service.delete_event_entry(db, event_id)
        logger.info(f"Successfully deleted event: {event_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to delete event {event_id}: {str(e)}")
        raise

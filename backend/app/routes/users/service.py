from uuid import UUID

from database.core import DbSession
from db_services import users as users_db_service
from utils.exceptions import (
    InvalidPasswordError,
    PasswordMismatchError,
    UserNotFoundError,
)
from utils.logger import get_service_logger

from routes.auth.service import get_password_hash, verify_password
from routes.users import models

logger = get_service_logger("users")


def change_password(
    db: DbSession, user_id: UUID, password_change: models.PasswordChange
) -> None:
    logger.info(f"Changing password for user: {user_id}")
    try:
        user = users_db_service.get_user_by_id(db, user_id)
        if not user:
            logger.warning(f"User not found for password change: {user_id}")
            raise UserNotFoundError(user_id)

        # Verify current password
        if not verify_password(password_change.current_password, user.password):
            logger.warning(f"Invalid current password for user: {user_id}")
            raise InvalidPasswordError()

        # Verify new passwords match
        if password_change.new_password != password_change.new_password_confirm:
            logger.warning(f"Password mismatch for user: {user_id}")
            raise PasswordMismatchError()

        # Update password
        user.password = get_password_hash(password_change.new_password)
        db.commit()
        logger.info(f"Password changed successfully for user: {user_id}")
    except Exception as e:
        logger.error(f"Password change failed for user {user_id}: {str(e)}")
        raise


def delete_user(user_id: int, db: DbSession):
    logger.info(f"Deleting user: {user_id}")
    try:
        # Note: This is a placeholder implementation
        logger.info(f"Successfully deleted user: {user_id}")
        return {"message": "User deleted successfully"}
    except Exception as e:
        logger.error(f"Failed to delete user {user_id}: {str(e)}")
        raise

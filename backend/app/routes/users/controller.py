from uuid import UUID

from database.core import DbSession
from db_services import users as users_db_service
from fastapi import APIRouter, status
from utils.logger import get_controller_logger

from routes.auth.service import CurrentUser
from routes.users import models, service

logger = get_controller_logger("users")
router = APIRouter(prefix="/users", tags=["Users"])


@router.get("/me", response_model=models.UserResponse)
def get_current_user(current_user: CurrentUser, db: DbSession):
    logger.info(f"Getting current user profile for user {current_user.get_uuid()}")
    try:
        result = users_db_service.get_user_by_id(db, current_user.get_uuid())
        logger.info(
            f"Successfully retrieved user profile for {current_user.get_uuid()}"
        )
        return result
    except Exception as e:
        logger.error(f"Failed to get current user profile: {str(e)}")
        raise


@router.put("/change-password", status_code=status.HTTP_200_OK)
def change_password(
    password_change: models.PasswordChange, db: DbSession, current_user: CurrentUser
):
    logger.info(f"Changing password for user {current_user.get_uuid()}")
    try:
        service.change_password(db, current_user.get_uuid(), password_change)
        logger.info(f"Successfully changed password for user {current_user.get_uuid()}")
    except Exception as e:
        logger.error(
            f"Failed to change password for user {current_user.get_uuid()}: {str(e)}"
        )
        raise


@router.delete("/{user_id}", status_code=status.HTTP_200_OK)
async def delete_user(user_id: UUID, db: DbSession):
    logger.info(f"Deleting user {user_id}")
    try:
        result = service.delete_user(user_id, db)
        logger.info(f"Successfully deleted user {user_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to delete user {user_id}: {str(e)}")
        raise

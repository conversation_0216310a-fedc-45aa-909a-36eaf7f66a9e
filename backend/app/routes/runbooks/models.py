from datetime import datetime
from typing import List, Optional
from uuid import UUID

from entities.runbooks import RunbookTypeEnum, StepStatusEnum
from pydantic import BaseModel, Field

from routes.users.models import UserResponse


class RunbookStepBase(BaseModel):
    step_order: int = Field(..., description="Order of the step in the runbook")
    title: str = Field(..., description="Title of the step")
    description: str = Field(..., description="Description of the step")
    details: str = Field(..., description="Detailed instructions for this step")
    expected_result: str = Field(..., description="Expected result of the step")
    status: Optional[StepStatusEnum] = Field(None, description="Status of the step")
    notes: Optional[str] = Field(None, description="Any additional notes")
    executed_at: Optional[datetime] = Field(
        None, description="When the step was executed"
    )
    executed_by_user: Optional[UserResponse] = Field(
        None, description="Who executed the step"
    )


class RunbookStepUpdate(BaseModel):
    status: Optional[StepStatusEnum] = Field(None, description="Status of the step")
    notes: Optional[str] = Field(None, description="Any additional notes")


class RunbookStepResponse(RunbookStepBase):
    id: UUID
    runbook_id: UUID
    created_at: Optional[datetime]

    class Config:
        from_attributes = True


class RunbookBase(BaseModel):
    title: str = Field(..., description="Runbook title")
    type: RunbookTypeEnum = Field(..., description="Runbook type")
    purpose: Optional[str] = None
    details: Optional[str] = None
    steps: Optional[List[RunbookStepBase]] = []


class RunbookCreate(RunbookBase):
    pass


class RunbookUpdate(RunbookBase):
    pass


class RunbookResponse(RunbookBase):
    id: UUID
    incident_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True

from datetime import datetime
from typing import List

from pydantic import BaseModel


class KeyPerformanceMetrics(BaseModel):
    mtbf: float
    mttd: float
    mtta: float
    mttr: float


class IncidentOverview(BaseModel):
    total_incidents: int
    open_incidents: int
    resolved_incidents: int
    critical_incidents: int


class TimeSeriesData(BaseModel):
    date: str
    count: int


class SeverityDistribution(BaseModel):
    critical: int
    high: int
    medium: int
    low: int


class ResolutionPerformance(BaseModel):
    resolution_rate: float
    critical_incident_rate: float
    avg_response_time: float
    avg_resolution_time: float
    avg_closure_time: float


class DashboardMetrics(BaseModel):
    key_performance: KeyPerformanceMetrics
    incident_overview: IncidentOverview
    incidents_over_time: List[TimeSeriesData]
    incidents_by_severity: SeverityDistribution
    resolution_performance: ResolutionPerformance
    last_updated: datetime

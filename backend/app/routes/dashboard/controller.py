from datetime import datetime
from typing import Optional

from database.core import DbSession
from fastapi import APIRouter, HTTPException, Query
from routes.auth.service import CurrentUser
from routes.dashboard import service
from routes.dashboard.models import (
    DashboardMetrics,
    IncidentOverview,
    KeyPerformanceMetrics,
    ResolutionPerformance,
    SeverityDistribution,
    TimeSeriesData,
)
from utils.logger import get_controller_logger

logger = get_controller_logger("dashboard")
router = APIRouter(prefix="/dashboard", tags=["Dashboard"])


@router.get("/metrics", response_model=DashboardMetrics)
async def get_dashboard_metrics(
    db: DbSession,
    current_user: CurrentUser,
    start: Optional[datetime] = Query(None),
    end: Optional[datetime] = Query(None),
):
    logger.info("Getting all dashboard metrics")
    try:
        result = service.get_dashboard_metrics(db, start, end)
        logger.info("Successfully retrieved dashboard metrics")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get dashboard metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard metrics")


@router.get("/key-performance", response_model=KeyPerformanceMetrics)
async def get_key_performance_metrics(
    db: DbSession,
    current_user: CurrentUser,
    start: Optional[datetime] = Query(None),
    end: Optional[datetime] = Query(None),
):
    logger.info("Getting key performance metrics")
    try:
        result = service.get_key_performance_metrics(db, start, end)
        logger.info("Successfully retrieved key performance metrics")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get key performance metrics: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to get key performance metrics"
        )


@router.get("/incident-overview", response_model=IncidentOverview)
async def get_incident_overview(
    db: DbSession,
    current_user: CurrentUser,
    start: Optional[datetime] = Query(None),
    end: Optional[datetime] = Query(None),
):
    logger.info("Getting incident overview")
    try:
        result = service.get_incident_overview(db, start, end)
        logger.info("Successfully retrieved incident overview")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get incident overview: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get incident overview")


@router.get("/incidents-over-time", response_model=list[TimeSeriesData])
async def get_incidents_over_time(
    db: DbSession,
    current_user: CurrentUser,
    start: Optional[datetime] = Query(None),
    end: Optional[datetime] = Query(None),
):
    logger.info("Getting incidents over time")
    try:
        result = service.get_incidents_over_time(db, start, end)
        logger.info("Successfully retrieved incidents over time")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get incidents over time: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get incidents over time")


@router.get("/incidents-by-severity", response_model=SeverityDistribution)
async def get_incidents_by_severity(
    db: DbSession,
    current_user: CurrentUser,
    start: Optional[datetime] = Query(None),
    end: Optional[datetime] = Query(None),
):
    logger.info("Getting incidents by severity")
    try:
        result = service.get_incidents_by_severity(db, start, end)
        logger.info("Successfully retrieved incidents by severity")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get incidents by severity: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to get incidents by severity"
        )


@router.get("/resolution-performance", response_model=ResolutionPerformance)
async def get_resolution_performance(
    db: DbSession,
    current_user: CurrentUser,
    start: Optional[datetime] = Query(None),
    end: Optional[datetime] = Query(None),
):
    logger.info("Getting resolution performance metrics")
    try:
        result = service.get_resolution_performance(db, start, end)
        logger.info("Successfully retrieved resolution performance metrics")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get resolution performance metrics: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to get resolution performance metrics"
        )

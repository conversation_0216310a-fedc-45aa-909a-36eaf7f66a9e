from datetime import datetime, timedelta, timezone
from typing import List, Optional

from database.core import DbSession
from entities.incident import Incident, SeverityEnum
from entities.incident_metrics import IncidentMetric
from fastapi import HTTPException, status
from routes.dashboard.models import (
    DashboardMetrics,
    IncidentOverview,
    KeyPerformanceMetrics,
    ResolutionPerformance,
    SeverityDistribution,
    TimeSeriesData,
)
from sqlalchemy import func
from utils.logger import get_service_logger

logger = get_service_logger("dashboard")


def get_dashboard_metrics(
    db: DbSession, start: Optional[datetime] = None, end: Optional[datetime] = None
) -> DashboardMetrics:
    """
    Calculate all dashboard metrics from incidents and events, with optional time window.
    """
    try:
        key_performance = get_key_performance_metrics(db, start, end)

        incident_overview = get_incident_overview(db)

        incidents_over_time = get_incidents_over_time(db)

        incidents_by_severity = get_incidents_by_severity(db)

        resolution_performance = get_resolution_performance(db)

        return DashboardMetrics(
            key_performance=key_performance,
            incident_overview=incident_overview,
            incidents_over_time=incidents_over_time,
            incidents_by_severity=incidents_by_severity,
            resolution_performance=resolution_performance,
            last_updated=datetime.now(timezone.utc),
        )

    except Exception as e:
        logger.error(f"Error calculating dashboard metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate dashboard metrics",
        )


def get_key_performance_metrics(
    db: DbSession, start: Optional[datetime] = None, end: Optional[datetime] = None
) -> KeyPerformanceMetrics:
    """Calculate key performance metrics with optional time window using IncidentMetric fields."""
    try:
        incident_query = db.query(Incident)
        if start:
            incident_query = incident_query.filter(Incident.reported_at >= start)
        if end:
            incident_query = incident_query.filter(Incident.reported_at <= end)
        incidents = incident_query.all()
        total_incidents = len(incidents)
        if total_incidents == 0:
            return KeyPerformanceMetrics(mtbf=0, mttd=0, mtta=0, mttr=0)
        incident_ids = [i.id for i in incidents]
        # Use SQL aggregation for averages
        metrics_query = db.query(
            func.avg(func.extract("epoch", IncidentMetric.time_to_acknowledge) / 60),
            func.avg(func.extract("epoch", IncidentMetric.time_to_resolve) / 60),
            func.avg(func.extract("epoch", IncidentMetric.time_to_report) / 60),
        ).filter(IncidentMetric.incident_id.in_(incident_ids))
        mtta_avg, mttr_avg, mttd_avg = metrics_query.one()
        mtta_avg = mtta_avg or 0
        mttr_avg = mttr_avg or 0
        mttd_avg = mttd_avg or 0
        # MTBF
        if total_incidents > 1:
            sorted_incidents = sorted(
                incidents, key=lambda x: getattr(x, "reported_at")
            )
            total_time = (
                sorted_incidents[-1].reported_at - sorted_incidents[0].reported_at
            ).total_seconds() / 60
            mtbf = total_time / (total_incidents - 1)
        else:
            mtbf = 0
        return KeyPerformanceMetrics(
            mtbf=mtbf, mttd=mttd_avg, mtta=mtta_avg, mttr=mttr_avg
        )
    except Exception as e:
        logger.error(f"Error calculating key performance metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate key performance metrics",
        )


def get_incident_overview(db: DbSession) -> IncidentOverview:
    """Calculate incident overview metrics."""
    try:
        total_incidents = db.query(Incident).count()
        open_incidents = (
            db.query(Incident).filter(Incident.status.in_(["OPEN", "ACTIVE"])).count()
        )
        resolved_incidents = (
            db.query(Incident)
            .filter(Incident.status.in_(["RESOLVED", "CLOSED"]))
            .count()
        )
        critical_incidents = (
            db.query(Incident)
            .filter(Incident.severity == SeverityEnum.CRITICAL)
            .count()
        )

        return IncidentOverview(
            total_incidents=total_incidents,
            open_incidents=open_incidents,
            resolved_incidents=resolved_incidents,
            critical_incidents=critical_incidents,
        )

    except Exception as e:
        logger.error(f"Error calculating incident overview: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate incident overview",
        )


def get_incidents_over_time(db: DbSession) -> List[TimeSeriesData]:
    """Calculate incidents over time for the last 30 days."""
    try:
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)

        daily_counts = (
            db.query(
                func.date(Incident.reported_at).label("date"),
                func.count(Incident.id).label("count"),
            )
            .filter(Incident.reported_at >= thirty_days_ago)
            .group_by(func.date(Incident.reported_at))
            .all()
        )

        return [
            TimeSeriesData(date=count.date.strftime("%Y-%m-%d"), count=count.count)
            for count in daily_counts
        ]

    except Exception as e:
        logger.error(f"Error calculating incidents over time: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate incidents over time",
        )


def get_incidents_by_severity(db: DbSession) -> SeverityDistribution:
    """Calculate incident distribution by severity."""
    try:
        critical = (
            db.query(Incident)
            .filter(Incident.severity == SeverityEnum.CRITICAL)
            .count()
        )
        high = db.query(Incident).filter(Incident.severity == SeverityEnum.HIGH).count()
        medium = (
            db.query(Incident).filter(Incident.severity == SeverityEnum.MEDIUM).count()
        )
        low = db.query(Incident).filter(Incident.severity == SeverityEnum.LOW).count()

        return SeverityDistribution(
            critical=critical, high=high, medium=medium, low=low
        )

    except Exception as e:
        logger.error(f"Error calculating incidents by severity: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate incidents by severity",
        )


def get_resolution_performance(db: DbSession) -> ResolutionPerformance:
    """Calculate resolution performance metrics."""
    try:
        total_incidents = db.query(Incident).count()
        resolved_incidents = (
            db.query(Incident)
            .filter(Incident.status.in_(["RESOLVED", "CLOSED"]))
            .count()
        )
        critical_incidents = (
            db.query(Incident)
            .filter(Incident.severity == SeverityEnum.CRITICAL)
            .count()
        )
        resolution_rate = (
            (resolved_incidents / total_incidents * 100) if total_incidents > 0 else 0
        )
        critical_incident_rate = (
            (critical_incidents / total_incidents * 100) if total_incidents > 0 else 0
        )
        # Use SQL aggregation for averages
        metrics_query = db.query(
            func.avg(func.extract("epoch", IncidentMetric.time_to_acknowledge) / 60),
            func.avg(func.extract("epoch", IncidentMetric.time_to_resolve) / 60),
            func.avg(func.extract("epoch", IncidentMetric.time_to_closure) / 60),
        )
        avg_response_time, avg_resolution_time, avg_closure_time = metrics_query.one()
        avg_response_time = avg_response_time or 0
        avg_resolution_time = avg_resolution_time or 0
        avg_closure_time = avg_closure_time or 0
        return ResolutionPerformance(
            resolution_rate=resolution_rate,
            critical_incident_rate=critical_incident_rate,
            avg_response_time=avg_response_time,
            avg_resolution_time=avg_resolution_time,
            avg_closure_time=avg_closure_time,
        )
    except Exception as e:
        logger.error(f"Error calculating resolution performance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate resolution performance",
        )

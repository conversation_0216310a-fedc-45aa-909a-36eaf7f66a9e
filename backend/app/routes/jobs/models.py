from datetime import datetime
from typing import List, Optional
from uuid import UUID

from entities.job import JobStatusEnum, JobTypeEnum
from pydantic import BaseModel


class JobBase(BaseModel):
    job_id: UUID
    job_type: JobTypeEnum
    status: JobStatusEnum
    message: Optional[str] = None


class JobCreate(JobBase):
    pass


class JobResponse(JobBase):
    created_at: datetime
    updated_at: datetime
    created_by_user_id: UUID

    class Config:
        from_attributes = True


class PaginatedResponse(BaseModel):
    items: List[JobResponse]
    total: int
    offset: int
    limit: int


class JobStatusResponse(BaseModel):
    status: JobStatusEnum
    message: Optional[str] = None

from celery.result import AsyncResult
from database.core import DbSession
from db_services import jobs as jobs_db_service
from entities.job import Job, JobStatusEnum
from fastapi import HTTPException, status
from routes.auth.service import CurrentUser
from routes.jobs.models import JobResponse, JobStatusResponse, PaginatedResponse
from sqlalchemy import func
from utils.celery_worker import celery_app
from utils.logger import get_service_logger

logger = get_service_logger("jobs")


def get_job_status(
    db: DbSession, current_user: CurrentUser, job_id: str
) -> JobStatusResponse:
    """
    Get the status and result of a Celery job.

    Args:
        db: Database session
        current_user: Current authenticated user
        job_id: The ID of the Celery job to check

    Returns:
        JobStatusResponse with updated status and message

    Raises:
        HTTPException: If job is not found or there's an error checking the status
    """
    logger.info(f"Checking status for job: {job_id}")

    try:
        job = jobs_db_service.get_job_by_job_id(db, job_id)
        if not job:
            logger.warning(f"Job not found: {job_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Job not found"
            )

        if job.status in {JobStatusEnum.STARTED, JobStatusEnum.PENDING}:
            task_result: AsyncResult = AsyncResult(job_id, app=celery_app)
            logger.info(f"Task status for job {job_id}: {task_result.status}")
            logger.debug(f"Task info for job {job_id}: {task_result.info}")

            if task_result.ready():
                job.status = (
                    JobStatusEnum.SUCCESS
                    if task_result.status == "SUCCESS"
                    else JobStatusEnum.FAILURE
                )
                job.message = str(task_result.result)[:200]
            else:
                # Update status for running jobs
                job.status = JobStatusEnum.__members__.get(
                    task_result.status, JobStatusEnum.PENDING
                )

                # Save progress information as status message if job is still running
                if task_result.status in ["STARTED", "PENDING", "RETRY"]:
                    progress_message = f"Task {task_result.status.lower()}"  # Default

                    # Check if task.info contains progress information
                    if (
                        isinstance(task_result.info, dict)
                        and "status" in task_result.info
                    ):
                        progress_message = task_result.info["status"]
                    elif isinstance(task_result.info, str):
                        progress_message = task_result.info

                    job.message = progress_message[:200]  # Limit message length
                    logger.info(
                        f"Updated progress for job {job_id}: {progress_message}"
                    )

            db.commit()
            db.refresh(job)

        return JobStatusResponse(status=job.status, message=job.message)

    except Exception as e:
        logger.error(f"Failed to get status for job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get job status: {str(e)}",
        )


def create_job(db: DbSession, current_user: CurrentUser, job_data: dict) -> Job:
    logger.info(f"Creating job for user {current_user.get_uuid()}")
    try:
        job = Job(
            job_id=job_data["job_id"],
            job_type=job_data["job_type"],
            status=job_data["status"],
            created_by_user_id=current_user.get_uuid(),
        )
        db.add(job)
        db.commit()
        db.refresh(job)
        logger.info(f"Successfully created job with job_id: {job.job_id}")
        return job
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create job",
        )


def list_jobs(
    db: DbSession, current_user: CurrentUser, offset: int = 0, limit: int = 10
) -> PaginatedResponse:
    logger.info("Listing jobs")
    try:
        # Get total count
        total = db.query(func.count(Job.job_id)).scalar()

        # Get paginated jobs
        jobs = (
            db.query(Job)
            .order_by(Job.created_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )

        logger.info(f"Successfully listed {len(jobs)} jobs out of {total} total")

        return PaginatedResponse(
            items=[JobResponse.model_validate(job) for job in jobs],
            total=total,
            offset=offset,
            limit=limit,
        )
    except Exception as e:
        logger.error(f"Failed to list jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list jobs",
        )


def list_running_jobs(db: DbSession, current_user: CurrentUser) -> PaginatedResponse:
    logger.info("Listing running jobs")
    try:
        # Get total count of running jobs
        total = (
            db.query(func.count(Job.job_id))
            .filter(Job.status.in_([JobStatusEnum.STARTED, JobStatusEnum.PENDING]))
            .scalar()
        )

        # Get all running jobs
        jobs = (
            db.query(Job)
            .filter(Job.status.in_([JobStatusEnum.STARTED, JobStatusEnum.PENDING]))
            .order_by(Job.created_at.desc())
            .all()
        )

        logger.info(
            f"Successfully listed {len(jobs)} running jobs out of {total} total"
        )

        return PaginatedResponse(
            items=[JobResponse.model_validate(job) for job in jobs],
            total=total,
            offset=0,
            limit=total,
        )
    except Exception as e:
        logger.error(f"Failed to list running jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list running jobs",
        )

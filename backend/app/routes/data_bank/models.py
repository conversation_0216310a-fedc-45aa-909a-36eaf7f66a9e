from typing import Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class SimilarityResult(BaseModel):
    """
    Represents a similarity search result with payload and score.
    """

    payload: Dict = Field(
        ...,
        description="The metadata payload of the similar item (incident or document).",
    )
    score: float = Field(..., description="The similarity score of the result.")


class SimilaritySearchResponse(BaseModel):
    """
    Legacy response model for backward compatibility.
    """

    similar_incidents: List[SimilarityResult] = Field(
        ..., description="A list of similar incidents."
    )


class CombinedSimilarityResponse(BaseModel):
    """
    Combined response model for similarity search across both collections.
    Returns both similar incidents and relevant documentations.
    """

    similar_incidents: List[SimilarityResult] = Field(
        ..., description="A list of similar incidents with their similarity scores."
    )
    relevant_documentations: List[SimilarityResult] = Field(
        ..., description="A list of relevant documents with their similarity scores."
    )


class TextSearchRequest(BaseModel):
    """
    Request model for text-based similarity search.
    """

    text: str = Field(
        ...,
        min_length=1,
        description="The text to search for similar incidents and documents. Can be a title, description, summary, logs, or any content.",
    )
    top_k: int = Field(
        default=5,
        ge=1,
        le=50,
        description="The maximum number of similar items to return from each collection (1-50).",
    )
    exclude_ids: Optional[List[UUID]] = Field(
        default=None,
        description="List of IDs to exclude from search results.",
    )


class DocumentData(BaseModel):
    """
    Model for document data used in vector operations.
    """

    document_id: UUID = Field(..., description="The unique identifier of the document.")

import random
import tempfile
import time
from datetime import datetime, timezone
from typing import List, Optional, Tuple
from uuid import UUID

from database.core import DbSession
from db_services import incident as incident_db_service
from entities.incident import Incident, IncidentDetail, StatusEnum
from fastapi import HTTPException, status
from markdown_pdf import MarkdownPdf, Section
from utils.logger import get_service_logger
from vector_db.search_service import VectorSearchService

from routes.agents.service import (
    handle_reporter_agent_request,
    handle_summary_agent_request,
)
from routes.auth.service import CurrentUser
from routes.incidents.models import (
    IncidentA<PERSON>nalysisRead,
    IncidentCreate,
    IncidentDetailsRead,
    IncidentDetailsUpdate,
    IncidentFilter,
    IncidentRead,
    IncidentUpdate,
    SimilarIncidentRead,
    SimilarIncidentsResponse,
)

logger = get_service_logger("incidents")


def generate_incident_number() -> str:
    """Generate a unique incident number."""
    return f"INC-{int(time.time())}-{random.randint(1000, 9999)}"


def create_incident(
    db: DbSession, incident_data: IncidentCreate, user_id: Optional[UUID] = None
) -> Incident:
    """Create a new incident and its details."""
    logger.info(f"Creating incident with title: {incident_data.title}")
    try:
        incident_number = incident_data.incident_number or generate_incident_number()

        incident = Incident(
            incident_number=incident_number,
            title=incident_data.title,
            summary=incident_data.summary,
            priority=incident_data.priority,
            severity=incident_data.severity,
            incident_type=incident_data.incident_type,
            status=StatusEnum.OPEN,
            reported_by=user_id,
            reported_at=datetime.now(timezone.utc),
        )
        db.add(incident)
        db.flush()  # get incident.id
        detail = IncidentDetail(
            incident_id=incident.id,
            affected_services=incident_data.affected_services or [],
            tags=incident_data.tags or [],
            incident_details=incident_data.incident_details,
            attachments=incident_data.attachments or [],
        )
        db.add(detail)
        db.flush()
        db.commit()
        db.refresh(incident)
        logger.info(f"Created incident {incident.incident_number}")

        # Trigger async embedding task (non-blocking)
        try:
            from tasks.vector_db import upsert_incident_embedding_task

            # Prepare incident data for embedding - ensure we have meaningful content
            # Queue the async embedding task with just the incident ID
            task = upsert_incident_embedding_task.delay(str(incident.id))
            logger.info(
                f"Triggered async embedding task {task.id} for incident {incident.incident_number}"
            )

        except Exception as e:
            # Log but don't fail the main operation - embedding is not critical for incident creation
            logger.warning(
                f"Failed to trigger embedding task for incident {incident.incident_number}: {e}"
            )

        return incident

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create incident '{incident_data.title}': {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create incident: {e}",
        )


def get_incident(
    db: DbSession,
    incident_id: Optional[UUID] = None,
    incident_number: Optional[str] = None,
) -> IncidentRead:
    """Get an incident by ID or incident number."""
    if incident_id:
        logger.info(f"Getting incident by ID: {incident_id}")
        incident = incident_db_service.get_incident_by_id(db, incident_id)
    elif incident_number:
        logger.info(f"Getting incident by number: {incident_number}")
        incident = incident_db_service.get_incident_by_number(db, incident_number)
    else:
        raise ValueError("Must specify either incident_id or incident_number")
    if not incident:
        logger.warning(f"Incident not found: {incident_id or incident_number}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
        )
    return IncidentRead.model_validate(incident)


def get_incident_details(
    db: DbSession, incident_id: UUID
) -> Optional[IncidentDetailsRead]:
    """Retrieve details for a specific incident."""
    logger.info(f"Retrieving incident details for incident: {incident_id}")
    detail = incident_db_service.get_incident_details(db, incident_id)
    if not detail:
        logger.warning(f"Incident details not found for incident: {incident_id}")
        return None
    logger.info(f"Successfully retrieved incident details for: {incident_id}")
    return IncidentDetailsRead.model_validate(detail)


def get_incident_ai_analysis(
    db: DbSession, incident_id: UUID
) -> Optional[IncidentAIAnalysisRead]:
    """Retrieve AI analysis for a specific incident."""
    logger.info(f"Retrieving AI analysis for incident: {incident_id}")
    analysis_data = incident_db_service.get_incident_ai_analysis(db, incident_id)
    if not analysis_data:
        logger.warning(f"AI analysis not found for incident: {incident_id}")
        return None

    # Add incident_id to the data for the model
    analysis_data["incident_id"] = incident_id
    logger.info(f"Successfully retrieved AI analysis for: {incident_id}")
    return IncidentAIAnalysisRead.model_validate(analysis_data)


def get_similar_incidents(
    db: DbSession, incident_id: UUID, top_k: int = 5
) -> SimilarIncidentsResponse:
    """Find similar incidents using vector similarity search."""
    logger.info(f"Finding similar incidents for incident: {incident_id}")

    try:
        # Use the common vector search service
        vector_service = VectorSearchService()
        similar_incident_data = vector_service.search_similar_incidents_by_id(
            db, incident_id=incident_id, top_k=top_k
        )

        # Convert to response format
        similar_incidents = []
        for item in similar_incident_data:
            incident_read = IncidentRead.model_validate(item["incident"])
            similar_incidents.append(
                SimilarIncidentRead(
                    incident=incident_read, similarity_score=item["similarity_score"]
                )
            )

        logger.info(
            f"Found {len(similar_incidents)} similar incidents for {incident_id}"
        )
        return SimilarIncidentsResponse(
            similar_incidents=similar_incidents, total_found=len(similar_incidents)
        )

    except ValueError as e:
        logger.warning(f"Invalid request for similar incidents {incident_id}: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to find similar incidents for {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to find similar incidents",
        )


def update_incident(
    db: DbSession, incident_id: UUID, incident_data: IncidentUpdate
) -> IncidentRead:
    """Update an incident and its details."""
    logger.info(f"Updating incident: {incident_id}")

    incident = incident_db_service.get_incident_by_id(db, incident_id)
    if not incident:
        logger.warning(f"Incident not found for update: {incident_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
        )

    update_data = incident_data.model_dump(exclude_unset=True)

    try:
        for field in ["title", "priority", "severity", "incident_type", "status"]:
            if field in update_data:
                setattr(incident, field, update_data[field])

        # Update or create incident details
        detail = incident.incident_detail
        if not detail:
            detail = IncidentDetail(incident_id=incident.id)
            db.add(detail)

        # Update detail fields
        for field in ["affected_services", "tags", "attachments"]:
            if field in update_data:
                setattr(detail, field, update_data[field])

        if "incident_details" in update_data and update_data["incident_details"]:
            detail.incident_details = update_data["incident_details"]

        db.commit()
        db.refresh(incident)
        logger.info(f"Successfully updated incident {incident_id}")

        # Trigger async embedding upsert task (non-blocking) - upsert handles both updates and inserts
        try:
            from tasks.vector_db import upsert_incident_embedding_task

            # Queue the async embedding upsert task with just the incident ID
            task = upsert_incident_embedding_task.delay(str(incident.id))
            logger.info(
                f"Triggered async embedding upsert task {task.id} for incident {incident_id}"
            )

        except Exception as e:
            # Log but don't fail the main operation - embedding upsert is not critical
            logger.warning(
                f"Failed to trigger embedding upsert task for incident {incident_id}: {e}"
            )

        return IncidentRead.model_validate(incident)

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update incident: {e}",
        )


async def update_incident_details(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    details_data: IncidentDetailsUpdate,
) -> Optional[IncidentDetailsRead]:
    """Update details for a specific incident."""
    logger.info(f"Updating incident details for incident: {incident_id}")

    detail = incident_db_service.get_incident_details(db, incident_id)
    if not detail:
        logger.warning(f"Incident details not found for incident: {incident_id}")
        return None

    incident = incident_db_service.get_incident_by_id(db, incident_id)
    if not incident:
        logger.warning(f"Incident not found for incident: {incident_id}")
        return None

    update_data = details_data.model_dump(exclude_unset=True)
    should_regenerate_summary = False

    try:
        # Update detail fields
        for field, value in update_data.items():
            setattr(detail, field, value)
            if field == "incident_details":
                should_regenerate_summary = True

        # Commit detail changes first
        db.commit()
        db.refresh(detail)

        # Regenerate summary if needed (this handles its own transaction)
        if should_regenerate_summary:
            new_summary = await handle_summary_agent_request(db, current_user, incident)
            incident_db_service.save_incident_summary(db, incident_id, new_summary)

        logger.info(f"Successfully updated incident details for: {incident_id}")

        # Trigger async embedding upsert task when incident details change (non-blocking)
        try:
            from tasks.vector_db import upsert_incident_embedding_task

            # Prepare updated incident data for embedding
            # Queue the async embedding upsert task with just the incident ID
            task = upsert_incident_embedding_task.delay(str(incident_id))
            logger.info(
                f"Triggered async embedding upsert task {task.id} for incident details update {incident_id}"
            )

        except Exception as e:
            # Log but don't fail the main operation - embedding upsert is not critical
            logger.warning(
                f"Failed to trigger embedding upsert task for incident details {incident_id}: {e}"
            )

        return IncidentDetailsRead.model_validate(detail)

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update incident details for {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update incident details: {e}",
        )


def delete_incident(db: DbSession, incident_id: UUID) -> dict:
    """
    Delete an incident and automatically clean up its vector embedding.

    This function deletes the incident from the main database and then automatically
    removes the corresponding vector embedding from the Qdrant vector database.
    If embedding deletion fails, it logs a warning but doesn't rollback the main deletion.
    """
    logger.info(f"Deleting incident: {incident_id}")

    incident = incident_db_service.get_incident_by_id(db, incident_id)
    if not incident:
        logger.warning(f"Incident not found for deletion: {incident_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
        )

    try:
        # Primary operation: Delete incident from main database
        db.delete(incident)
        db.commit()
        logger.info(f"Successfully deleted incident from database: {incident_id}")

        # Secondary operation: Delete embedding from vector database (non-critical)
        try:
            # Import here to avoid circular imports
            from routes.data_bank.service import (
                delete_incident as delete_incident_embedding,
            )

            embedding_deleted = delete_incident_embedding(incident_id)
            if embedding_deleted:
                logger.info(
                    f"Successfully deleted vector embedding for incident: {incident_id}"
                )
            else:
                logger.warning(
                    f"Vector embedding deletion returned false for incident: {incident_id}"
                )

        except ImportError:
            logger.warning(
                f"Vector DB service not available for embedding cleanup for incident: {incident_id}"
            )
        except Exception as e:
            # Log warning but don't fail the main deletion - embedding cleanup is secondary
            logger.warning(
                f"Failed to delete vector embedding for incident {incident_id}: {e}"
            )

        logger.info(f"Incident deletion completed for: {incident_id}")
        return {"message": f"Incident {incident_id} deleted successfully"}

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to delete incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete incident: {str(e)}",
        )


def get_incidents(
    db: DbSession,
    offset: int = 0,
    limit: int = 10,
    filters: Optional[IncidentFilter] = None,
) -> Tuple[List[IncidentRead], int]:
    """Get paginated list of incidents with optional filters."""
    logger.debug(f"Getting incidents with offset={offset}, limit={limit}")
    query = db.query(Incident)
    if filters:
        filter_data = filters.model_dump(exclude_unset=True)
        logger.debug(f"Applying filters: {filter_data}")
        for key, value in filter_data.items():
            if value is not None:
                query = query.filter(getattr(Incident, key) == value)
    total = query.count()
    incidents = (
        query.order_by(Incident.reported_at.desc()).offset(offset).limit(limit).all()
    )
    incident_list = [IncidentRead.model_validate(incident) for incident in incidents]
    logger.info(f"Retrieved {len(incident_list)} incidents out of {total} total")
    return incident_list, total


async def regenerate_incident_summary(
    db: DbSession, incident_id: UUID, current_user: CurrentUser
) -> IncidentRead:
    """Regenerate the summary for an incident using AI-powered summary agent."""
    logger.info(f"Regenerating AI summary for incident: {incident_id}")

    incident = incident_db_service.get_incident_by_id(db, incident_id)
    if not incident:
        logger.warning(f"Incident not found for summary regeneration: {incident_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
        )

    try:
        # Generate new AI-powered summary
        new_summary = await handle_summary_agent_request(db, current_user, incident)

        # Save the new summary using the database service
        updated_incident = incident_db_service.save_incident_summary(
            db, incident_id, new_summary
        )

        logger.info(f"Successfully regenerated AI summary for incident {incident_id}")
        return IncidentRead.model_validate(updated_incident)

    except Exception as e:
        db.rollback()
        logger.error(
            f"Failed to regenerate summary for incident {incident_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate summary: {e}",
        )


async def generate_incident_pdf(
    db: DbSession, current_user: CurrentUser, incident_id: UUID
) -> str:
    md_content = await handle_reporter_agent_request(db, current_user, incident_id)
    css = """
    body { font-family: Arial, sans-serif; }
    h1, h2, h3 { color: #2c3e50; }
    table, th, td { border: 1px solid black; border-collapse: collapse; }
    th, td { padding: 6px; }
    """
    pdf = MarkdownPdf(toc_level=2, optimize=True)
    pdf.add_section(Section(md_content), user_css=css)
    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmpfile:
        pdf.save(tmpfile.name)
        pdf_path = tmpfile.name
    return pdf_path

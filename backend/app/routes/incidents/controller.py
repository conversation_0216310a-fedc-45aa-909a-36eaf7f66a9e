from typing import Optional
from uuid import UUID

from database.core import DbSession
from db_services import incident as incident_db_service
from fastapi import APIRouter, HTTPException, Query, status
from fastapi.responses import FileResponse
from utils.logger import get_controller_logger

from routes.agents.service import handle_root_cause_agent_request
from routes.auth.service import CurrentUser
from routes.incidents import models, service
from routes.incidents.models import IncidentFilter

logger = get_controller_logger("incidents")

router = APIRouter(prefix="/incidents", tags=["Incidents"])


@router.get("/", response_model=models.PaginatedIncidentResponse)
def get_incidents(
    db: DbSession,
    current_user: CurrentUser,
    offset: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    incident_type: Optional[models.IncidentTypeEnum] = None,
    priority: Optional[models.PriorityEnum] = None,
    severity: Optional[models.SeverityEnum] = None,
    status: Optional[models.StatusEnum] = None,
    reporter: Optional[str] = None,
):
    """Get a paginated list of incidents with optional filters."""
    logger.debug(
        f"User {current_user.get_uuid()} requesting incidents (offset={offset}, limit={limit})"
    )

    filters = IncidentFilter(
        incident_type=incident_type,
        priority=priority,
        severity=severity,
        status=status,
        reporter=reporter,
    )
    try:
        incidents, total = service.get_incidents(db, offset, limit, filters)
        pages = (total + limit - 1) // limit if limit > 0 else 0
        page = (offset // limit) + 1 if limit > 0 else 1
        logger.info(
            f"Retrieved {len(incidents)}/{total} incidents for user {current_user.get_uuid()}"
        )
        return models.PaginatedIncidentResponse(
            items=incidents,
            total=total,
            page=page,
            limit=limit,
            pages=pages,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to get incidents for user {current_user.get_uuid()}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Failed to get incidents.")


@router.post(
    "/create", response_model=models.IncidentRead, status_code=status.HTTP_201_CREATED
)
def create_incident(
    incident_data: models.IncidentCreate, db: DbSession, current_user: CurrentUser
):
    """Create a new incident."""
    logger.info(f"Creating incident '{incident_data.title}'")

    try:
        result = service.create_incident(db, incident_data, current_user.get_uuid())
        logger.info(f"Created incident {result.incident_number}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create incident '{incident_data.title}': {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create incident.",
        )


@router.get("/{value}", response_model=models.IncidentRead)
def get_incident(value: str, db: DbSession, current_user: CurrentUser):
    """Get an incident by ID or incident number."""
    logger.debug(f"Getting incident by {value}")
    try:
        incident_id = UUID(value)
        return service.get_incident(db, incident_id=incident_id)
    except ValueError:
        return service.get_incident(db, incident_number=value)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get incident by {value}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get incident.",
        )


@router.get("/{incident_id}/details", response_model=models.IncidentDetailsRead)
def get_incident_details(incident_id: UUID, db: DbSession, current_user: CurrentUser):
    """Get details for a specific incident."""
    try:
        details = service.get_incident_details(db, incident_id)
        if not details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Incident details not found",
            )
        return details
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get incident details {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get incident details.",
        )


@router.get("/{incident_id}/ai-analysis", response_model=models.IncidentAIAnalysisRead)
async def get_incident_ai_analysis(
    incident_id: UUID,
    db: DbSession,
    current_user: CurrentUser,
):
    """Get AI analysis for a specific incident."""
    logger.info(f"Retrieving AI analysis for incident {incident_id}")

    try:
        # Try to get existing analysis first
        analysis = service.get_incident_ai_analysis(db, incident_id)
        if analysis:
            return analysis

        logger.info(f"Generating AI analysis for incident {incident_id}")
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Incident not found",
            )

        # Generate and validate analysis
        analysis_data = await handle_root_cause_agent_request(
            db, current_user, incident
        )
        if "raw_response" in analysis_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to parse AI analysis response.",
            )

        incident_db_service.save_incident_ai_analysis(db, incident_id, analysis_data)

        analysis = models.IncidentAIAnalysisRead(
            incident_id=incident_id,
            root_cause=analysis_data.get("root_cause"),
            immediate_action=analysis_data.get("immediate_action"),
            impact_forecast=analysis_data.get("impact_forecast"),
            cascading_risks=analysis_data.get("cascading_risks"),
        )
        logger.info(f"Successfully generated AI analysis for incident {incident_id}")
        return analysis
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get AI analysis for incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get AI analysis.",
        )


@router.get("/{incident_id}/similar", response_model=models.SimilarIncidentsResponse)
def get_similar_incidents(
    incident_id: UUID,
    db: DbSession,
    current_user: CurrentUser,
    top_k: int = Query(
        5, ge=1, le=20, description="Number of similar incidents to return"
    ),
):
    """Get similar incidents using AI-powered vector similarity search."""
    logger.info(f"Finding {top_k} similar incidents for incident {incident_id}")

    try:
        result = service.get_similar_incidents(db, incident_id, top_k)
        logger.info(
            f"Successfully found {result.total_found} similar incidents for incident {incident_id}"
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get similar incidents for {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get similar incidents.",
        )


@router.put("/{incident_id}", response_model=models.IncidentRead)
def update_incident(
    incident_id: UUID,
    incident_data: models.IncidentUpdate,
    db: DbSession,
    current_user: CurrentUser,
):
    """Update an incident's metadata."""
    logger.info(f"Updating incident {incident_id}")
    try:
        result = service.update_incident(db, incident_id, incident_data)
        logger.info(f"Updated incident {incident_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update incident.",
        )


@router.put("/{incident_id}/details", response_model=models.IncidentDetailsRead)
async def update_incident_details(
    incident_id: UUID,
    details_data: models.IncidentDetailsUpdate,
    db: DbSession,
    current_user: CurrentUser,
):
    """Update details for a specific incident."""
    try:
        updated = await service.update_incident_details(
            db, current_user, incident_id, details_data
        )
        if not updated:
            logger.warning(f"Incident details not found for update: {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Incident details not found or not updated",
            )
        return updated
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update incident details {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update incident details.",
        )


@router.delete("/{incident_id}", status_code=status.HTTP_200_OK)
def delete_incident(incident_id: UUID, db: DbSession, current_user: CurrentUser):
    """
    Delete an incident and automatically clean up its vector embedding.

    This endpoint deletes the specified incident from the database and automatically
    removes the corresponding vector embedding from the Qdrant vector database.

    The deletion process follows a two-phase approach:
    1. Primary: Delete incident from the main database (critical operation)
    2. Secondary: Delete vector embedding from Qdrant (cleanup operation)

    If the embedding deletion fails, a warning is logged but the main deletion
    is not rolled back, ensuring data consistency while providing best-effort cleanup.

    Args:
        incident_id: The unique identifier of the incident to delete

    Returns:
        dict: Success message indicating the incident was deleted

    Raises:
        404: If the incident is not found
        500: If the main database deletion fails
    """
    logger.info(f"Deleting incident {incident_id}")
    try:
        result = service.delete_incident(db, incident_id)
        logger.info(f"Deleted incident {incident_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete incident.",
        )


@router.post("/{incident_id}/regenerate-summary", response_model=models.IncidentRead)
async def regenerate_incident_summary(
    incident_id: UUID, db: DbSession, current_user: CurrentUser
):
    """Regenerate the summary for an incident using AI-powered summary agent."""
    logger.info(f"Regenerating AI summary for incident {incident_id}")
    try:
        result = await service.regenerate_incident_summary(
            db, incident_id, current_user
        )
        logger.info(f"Successfully regenerated AI summary for incident {incident_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to regenerate summary for incident {incident_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to regenerate summary.",
        )


@router.get("/{incident_id}/generate_pdf")
async def generate_incident_pdf_endpoint(
    incident_id: UUID, db: DbSession, current_user: CurrentUser
):
    try:
        pdf_path = await service.generate_incident_pdf(db, current_user, incident_id)
        filename = f"incident_report_{incident_id}.pdf"
        return FileResponse(pdf_path, filename=filename, media_type="application/pdf")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate PDF: {str(e)}")

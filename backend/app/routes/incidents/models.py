from datetime import datetime
from typing import List, Optional
from uuid import UUID

from entities.incident import IncidentTypeEnum, PriorityEnum, SeverityEnum, StatusEnum
from pydantic import BaseModel

from routes.users.models import UserResponse


class IncidentCreate(BaseModel):
    incident_number: Optional[str] = None
    title: str
    summary: Optional[str] = None
    priority: Optional[PriorityEnum] = None
    severity: Optional[SeverityEnum] = None
    incident_type: IncidentTypeEnum
    status: Optional[StatusEnum] = None
    reporter: Optional[UserResponse] = None
    reported_at: Optional[datetime] = None
    affected_services: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    incident_details: Optional[str] = None
    attachments: Optional[List[str]] = None


class IncidentRead(BaseModel):
    id: UUID
    incident_number: str
    title: str
    summary: Optional[str]
    priority: Optional[PriorityEnum]
    severity: Optional[SeverityEnum]
    incident_type: IncidentTypeEnum
    status: StatusEnum
    reporter: Optional[UserResponse]
    reported_at: datetime

    class Config:
        from_attributes = True


# Incident details only
class IncidentDetailsRead(BaseModel):
    incident_id: UUID
    affected_services: Optional[List[str]]
    tags: Optional[List[str]]
    incident_details: Optional[str]
    attachments: Optional[List[str]]

    class Config:
        from_attributes = True


# Incident details update
class IncidentDetailsUpdate(BaseModel):
    affected_services: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    incident_details: Optional[str] = None
    attachments: Optional[List[str]] = None


class IncidentUpdate(BaseModel):
    title: Optional[str] = None
    priority: Optional[PriorityEnum] = None
    severity: Optional[SeverityEnum] = None
    incident_type: Optional[IncidentTypeEnum] = None
    status: Optional[StatusEnum] = None
    affected_services: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    incident_details: Optional[str] = None
    attachments: Optional[List[str]] = None


class IncidentFilter(BaseModel):
    incident_type: Optional[IncidentTypeEnum] = None
    priority: Optional[PriorityEnum] = None
    severity: Optional[SeverityEnum] = None
    status: Optional[StatusEnum] = None
    reporter: Optional[str] = None


class PaginatedIncidentResponse(BaseModel):
    items: List[IncidentRead]
    total: int
    page: int
    limit: int
    pages: int


class IncidentAIAnalysisRead(BaseModel):
    incident_id: UUID
    root_cause: Optional[str] = None
    immediate_action: Optional[str] = None
    impact_forecast: Optional[str] = None
    cascading_risks: Optional[str] = None

    class Config:
        from_attributes = True


class SimilarIncidentRead(BaseModel):
    incident: IncidentRead
    similarity_score: float

    class Config:
        from_attributes = True


class SimilarIncidentsResponse(BaseModel):
    similar_incidents: List[SimilarIncidentRead]
    total_found: int

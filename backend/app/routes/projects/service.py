from datetime import datetime, timezone
from typing import List, Tuple
from uuid import UUID

from database.core import DbSession
from db_services import projects as projects_db_service
from entities.projects import Project
from fastapi import HTTPException, status
from utils.logger import get_service_logger

from routes.projects.models import ProjectCreate, ProjectUpdate

logger = get_service_logger("projects")


def create_project(db: DbSession, data: ProjectCreate, user_id: UUID) -> Project:
    """Create a new project."""
    logger.info(f"Creating project for user {user_id}: {data.name}")
    try:
        new_project = Project(
            name=data.name,
            description=data.description,
            created_by=user_id,
        )
        result = projects_db_service.create_project(db, new_project)
        logger.info(f"Successfully created project with ID: {result.id}")
        return result
    except Exception as e:
        logger.error(f"Failed to create project: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create project",
        )


def get_project_by_id(db: DbSession, project_id: UUID) -> Project:
    """Get a project by ID."""
    logger.info(f"Getting project: {project_id}")
    project = projects_db_service.get_project_by_id(db, project_id)
    if not project:
        logger.warning(f"Project not found: {project_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )
    return project


def update_project(
    db: DbSession, project_id: UUID, data: ProjectUpdate, user_id: UUID
) -> Project:
    """Update a project."""
    logger.info(f"Updating project: {project_id}")

    # Check if project exists and user has access
    project = get_project_by_id(db, project_id)
    if project.created_by != user_id:
        logger.warning(
            f"User {user_id} attempted to update project {project_id} without permission"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this project",
        )

    update_data = data.model_dump(exclude_unset=True)
    if update_data:
        update_data["updated_at"] = datetime.now(timezone.utc)

    try:
        result = projects_db_service.update_project(db, project_id, update_data)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
            )
        logger.info(f"Successfully updated project: {project_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update project {project_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update project",
        )


def delete_project(db: DbSession, project_id: UUID, user_id: UUID) -> dict:
    """Delete a project."""
    logger.info(f"Deleting project: {project_id}")

    # Check if project exists and user has access
    project = get_project_by_id(db, project_id)
    if project.created_by != user_id:
        logger.warning(
            f"User {user_id} attempted to delete project {project_id} without permission"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this project",
        )

    try:
        success = projects_db_service.delete_project(db, project_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
            )
        logger.info(f"Successfully deleted project: {project_id}")
        return {"message": f"Project {project_id} deleted"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete project {project_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete project",
        )


def get_user_projects(
    db: DbSession, user_id: UUID, offset: int = 0, limit: int = 10
) -> Tuple[List[Project], int]:
    """Get projects for a user."""
    logger.info(
        f"Getting projects for user {user_id} with offset={offset}, limit={limit}"
    )
    try:
        projects, total = projects_db_service.get_projects_by_user(
            db, user_id, offset, limit
        )
        logger.info(
            f"Retrieved {len(projects)} projects out of {total} total for user {user_id}"
        )
        return projects, total
    except Exception as e:
        logger.error(f"Failed to get projects for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get projects",
        )


def get_all_projects(
    db: DbSession, offset: int = 0, limit: int = 10
) -> Tuple[List[Project], int]:
    """Get all projects (admin only)."""
    logger.info(f"Getting all projects with offset={offset}, limit={limit}")
    try:
        projects, total = projects_db_service.get_all_projects(db, offset, limit)
        logger.info(f"Retrieved {len(projects)} projects out of {total} total")
        return projects, total
    except Exception as e:
        logger.error(f"Failed to get all projects: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get projects",
        )


def search_projects(
    db: DbSession, search_term: str, offset: int = 0, limit: int = 10
) -> Tuple[List[Project], int]:
    """Search projects by name."""
    logger.info(
        f"Searching projects with term '{search_term}' with offset={offset}, limit={limit}"
    )
    try:
        projects, total = projects_db_service.search_projects_by_name(
            db, search_term, offset, limit
        )
        logger.info(
            f"Found {len(projects)} projects out of {total} total matching '{search_term}'"
        )
        return projects, total
    except Exception as e:
        logger.error(f"Failed to search projects: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search projects",
        )

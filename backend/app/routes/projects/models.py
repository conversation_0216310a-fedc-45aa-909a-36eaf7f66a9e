from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ProjectCreate(BaseModel):
    name: str = Field(..., description="Project name", max_length=255)
    description: str = Field(..., description="Project description")


class ProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Project name", max_length=255)
    description: Optional[str] = Field(None, description="Project description")


class KnowledgeBaseInfo(BaseModel):
    id: UUID
    name: str
    kb_type: str
    description: Optional[str]
    document_count: int = 0

    class Config:
        from_attributes = True


class ProjectResponse(BaseModel):
    id: UUID
    name: str
    description: str
    created_at: datetime
    updated_at: datetime
    created_by: UUID
    knowledge_bases: List[KnowledgeBaseInfo] = []

    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    id: UUID
    name: str
    description: str
    created_at: datetime
    updated_at: datetime
    created_by: UUID
    knowledge_base_count: int = 0

    class Config:
        from_attributes = True


class PaginatedProjectResponse(BaseModel):
    items: List[ProjectListResponse]
    total: int
    page: int
    limit: int
    pages: int

from typing import Optional
from uuid import UUID

from database.core import DbSession
from entities.incident_metrics import IncidentMetric
from fastapi import HTTPException, status
from utils.logger import get_service_logger

from routes.incident_metrics.models import (
    IncidentMetricCreate,
    IncidentMetricResponse,
    IncidentMetricUpdate,
)

logger = get_service_logger("incident_metrics")


def get_incident_metrics(
    db: DbSession,
    incident_id: UUID,
) -> Optional[IncidentMetricResponse]:
    logger.info(f"Getting metrics for incident {incident_id}")
    try:
        metric = (
            db.query(IncidentMetric)
            .filter(IncidentMetric.incident_id == incident_id)
            .first()
        )

        if metric:
            logger.info(f"Retrieved metric for incident {incident_id}")
            return IncidentMetricResponse.model_validate(metric)
        else:
            logger.info(f"No metrics found for incident {incident_id}")
            return None
    except Exception as e:
        logger.error(f"Error fetching metrics for incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch incident metrics",
        )


def create_incident_metric(
    db: DbSession, metric_data: IncidentMetricCreate
) -> IncidentMetricResponse:
    logger.info(f"Creating metric for incident {metric_data.incident_id}")
    try:
        metric = IncidentMetric(**metric_data.model_dump())
        db.add(metric)
        db.commit()
        db.refresh(metric)
        logger.info(
            f"Created metric {metric.id} for incident {metric_data.incident_id}"
        )
        return IncidentMetricResponse.model_validate(metric)
    except Exception as e:
        db.rollback()
        logger.error(
            f"Error creating metric for incident {metric_data.incident_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create incident metric",
        )


def update_incident_metric(
    db: DbSession, incident_id: UUID, metric_data: IncidentMetricUpdate
) -> IncidentMetricResponse:
    logger.info(f"Updating metrics for incident {incident_id}")
    try:
        metric = (
            db.query(IncidentMetric)
            .filter(IncidentMetric.incident_id == incident_id)
            .first()
        )
        if not metric:
            logger.warning(f"No metrics found for incident {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Incident metric not found",
            )

        update_data = metric_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(metric, key, value)

        db.commit()
        db.refresh(metric)
        logger.info(f"Updated metrics for incident {incident_id}")
        return IncidentMetricResponse.model_validate(metric)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating metrics for incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update incident metric",
        )

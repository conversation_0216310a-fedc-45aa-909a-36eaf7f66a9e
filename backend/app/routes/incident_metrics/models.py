from datetime import datetime, timedelta
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, field_serializer


class IncidentMetricCreate(BaseModel):
    incident_id: UUID
    detected_time: Optional[datetime] = None
    acknowledged_time: Optional[datetime] = None
    resolved_time: Optional[datetime] = None
    closed_time: Optional[datetime] = None


class IncidentMetricResponse(BaseModel):
    id: UUID
    incident_id: UUID
    detected_time: Optional[datetime] = None
    reported_time: datetime
    acknowledged_time: Optional[datetime] = None
    resolved_time: Optional[datetime] = None
    closed_time: Optional[datetime] = None
    time_to_report: Optional[timedelta] = None
    time_to_acknowledge: Optional[timedelta] = None
    time_to_resolve: Optional[timedelta] = None
    time_to_closure: Optional[timedelta] = None
    total_downtime: Optional[timedelta] = None

    @field_serializer(
        "time_to_report",
        "time_to_acknowledge",
        "time_to_resolve",
        "time_to_closure",
        "total_downtime",
    )
    def serialize_timedelta(self, value: Optional[timedelta]) -> Optional[str]:
        """Convert timedelta to string format like 'HH:MM:SS'"""
        if value is None:
            return None

        total_seconds = int(value.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60

        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    class Config:
        from_attributes = True


class IncidentMetricUpdate(BaseModel):
    detected_time: Optional[datetime] = None
    acknowledged_time: Optional[datetime] = None
    resolved_time: Optional[datetime] = None
    closed_time: Optional[datetime] = None

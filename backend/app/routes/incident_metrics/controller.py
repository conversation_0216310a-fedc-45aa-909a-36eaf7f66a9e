from uuid import UUID

from database.core import DbSession
from fastapi import APIRouter, HTTPException, status
from utils.logger import get_controller_logger

from routes.auth.service import CurrentUser
from routes.incident_metrics import models, service

logger = get_controller_logger("incident_metrics")

router = APIRouter(prefix="/incident/{incident_id}/metrics", tags=["Incident Metrics"])


@router.get("", response_model=models.IncidentMetricResponse)
def get_incident_metrics(
    incident_id: UUID,
    db: DbSession,
    current_user: CurrentUser,
):
    logger.info(f"Getting metrics for incident {incident_id}")
    try:
        metric = service.get_incident_metrics(db, incident_id)
        if metric is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No metrics found for this incident",
            )
        logger.info(f"Retrieved metric for incident {incident_id}")
        return metric
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get metrics for incident {incident_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch incident metrics",
        )


@router.post(
    "",
    response_model=models.IncidentMetricResponse,
    status_code=status.HTTP_201_CREATED,
)
def create_incident_metric(
    incident_id: UUID,
    metric_data: models.IncidentMetricCreate,
    db: DbSession,
    current_user: CurrentUser,
):
    logger.info(f"Creating metric for incident {incident_id}")
    # Ensure the incident_id in the path matches the one in the request body
    if metric_data.incident_id != incident_id:
        logger.warning(
            f"Incident ID mismatch: {incident_id} vs {metric_data.incident_id}"
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incident ID in path must match the one in request body",
        )

    try:
        return service.create_incident_metric(db, metric_data)
    except Exception as e:
        logger.error(f"Failed to create metric for incident {incident_id}: {str(e)}")
        raise


@router.put("", response_model=models.IncidentMetricResponse)
def update_incident_metric(
    incident_id: UUID,
    metric_data: models.IncidentMetricUpdate,
    db: DbSession,
    current_user: CurrentUser,
):
    logger.info(f"Updating metrics for incident {incident_id}")
    try:
        return service.update_incident_metric(db, incident_id, metric_data)
    except Exception as e:
        logger.error(f"Failed to update metrics for incident {incident_id}: {str(e)}")
        raise

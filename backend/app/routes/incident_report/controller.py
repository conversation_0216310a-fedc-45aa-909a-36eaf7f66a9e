from uuid import UUID

from database.core import DbSession, get_db
from entities.incident_report import IncidentReport
from fastapi import APIRouter, Depends, HTTPException
from routes.auth.service import CurrentUser
from routes.incident_report import service

from .models import IncidentReportResponse

router = APIRouter(prefix="/incident/{incident_id}/report", tags=["Incident Report"])


@router.get("", response_model=IncidentReportResponse)
def get_incident_report(incident_id: UUID, db: DbSession, current_user: CurrentUser):
    report = service.get_incident_report_by_incident_id(db, incident_id)
    if not report:
        raise HTTPException(status_code=404, detail="Incident report not found")
    return report

from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class IncidentReportResponse(BaseModel):
    incident_id: UUID
    executive_summary: Optional[str] = None
    post_incident_actions: List[Any] = Field(default_factory=list)
    retrospectives: Optional[Dict[str, Any]] = Field(default_factory=dict)
    action_items: List[Dict[str, Any]] = Field(default_factory=list)

    class Config:
        from_attributes = True

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from entities.documents import DocumentTypeEnum
from entities.knowledge_base import KnowledgeBaseTypeEnum
from pydantic import BaseModel, Field


class KnowledgeBaseCreate(BaseModel):
    name: str = Field(..., description="Knowledge base name", max_length=255)
    description: Optional[str] = Field(None, description="Knowledge base description")
    kb_type: KnowledgeBaseTypeEnum = Field(..., description="Knowledge base type")
    project_id: UUID = Field(..., description="Project ID")


class KnowledgeBaseUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Knowledge base name", max_length=255)
    description: Optional[str] = Field(None, description="Knowledge base description")


class DocumentInfo(BaseModel):
    id: UUID
    name: str
    document_type: DocumentTypeEnum
    sync_status: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


class KnowledgeBaseResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str]
    kb_type: str
    project_id: UUID
    created_at: datetime
    updated_at: datetime
    created_by: UUID
    updated_by: UUID
    documents: List[DocumentInfo] = []

    class Config:
        from_attributes = True


# Document models for knowledge base endpoints
class DocumentListResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str]
    document_type: str
    created_at: datetime
    updated_at: datetime
    sync_status: Optional[str]
    knowledge_base_id: UUID

    class Config:
        from_attributes = True


class DocumentResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str]
    document_type: str
    content: Optional[str]
    meta_data: Optional[dict]
    created_at: datetime
    updated_at: datetime
    created_by: UUID
    updated_by: UUID
    last_synced_at: Optional[datetime]
    sync_status: Optional[str]
    knowledge_base_id: UUID

    class Config:
        from_attributes = True


class PaginatedDocumentResponse(BaseModel):
    items: List[DocumentListResponse]
    total: int
    page: int
    limit: int
    pages: int


class DocumentSearchRequest(BaseModel):
    query: str = Field(..., description="Search query")
    knowledge_base_id: Optional[UUID] = Field(
        None, description="Filter by knowledge base"
    )
    document_type: Optional[str] = Field(None, description="Filter by document type")
    top_k: int = Field(5, ge=1, le=20, description="Number of results to return")


class DocumentSearchResult(BaseModel):
    document_id: UUID
    name: str
    document_type: str
    score: float
    content_snippet: Optional[str]
    metadata: Optional[Dict[str, Any]]

    class Config:
        from_attributes = True


class DocumentSearchResponse(BaseModel):
    results: List[DocumentSearchResult]
    total_found: int
    query: str


class DocumentUploadResponse(BaseModel):
    """Response model for document upload."""

    id: UUID
    name: str
    document_type: str
    content_extracted: bool
    message: str

    class Config:
        from_attributes = True


class DocumentFileUpdate(BaseModel):
    """Model for updating document with file upload."""

    name: Optional[str] = Field(None, description="Document name", max_length=255)
    description: Optional[str] = Field(None, description="Document description")

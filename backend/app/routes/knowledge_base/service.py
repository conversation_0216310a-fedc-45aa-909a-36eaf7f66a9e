from datetime import datetime, timezone
from typing import List, Optional, Tuple
from uuid import UUID

from database.core import DbSession
from db_services import documents as documents_db_service
from db_services import knowledge_base as knowledge_base_db_service
from db_services import projects as projects_db_service
from entities.documents import Document, DocumentTypeEnum, SyncStatusEnum
from entities.knowledge_base import KnowledgeBase, KnowledgeBaseTypeEnum
from fastapi import HTTPException, UploadFile, status
from services.file_manager import FileManager
from tasks.vector_db import process_document_task
from utils.logger import get_service_logger

from routes.knowledge_base.models import (
    DocumentFileUpdate,
    KnowledgeBaseCreate,
    KnowledgeBaseUpdate,
)

logger = get_service_logger("knowledge_base")


def create_kb_entry(
    db: DbSession, data: KnowledgeBaseCreate, user_id: UUID
) -> KnowledgeBase:
    logger.info(f"Creating knowledge base entry for user {user_id}: {data.name}")

    # Verify project exists
    project = projects_db_service.get_project_by_id(db, data.project_id)
    if not project:
        logger.warning(f"Project not found: {data.project_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    # Validate knowledge base type
    try:
        KnowledgeBaseTypeEnum(data.kb_type)
    except ValueError:
        logger.warning(f"Invalid knowledge base type: {data.kb_type}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid knowledge base type: {data.kb_type}",
        )

    try:
        new_entry = KnowledgeBase(
            name=data.name,
            description=data.description,
            kb_type=data.kb_type,
            project_id=data.project_id,
            created_by=user_id,
            updated_by=user_id,
        )
        result = knowledge_base_db_service.create_knowledge_base(db, new_entry)
        logger.info(f"Successfully created knowledge base entry with ID: {result.id}")
        return result
    except Exception as e:
        logger.error(f"Failed to create knowledge base entry: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create KB entry",
        )


def update_kb_entry(
    db: DbSession, entry_id: UUID, data: KnowledgeBaseUpdate, user_id: UUID
) -> KnowledgeBase:
    logger.info(f"Updating knowledge base entry: {entry_id}")
    entry = knowledge_base_db_service.get_kb_entry_by_id(db, entry_id)
    if not entry:
        logger.warning(f"Knowledge base entry not found: {entry_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="KB entry not found"
        )
    update_data = data.model_dump(exclude_unset=True)
    if update_data:
        update_data["updated_by"] = user_id

    for key, value in update_data.items():
        setattr(entry, key, value)

    try:
        db.commit()
        db.refresh(entry)
        logger.info(f"Successfully updated knowledge base entry: {entry_id}")
        return entry
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update knowledge base entry {entry_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update KB entry",
        )


def get_kb_entry_by_id(db: DbSession, entry_id: UUID) -> KnowledgeBase:
    """Get a knowledge base by ID."""
    logger.info(f"Getting knowledge base: {entry_id}")
    kb = knowledge_base_db_service.get_kb_entry_by_id(db, entry_id)
    if not kb:
        logger.warning(f"Knowledge base not found: {entry_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found"
        )
    return kb


def get_kb_entries_by_project_id(
    db: DbSession, project_id: UUID
) -> List[KnowledgeBase]:
    logger.info(f"Getting knowledge base entries for project: {project_id}")
    try:
        knowledgebases = knowledge_base_db_service.get_kb_entries_by_project_id(
            db, project_id
        )
        logger.info(f"Retrieved {len(knowledgebases)} knowledge bases")
        return knowledgebases
    except Exception as e:
        logger.error(f"Failed to get knowledge base entries: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get KB entries",
        )


def delete_kb_entry(db: DbSession, entry_id: UUID, user_id: UUID) -> dict:
    """Delete a knowledge base entry."""
    logger.info(f"Deleting knowledge base entry: {entry_id}")
    entry = knowledge_base_db_service.get_kb_entry_by_id(db, entry_id)
    if not entry:
        logger.warning(f"Knowledge base entry not found for deletion: {entry_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="KB entry not found"
        )

    try:
        success = knowledge_base_db_service.delete_knowledge_base(db, entry_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="KB entry not found"
            )
        logger.info(f"Successfully deleted knowledge base entry: {entry_id}")
        return {"message": f"Knowledge base {entry_id} deleted"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete knowledge base entry {entry_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete KB entry",
        )


def get_document_by_id(db: DbSession, document_id: UUID) -> Document:
    """Get a document by ID."""
    logger.info(f"Getting document: {document_id}")
    document = documents_db_service.get_document_by_id(db, document_id)
    if not document:
        logger.warning(f"Document not found: {document_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
        )
    return document


def get_documents_by_knowledge_base(
    db: DbSession, knowledge_base_id: UUID, offset: int = 0, limit: int = 10
) -> Tuple[List[Document], int]:
    """Get documents for a knowledge base."""
    logger.info(
        f"Getting documents for knowledge base {knowledge_base_id} with offset={offset}, limit={limit}"
    )

    # Verify knowledge base exists
    kb = knowledge_base_db_service.get_kb_entry_by_id(db, knowledge_base_id)
    if not kb:
        logger.warning(f"Knowledge base not found: {knowledge_base_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found"
        )

    try:
        documents, total = documents_db_service.get_documents_by_knowledge_base(
            db, knowledge_base_id, offset, limit
        )
        logger.info(
            f"Retrieved {len(documents)} documents out of {total} total for KB {knowledge_base_id}"
        )
        return documents, total
    except Exception as e:
        logger.error(
            f"Failed to get documents for knowledge base {knowledge_base_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get documents",
        )


# File upload and management functions
async def create_document(
    db: DbSession,
    file: UploadFile,
    knowledge_base_id: UUID,
    name: Optional[str] = None,
    description: Optional[str] = None,
    user_id: Optional[UUID] = None,
) -> Document:
    """
    Create a new document with file upload.

    Optimized flow:
    1. Save uploaded file to disk
    2. Create document record in database
    3. Trigger Celery task for content extraction and vector indexing
    4. Return response immediately
    """
    logger.info(
        f"Creating document with file upload for KB {knowledge_base_id}: {file.filename}"
    )

    # Verify knowledge base exists
    kb = knowledge_base_db_service.get_kb_entry_by_id(db, knowledge_base_id)
    if not kb:
        logger.warning(f"Knowledge base not found: {knowledge_base_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found"
        )

    try:
        # Save file and extract content
        file_manager = FileManager()
        file_result = await file_manager.save_uploaded_file(
            file, str(knowledge_base_id)
        )

        # Determine document type from file extension
        file_extension = file_result["metadata"]["file_extension"].lower()
        document_type_map = {
            ".pdf": DocumentTypeEnum.PDF,
            ".txt": DocumentTypeEnum.TEXT,
            ".md": DocumentTypeEnum.MARKDOWN,
            ".docx": DocumentTypeEnum.DOCX,
            ".doc": DocumentTypeEnum.DOCX,
        }
        document_type = document_type_map.get(file_extension, DocumentTypeEnum.TEXT)

        # Create document record - content will be extracted by Celery task
        new_document = Document(
            name=name or file.filename,
            description=description,
            document_type=document_type,
            content=None,  # Content extraction handled by Celery task
            meta_data=file_result.get("metadata", {}),
            knowledge_base_id=knowledge_base_id,
            created_by=user_id,
            updated_by=user_id,
            sync_status=SyncStatusEnum.PENDING,  # Will be updated by Celery task
        )

        result = documents_db_service.create_document(db, new_document)

        # Trigger async document processing task
        process_document_task.delay(str(result.id), "create")

        logger.info(f"Successfully created document with file: {result.id}")
        return result

    except Exception as e:
        logger.error(f"Failed to create document with file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create document with file: {str(e)}",
        )


async def update_document(
    db: DbSession,
    document_id: UUID,
    file: Optional[UploadFile] = None,
    data: Optional[DocumentFileUpdate] = None,
    user_id: Optional[UUID] = None,
) -> Document:
    """Update a document with optional file upload."""
    logger.info(f"Updating document {document_id} with file upload")

    # Get existing document
    document = get_document_by_id(db, document_id)

    update_data = {}
    if data:
        update_data = data.model_dump(exclude_unset=True)

    try:
        # Handle file update if provided
        if file:
            # Save new file
            file_manager = FileManager()
            file_result = await file_manager.update_file(
                file,
                old_file_path=document.meta_data.get("file_path"),
                knowledge_base_id=str(document.knowledge_base_id),
                document_id=str(document_id),
            )

            # Update document fields - content extraction handled by Celery task
            update_data.update(
                {
                    "content": None,  # Content extraction handled by Celery task
                    "meta_data": file_result.get("metadata", {}),
                    "sync_status": SyncStatusEnum.PENDING,  # Will be updated by Celery task
                }
            )

        if update_data:
            update_data["updated_by"] = user_id
            update_data["updated_at"] = datetime.now(timezone.utc)

        # Update document in database
        result = documents_db_service.update_document(db, document_id, update_data)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
            )

        # Trigger async document processing task
        operation = "update" if file else "refresh"
        process_document_task.delay(str(document_id), operation)

        logger.info(f"Successfully updated document with file: {document_id}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update document with file {document_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update document with file: {str(e)}",
        )


async def delete_document(db: DbSession, document_id: UUID, user_id: UUID) -> dict:
    """Delete a document with file and vector DB cleanup."""
    logger.info(f"Deleting document with cleanup: {document_id}")

    # Get document to access file path
    document = get_document_by_id(db, document_id)

    try:
        # Delete from vector DB first
        from vector_db.search_service import VectorSearchService

        vector_service = VectorSearchService()
        try:
            vector_service.delete_document_vector(document_id)
            logger.info(f"Removed document {document_id} from vector DB")
        except Exception as e:
            logger.warning(f"Failed to remove document from vector DB: {str(e)}")

        # Delete file if exists
        file_path = document.meta_data.get("file_path")
        if file_path:
            file_manager = FileManager()
            await file_manager.delete_file(file_path)
            logger.info(f"Deleted file: {file_path}")

        # Delete document record
        success = documents_db_service.delete_document(db, document_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
            )

        logger.info(f"Successfully deleted document: {document_id}")
        return {"message": f"Document {document_id} deleted "}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete document {document_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete document : {str(e)}",
        )


async def create_document_from_url(
    db: DbSession,
    external_url: str,
    knowledge_base_id: UUID,
    name: Optional[str] = None,
    description: Optional[str] = None,
    user_id: Optional[UUID] = None,
) -> Document:
    """Create a new document from external URL."""
    logger.info(
        f"Creating document from URL for KB {knowledge_base_id}: {external_url}"
    )

    # Verify knowledge base exists
    kb = knowledge_base_db_service.get_kb_entry_by_id(db, knowledge_base_id)
    if not kb:
        logger.warning(f"Knowledge base not found: {knowledge_base_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found"
        )

    try:
        # Create document record
        new_document = Document(
            name=name or f"Document from {external_url}",
            description=description,
            document_type=DocumentTypeEnum.URL,
            content=None,  # Will be fetched by Celery task
            meta_data={"external_url": external_url},
            knowledge_base_id=knowledge_base_id,
            created_by=user_id,
            updated_by=user_id,
            sync_status=SyncStatusEnum.PENDING,
        )

        result = documents_db_service.create_document(db, new_document)

        # Trigger async document processing task
        process_document_task.delay(str(result.id), "create")

        logger.info(f"Successfully created document from URL: {result.id}")
        return result

    except Exception as e:
        logger.error(f"Failed to create document from URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create document from URL: {str(e)}",
        )


async def create_document_from_text(
    db: DbSession,
    content: str,
    knowledge_base_id: UUID,
    name: Optional[str] = None,
    description: Optional[str] = None,
    user_id: Optional[UUID] = None,
) -> Document:
    """Create a new document from text content."""
    logger.info(f"Creating document from text for KB {knowledge_base_id}")

    # Verify knowledge base exists
    kb = knowledge_base_db_service.get_kb_entry_by_id(db, knowledge_base_id)
    if not kb:
        logger.warning(f"Knowledge base not found: {knowledge_base_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found"
        )

    try:
        # Create document record
        new_document = Document(
            name=name or "Text Document",
            description=description,
            document_type=DocumentTypeEnum.TEXT,
            content=content,
            knowledge_base_id=knowledge_base_id,
            created_by=user_id,
            updated_by=user_id,
            sync_status=SyncStatusEnum.SYNCED,  # Already has content
        )

        result = documents_db_service.create_document(db, new_document)

        # Trigger async document processing task for vector indexing
        process_document_task.delay(str(result.id), "create")

        logger.info(f"Successfully created document from text: {result.id}")
        return result

    except Exception as e:
        logger.error(f"Failed to create document from text: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create document from text: {str(e)}",
        )


async def update_document_content(
    db: DbSession,
    document_id: UUID,
    external_url: Optional[str] = None,
    content: Optional[str] = None,
    name: Optional[str] = None,
    description: Optional[str] = None,
    user_id: Optional[UUID] = None,
) -> Document:
    """Update document content (URL, text, or metadata)."""
    logger.info(f"Updating document content: {document_id}")

    # Get existing document
    document = get_document_by_id(db, document_id)

    try:
        update_data = {}

        if name is not None:
            update_data["name"] = name
        if description is not None:
            update_data["description"] = description
        if user_id is not None:
            update_data["updated_by"] = user_id
            update_data["updated_at"] = datetime.now(timezone.utc)

        if external_url:
            # Update external URL
            update_data.update(
                {
                    "document_type": DocumentTypeEnum.URL,
                    "content": None,  # Will be fetched by task
                    "meta_data": {"external_url": external_url},
                    "sync_status": SyncStatusEnum.PENDING,
                }
            )
        elif content:
            # Update text content
            update_data.update(
                {
                    "document_type": DocumentTypeEnum.TEXT,
                    "content": content,
                    "meta_data": None,
                    "sync_status": SyncStatusEnum.SYNCED,
                }
            )

        if update_data:
            result = documents_db_service.update_document(db, document_id, update_data)
            if not result:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
                )

            # Trigger async document processing task
            operation = "update" if (external_url or content) else "refresh"
            process_document_task.delay(str(document_id), operation)

            logger.info(f"Successfully updated document content: {document_id}")
            return result
        else:
            # No updates provided
            return document

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update document content {document_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update document content: {str(e)}",
        )

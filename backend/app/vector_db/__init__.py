"""
Vector Database Module
======================

This module provides a unified interface for vector database operations
across different vector database providers. It includes:

- Abstract base class (VectorDBConnector) defining the standard interface
- Concrete implementations for specific vector databases (QdrantConnector)
- Pre-configured instances ready for use throughout the application

Architecture:
- VectorDBConnector: Abstract base class defining the interface
- QdrantConnector: Qdrant-specific implementation
- qdrant_connector: Ready-to-use instance of QdrantConnector

Usage:
    # Use the pre-configured instance (recommended)
    from vector_db import qdrant_connector, vector_search_service

    # Use classes for custom configuration
    from vector_db import VectorDBConnector, QdrantConnector, VectorSearchService

    # Create custom instance
    custom_connector = QdrantConnector(url="custom://url")
"""

# Import abstract base class for interface definition
from vector_db.base_connector import VectorDBConnector

# Import concrete implementations
from vector_db.qdrant_connector import QdrantConnector
from vector_db.search_service import VectorSearchService

# Define what gets exported when using "from vector_db import *"
__all__ = [
    # Abstract base class
    "VectorDBConnector",
    # Concrete implementations
    "QdrantConnector",
    "VectorSearchService",
]

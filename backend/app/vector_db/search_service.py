"""
Vector Search Service Module

Provides comprehensive vector operations for incidents and documents including
data retrieval, embedding generation, metadata creation, and vector storage/search.
This service handles all the technical complexity of vector operations internally.
"""

from typing import Dict, List, Optional
from uuid import UUID

from database.core import DbSession
from db_services import documents as documents_db_service
from db_services import incident as incident_db_service
from utils.logger import get_service_logger

from vector_db.base_connector import CollectionType
from vector_db.embeddings import generate_embedding
from vector_db.qdrant_connector import QdrantConnector

logger = get_service_logger("vector_search")


class VectorSearchService:
    """High-level vector search service for finding similar incidents and documents."""

    def __init__(self):
        self.connector = QdrantConnector(
            collections={
                CollectionType.INCIDENTS: "incidents",
                CollectionType.DOCUMENTS: "documents",
            }
        )

    def search_similar_incidents_by_text(
        self,
        db: DbSession,
        text: str,
        top_k: int = 5,
        exclude_ids: Optional[List[UUID]] = None,
    ) -> List[Dict]:
        """Search for incidents similar to given text."""
        if not text.strip():
            raise ValueError("Text cannot be empty")

        embedding = generate_embedding(text)
        results = self.connector._find_similar_vectors(
            CollectionType.INCIDENTS, embedding, top_k
        )

        return self._process_incident_results(db, results, exclude_ids=exclude_ids)

    def search_similar_incidents_by_id(
        self,
        db: DbSession,
        incident_id: UUID,
        top_k: int = 5,
        exclude_ids: Optional[List[UUID]] = None,
    ) -> List[Dict]:
        """Search for incidents similar to given incident ID."""
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            raise ValueError(f"Incident with ID {incident_id} not found")

        incident_dict = {
            "title": incident.title,
            "summary": incident.summary or "",
            "details": (
                incident.incident_detail.incident_details
                if incident.incident_detail
                else ""
            ),
        }
        embedding = generate_embedding(incident_dict)
        results = self.connector._find_similar_vectors(
            CollectionType.INCIDENTS, embedding, top_k + 1
        )

        # Add the source incident to exclusion list
        all_exclude_ids = [incident_id]
        if exclude_ids:
            all_exclude_ids.extend(exclude_ids)

        return self._process_incident_results(db, results, exclude_ids=all_exclude_ids)

    def search_similar_documents_by_text(
        self, text: str, top_k: int = 5, exclude_ids: Optional[List[UUID]] = None
    ) -> List[Dict]:
        """Search for documents similar to given text."""
        if not text.strip():
            raise ValueError("Text cannot be empty")

        embedding = generate_embedding(text)
        results = self.connector._find_similar_vectors(
            CollectionType.DOCUMENTS, embedding, top_k
        )

        if exclude_ids:
            exclude_strings = [str(doc_id) for doc_id in exclude_ids]
            filtered_results = []
            for result in results:
                document_id = result["payload"].get("document_id")
                if document_id not in exclude_strings:
                    filtered_results.append(result)
            return filtered_results

        return results

    def _process_incident_results(
        self,
        db: DbSession,
        results: List[Dict],
        exclude_ids: Optional[List[UUID]] = None,
    ) -> List[Dict]:
        """Process vector search results into incident objects."""
        processed_results = []
        exclude_strings = [str(uid) for uid in exclude_ids] if exclude_ids else []

        for result in results:
            payload_incident_id = result["payload"].get("incident_id")
            if not payload_incident_id:
                continue

            if payload_incident_id in exclude_strings:
                continue

            try:
                incident_uuid = UUID(payload_incident_id)
                incident = incident_db_service.get_incident_by_id(db, incident_uuid)
                if incident:
                    processed_results.append(
                        {
                            "incident": incident,
                            "similarity_score": float(result["score"]),
                        }
                    )
            except (ValueError, TypeError):
                continue

        return processed_results

    def _process_document_results(
        self, results: List[Dict], exclude_ids: Optional[List[UUID]] = None
    ) -> List[Dict]:
        """Process document search results and add document information."""
        if exclude_ids:
            exclude_strings = [str(doc_id) for doc_id in exclude_ids]
            filtered_results = []
            for result in results:
                document_id = result["payload"].get("document_id")
                if document_id not in exclude_strings:
                    filtered_results.append(result)
            results = filtered_results

        processed_results = []
        for result in results:
            processed_result = {
                "document_id": result["payload"].get("document_id"),
                "document_name": result["payload"].get("document_name"),
                "document_type": result["payload"].get("document_type"),
                "knowledge_base_id": result["payload"].get("knowledge_base_id"),
                "score": result["score"],
                "metadata": result["payload"],
            }
            processed_results.append(processed_result)

        return processed_results

    def upsert_incident(self, db: DbSession, incident_id: UUID) -> bool:
        """
        Upsert an incident vector into the collection.

        Handles data retrieval, metadata creation, and embedding generation internally.

        Args:
            db (DbSession): Database session for data retrieval.
            incident_id (UUID): The unique identifier for the incident.

        Returns:
            bool: True if the upsert was successful, False otherwise.
        """
        # Retrieve incident data from database
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            raise ValueError(f"Incident with ID {incident_id} not found")

        # Prepare incident data for embedding
        incident_dict = {
            "title": incident.title,
            "summary": incident.summary or "",
            "details": (
                incident.incident_detail.incident_details
                if incident.incident_detail
                else ""
            ),
        }

        # Generate embedding from incident data
        embedding = generate_embedding(incident_dict)

        # Create metadata
        metadata = {"incident_id": str(incident_id), "incident_title": incident.title}

        return self.connector._upsert_vector(
            CollectionType.INCIDENTS, incident_id, embedding, metadata
        )

    def upsert_document(self, db: DbSession, document_id: UUID) -> bool:
        """
        Upsert a document vector into the collection.

        Handles data retrieval, metadata creation, and embedding generation internally.

        Args:
            db (DbSession): Database session for data retrieval.
            document_id (UUID): The unique identifier for the document.

        Returns:
            bool: True if the upsert was successful, False otherwise.
        """
        # Retrieve document from database
        document = documents_db_service.get_document_by_id(db, document_id)
        if not document:
            raise ValueError(f"Document with ID {document_id} not found")

        # Prepare document data for embedding
        document_dict = {
            "name": document.name,
            "description": document.description or "",
            "content": document.content or "",
            "document_type": document.document_type,
        }

        # Generate embedding from document data
        embedding = generate_embedding(document_dict)

        # Create metadata with comprehensive document information
        metadata = {
            "document_id": str(document_id),
            "document_name": document.name,
            "document_type": document.document_type,
            "knowledge_base_id": str(document.knowledge_base_id),
            "created_at": document.created_at.isoformat()
            if document.created_at
            else None,
            "updated_at": document.updated_at.isoformat()
            if document.updated_at
            else None,
        }

        return self.connector._upsert_vector(
            CollectionType.DOCUMENTS, document_id, embedding, metadata
        )

    def delete_incident_vector(self, incident_id: UUID) -> bool:
        """Delete an incident vector from the collection."""
        return self.connector._delete_vector(CollectionType.INCIDENTS, incident_id)

    def delete_document_vector(self, document_id: UUID) -> bool:
        """Delete a document vector from the collection."""
        return self.connector._delete_vector(CollectionType.DOCUMENTS, document_id)

from typing import Dict, List, Union

from litellm import embedding
from utils.logger import get_service_logger

logger = get_service_logger("embeddings")

EMBEDDING_MODEL = "gemini/text-embedding-004"
EMBEDDING_VECTOR_SIZE = 768


def generate_embedding(input_data: Union[str, Dict]) -> List[float]:
    """
    Generates a vector embedding for any text string or dictionary using the configured embedding model.

    This function can handle:
    - Plain text strings
    - Dictionaries with common fields like 'title', 'summary', 'details', 'content'
    - Any other dictionary by combining all string values

    Args:
        input_data (Union[str, Dict]): The text string or dictionary to generate an embedding for.

    Returns:
        List[float]: Embedding vector from the configured model.

    Raises:
        ValueError: If input is empty, not a string/dict, or API fails.
        Exception: For any other errors during the embedding process.

    Examples:
        >>> generate_embedding("Database connection failed")
        [...embedding floats...]

        >>> generate_embedding({'title': 'DB Outage', 'summary': 'Database unavailable', 'details': 'Connection timeout...'})
        [...embedding floats...]

        >>> generate_embedding({'title': 'Troubleshooting Guide', 'content': 'Steps to resolve issues...'})
        [...embedding floats...]

    Security:
        - Never logs sensitive content or API keys.
        - Handles all error cases gracefully.
    """
    if isinstance(input_data, str):
        text = input_data.strip()
    elif isinstance(input_data, dict):
        fields = []
        for key, value in input_data.items():
            if isinstance(value, str) and value.strip():
                fields.append(value.strip())

        text = " ".join(fields).strip()
    else:
        logger.error(f"Invalid input type: {type(input_data)}. Expected str or dict.")
        raise ValueError("Input must be a string or dictionary.")

    if not text:
        logger.warning("Cannot generate embedding for empty content.")
        raise ValueError("Cannot generate embedding for empty content.")

    try:
        logger.debug(f"Requesting embedding for content (length={len(text)} chars)")
        response = embedding(model=EMBEDDING_MODEL, input=[text])
        embedding_vector = response["data"][0]["embedding"]
        logger.info("Successfully generated embedding.")
        return embedding_vector
    except Exception as e:
        logger.error(f"Failed to generate embedding: {e}")
        raise

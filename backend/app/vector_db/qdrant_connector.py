"""
Qdrant Connector Module
=======================

This module serves as the primary interface to the Qdrant vector database.
It implements concrete implementations for all vector database operations using Qdrant as the backend.

The `QdrantConnector` class extends the abstract `VectorDBConnector` base class
and encapsulates the Qdrant client, providing methods that are easy to use
throughout the application. It is designed to be instantiated once and reused,
handling connection and collection setup upon initialization.
"""

import os
from typing import Dict, List
from uuid import UUID

from qdrant_client import QdrantClient, models
from qdrant_client.http.models import Distance, PointStruct, UpdateStatus
from utils.logger import get_service_logger

from vector_db.base_connector import VectorDBConnector
from vector_db.embeddings import EMBEDDING_VECTOR_SIZE
from vector_db.models import CollectionType

# Initialize logger for the Qdrant connector module
logger = get_service_logger("qdrant_connector")


class QdrantConnector(VectorDBConnector):
    """
    A concrete implementation of VectorDBConnector for Qdrant vector database.

    This class implements all abstract methods defined in the VectorDBConnector
    base class, providing Qdrant-specific implementations while maintaining
    the standard interface.

    Inheritance:
        Extends VectorDBConnector abstract base class to ensure interface compliance
        and enable future extensibility with other vector database providers.
    """

    def __init__(self, collections: Dict[CollectionType, str]):
        """
        Initializes the QdrantConnector with support for both collections.
        """
        super().__init__(collections)

    def _create_client(self):
        qdrant_url = os.getenv("QDRANT_URL", "http://qdrant:6333")
        qdrant_api_key = os.getenv("QDRANT_API_KEY", None)
        try:
            logger.info(f"Initializing Qdrant client with URL: {qdrant_url}")
            return QdrantClient(url=qdrant_url, api_key=qdrant_api_key)

        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client: {e}")
            return None

    def _create_collections_if_not_exist(self):
        """
        Creates self._collections in Qdrant if they don't exist.
        """
        if not self._client:
            logger.error("Cannot create collections, Qdrant client is not initialized.")
            return

        try:
            collections_response = self._client.get_collections()
            existing_collections = {
                collection.name for collection in collections_response.collections
            }

            # Create collections for both types
            for collection_type, collection_name in self._collections.items():
                if collection_name not in existing_collections:
                    logger.info(
                        f"Collection '{collection_name}' not found. Creating it with vector size {EMBEDDING_VECTOR_SIZE}."
                    )
                    self._client.recreate_collection(
                        collection_name=collection_name,
                        vectors_config=models.VectorParams(
                            size=EMBEDDING_VECTOR_SIZE,
                            distance=Distance.COSINE,
                        ),
                    )
                    logger.info(
                        f"Successfully created collection '{collection_name}' with vector size {EMBEDDING_VECTOR_SIZE}."
                    )
                else:
                    logger.info(f"Collection '{collection_name}' already exists.")

        except Exception as e:
            logger.error(f"Failed to create or verify collections: {e}")

    def _upsert_vector(
        self,
        collection_type: CollectionType,
        vector_id: UUID,
        embedding: List[float],
        metadata: Dict,
    ) -> bool:
        """
        Generic method to upsert a vector into any collection.

        Args:
            collection_type (CollectionType): The type of collection to upsert into.
            vector_id (UUID): The unique identifier for the vector.
            embedding (List[float]): The vector embedding.
            metadata (Dict): Metadata to store with the vector.

        Returns:
            bool: True if the upsert was successful, False otherwise.
        """
        if not self._client:
            logger.error(
                f"Cannot upsert {collection_type.value}, Qdrant client is not initialized."
            )
            return False

        collection_name = self._collections[collection_type]

        try:
            logger.debug(
                f"Upserting {collection_type.value} vector for ID: {vector_id}"
            )
            operation_info = self._client.upsert(
                collection_name=collection_name,
                wait=True,
                points=[
                    PointStruct(id=str(vector_id), vector=embedding, payload=metadata)
                ],
            )
            if operation_info.status == UpdateStatus.COMPLETED:
                logger.info(
                    f"Successfully upserted {collection_type.value} vector for ID: {vector_id}"
                )
                return True
            else:
                logger.warning(
                    f"Upsert operation for {collection_type.value} {vector_id} was not completed: {operation_info.status}"
                )
                return False
        except Exception as e:
            logger.error(
                f"Failed to upsert {collection_type.value} vector for ID {vector_id}: {e}"
            )
            return False

    def _delete_vector(self, collection_type: CollectionType, vector_id: UUID) -> bool:
        """
        Generic method to delete a vector from any collection.

        Args:
            collection_type (CollectionType): The type of collection to delete from.
            vector_id (UUID): The unique identifier for the vector to delete.

        Returns:
            bool: True if the deletion was successful, False otherwise.
        """
        if not self._client:
            logger.error(
                f"Cannot delete {collection_type.value}, Qdrant client is not initialized."
            )
            return False

        collection_name = self._collections[collection_type]

        try:
            logger.debug(f"Deleting {collection_type.value} vector for ID: {vector_id}")
            operation_info = self._client.delete(
                collection_name=collection_name,
                points_selector=models.PointIdsList(points=[str(vector_id)]),
                wait=True,
            )
            if operation_info.status == UpdateStatus.COMPLETED:
                logger.info(
                    f"Successfully deleted {collection_type.value} vector for ID: {vector_id}"
                )
                return True
            else:
                logger.warning(
                    f"Delete operation for {collection_type.value} {vector_id} was not completed: {operation_info.status}"
                )
                return False
        except Exception as e:
            logger.error(
                f"Failed to delete {collection_type.value} vector for ID {vector_id}: {e}"
            )
            return False

    def _find_similar_vectors(
        self, collection_type: CollectionType, embedding: List[float], top_k: int = 5
    ) -> List[Dict]:
        """
        Generic method to find similar vectors in any collection.

        Args:
            collection_type (CollectionType): The type of collection to search in.
            embedding (List[float]): The query vector embedding.
            top_k (int): The number of similar vectors to return.

        Returns:
            List[Dict]: A list of dictionaries containing payload and score.
        """
        if not self._client:
            logger.error(
                f"Cannot search {collection_type.value}, Qdrant client is not initialized."
            )
            return []

        collection_name = self._collections[collection_type]

        try:
            logger.debug(f"Searching for {top_k} similar {collection_type.value}.")
            hits = self._client.search(
                collection_name=collection_name,
                query_vector=embedding,
                limit=top_k,
                with_payload=True,
            )
            logger.info(f"Found {len(hits)} similar {collection_type.value}.")
            return [{"payload": hit.payload, "score": hit.score} for hit in hits]
        except Exception as e:
            logger.error(
                f"Failed to perform {collection_type.value} similarity search: {e}"
            )
            return []

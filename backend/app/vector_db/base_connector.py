from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from uuid import UUID

from vector_db.models import CollectionType


class VectorDBConnector(ABC):
    """
    Abstract base class for vector database connectors.

    This class defines the interface that all vector database implementations
    must implement. It ensures consistency across different vector database
    providers (Qdrant, Pinecone, Weaviate, etc.) while allowing for
    implementation-specific optimizations.
    """

    def __init__(self, collections: Dict[CollectionType, str]):
        self._client: Optional[Any] = self._create_client()
        self._collections: Dict[CollectionType, str] = collections
        self._create_collections_if_not_exist()

    @abstractmethod
    def _create_client(self):
        """
        Creates a client for the vector database.

        This method should be implemented by subclasses to create and return
        a client instance for the specific vector database being used.
        """
        raise NotImplementedError

    @abstractmethod
    def _create_collections_if_not_exist(self):
        """
        Creates both incidents and documents collections in Vector DB if they don't exist.
        """
        raise NotImplementedError

    @abstractmethod
    def _upsert_vector(
        self,
        collection_type: CollectionType,
        vector_id: UUID,
        embedding: List[float],
        metadata: Dict,
    ) -> bool:
        """
        Generic method to upsert a vector into any collection.

        This is the concrete implementation method that subclasses must implement.

        Args:
            collection_type (CollectionType): The type of collection to upsert into.
            vector_id (UUID): The unique identifier for the vector.
            embedding (List[float]): The vector embedding.
            metadata (Dict): Metadata to store with the vector.

        Returns:
            bool: True if the upsert was successful, False otherwise.
        """
        raise NotImplementedError

    @abstractmethod
    def _delete_vector(self, collection_type: CollectionType, vector_id: UUID) -> bool:
        """
        Generic method to delete a vector from any collection.

        This is the concrete implementation method that subclasses must implement.

        Args:
            collection_type (CollectionType): The type of collection to delete from.
            vector_id (UUID): The unique identifier for the vector to delete.

        Returns:
            bool: True if the deletion was successful, False otherwise.
        """
        raise NotImplementedError

    @abstractmethod
    def _find_similar_vectors(
        self, collection_type: CollectionType, embedding: List[float], top_k: int = 5
    ) -> List[Dict]:
        """
        Generic method to find similar vectors in any collection.

        This is the concrete implementation method that subclasses must implement.

        Args:
            collection_type (CollectionType): The type of collection to search in.
            embedding (List[float]): The query vector embedding.
            top_k (int): The number of similar vectors to return.

        Returns:
            List[Dict]: A list of dictionaries containing payload and score.
        """
        raise NotImplementedError

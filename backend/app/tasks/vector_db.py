"""
Celery Tasks for Vector DB Operations
====================================

This module contains Celery tasks for asynchronous vector database operations.
These tasks handle embedding generation and vector DB upsert operations
for both incidents and documents in the background, ensuring that main operations remain non-blocking.
"""

from typing import Any, Dict, Optional
from uuid import UUID

from database.core import SessionLocal
from db_services import documents as documents_db_service
from entities.documents import SyncStatusEnum
from services.content_extractor import ContentExtractor
from utils.celery_worker import celery_app
from utils.logger import get_service_logger
from vector_db.search_service import VectorSearchService

# Initialize logger for vector DB tasks
logger = get_service_logger("vector_db_tasks")

# Document type mappings - centralized configuration
DOCUMENT_TYPES = {
    "application/pdf": ".pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ".docx",
    "application/msword": ".doc",
    "text/csv": ".csv",
}

# File extension to MIME type mapping
EXT_TO_MIME = {ext: mime for mime, ext in DOCUMENT_TYPES.items()}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def upsert_incident_embedding_task(self, incident_id: str) -> Dict[str, Any]:
    """
    Async task to upsert incident embedding to vector DB.

    This task takes an incident ID, retrieves the incident data from the database,
    generates an embedding, and upserts it to the Qdrant incidents collection using
    the VectorSearchService directly. The upsert operation handles both creation of
    new incidents and updating of existing ones based on the incident ID. It includes
    retry logic for handling transient failures and comprehensive logging.

    Args:
        incident_id (str): The UUID string of the incident to process

    Returns:
        Dict[str, Any]: Task result with status and incident_id

    Raises:
        Exception: Re-raised after max retries for permanent failures

    Security:
        - Never logs sensitive incident content
        - Handles all error cases gracefully
    """
    try:
        logger.info(f"Starting async embedding upsert task for incident {incident_id}")

        # Convert string ID to UUID
        incident_uuid = UUID(incident_id)

        # Get database session and vector search service
        db = SessionLocal()
        try:
            vector_service = VectorSearchService()

            # Perform the upsert operation using the vector search service directly
            success = vector_service.upsert_incident(db, incident_uuid)
        finally:
            db.close()

        if not success:
            raise Exception("Upsert operation failed - service returned False")

        logger.info(
            f"Successfully completed async embedding upsert for incident {incident_id}"
        )
        return {
            "status": "success",
            "incident_id": incident_id,
            "task_id": self.request.id,
        }

    except Exception as e:
        logger.error(f"Embedding upsert task failed for incident {incident_id}: {e}")

        # Retry logic with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 60 * (2**self.request.retries)  # Exponential backoff
            logger.warning(
                f"Retrying embedding upsert task in {countdown} seconds (attempt {self.request.retries + 1}/{self.max_retries})"
            )
            raise self.retry(countdown=countdown, exc=e)
        else:
            logger.error(
                f"Max retries exceeded for embedding upsert task for incident {incident_id}"
            )
            raise


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def upsert_document_embedding_task(self, document_id: str) -> Dict[str, Any]:
    """
    Async task to upsert document embedding to vector DB.

    This task takes a document ID, retrieves the document data from the database,
    generates an embedding, and upserts it to the Qdrant documents collection using
    the VectorSearchService directly. The upsert operation handles both creation of
    new documents and updating of existing ones based on the document ID. It includes
    retry logic for handling transient failures and comprehensive logging.

    Args:
        document_id (str): The UUID string of the document to process

    Returns:
        Dict[str, Any]: Task result with status and document_id

    Raises:
        Exception: Re-raised after max retries for permanent failures

    Security:
        - Never logs sensitive document content
        - Handles all error cases gracefully
    """
    try:
        logger.info(f"Starting async embedding upsert task for document {document_id}")

        # Convert string ID to UUID
        document_uuid = UUID(document_id)

        # Get database session and vector search service
        db = SessionLocal()
        try:
            vector_service = VectorSearchService()

            # Perform the upsert operation using the vector search service directly
            success = vector_service.upsert_document(db, document_uuid)
        finally:
            db.close()

        if not success:
            raise Exception("Upsert operation failed - service returned False")

        logger.info(
            f"Successfully completed async embedding upsert for document {document_id}"
        )
        return {
            "status": "success",
            "document_id": document_id,
            "task_id": self.request.id,
        }

    except Exception as e:
        logger.error(
            f"Document embedding upsert task failed for document {document_id}: {e}"
        )

        # Retry logic with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 60 * (2**self.request.retries)  # Exponential backoff
            logger.warning(
                f"Retrying document embedding upsert task in {countdown} seconds (attempt {self.request.retries + 1}/{self.max_retries})"
            )
            raise self.retry(countdown=countdown, exc=e)
        else:
            logger.error(
                f"Max retries exceeded for document embedding upsert task for document {document_id}"
            )
            raise


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def process_document_task(
    self, document_id: str, operation: str = "create"
) -> Dict[str, Any]:
    """
    Universal document processing task that handles all document operations.

    This single task handles:
    - File uploads (extract content from uploaded files)
    - External URLs (fetch content from web sources)
    - Updates (re-extract and re-index content)
    - Chunking and vector embedding generation
    - Vector database operations

    Args:
        document_id (str): The UUID string of the document to process
        operation (str): Type of operation - "create", "update", "refresh"

    Returns:
        Dict[str, Any]: Task result with status and document_id
    """
    try:
        logger.info(
            f"Starting document processing task for {document_id} (operation: {operation})"
        )

        # Convert string ID to UUID
        document_uuid = UUID(document_id)

        # Get database session
        db = SessionLocal()
        try:
            # Get the document
            document = documents_db_service.get_document_by_id(db, document_uuid)
            if not document:
                raise Exception(f"Document with ID {document_id} not found")

            # Update sync status to processing
            documents_db_service.update_document_sync_status(
                db, document_uuid, SyncStatusEnum.PROCESSING.value
            )

            extracted_content = None

            # Handle different document sources
            if document.meta_data and document.meta_data.get("external_url"):
                # External URL document
                external_url = document.meta_data.get("external_url")
                logger.info(f"Processing external URL document: {external_url}")
                import asyncio

                extracted_content = asyncio.run(_fetch_url_content(external_url))

            elif document.meta_data and document.meta_data.get("file_path"):
                # File-based document
                logger.info(
                    f"Processing file document: {document.meta_data['file_path']}"
                )

                content_extractor = ContentExtractor()
                import asyncio
                from pathlib import Path

                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    extracted_content = loop.run_until_complete(
                        content_extractor.extract_content(
                            Path(document.meta_data["file_path"]),
                            document.meta_data.get("mime_type", "text/plain"),
                        )
                    )
                except Exception as extract_error:
                    logger.error(f"Content extraction failed: {extract_error}")
                    extracted_content = None

            elif document.content:
                # Text-based document (already has content)
                logger.info("Processing text document with existing content")
                extracted_content = document.content

            else:
                logger.warning(
                    f"Document {document_id} has no processable content source"
                )
                documents_db_service.update_document_sync_status(
                    db, document_uuid, SyncStatusEnum.FAILED.value
                )
                return {
                    "status": "failed",
                    "document_id": document_id,
                    "message": "No processable content source found",
                    "task_id": self.request.id,
                }

            # Update document with extracted content if we got any
            if extracted_content and (
                not document.content or operation in ["update", "refresh"]
            ):
                update_data = {"content": extracted_content}
                documents_db_service.update_document(db, document_uuid, update_data)
                logger.info(
                    f"Updated document {document_id} with extracted content ({len(extracted_content)} chars)"
                )
            elif not extracted_content:
                logger.warning(f"Could not extract content for document {document_id}")
                documents_db_service.update_document_sync_status(
                    db, document_uuid, SyncStatusEnum.FAILED.value
                )
                return {
                    "status": "failed",
                    "document_id": document_id,
                    "message": "Content extraction failed",
                    "task_id": self.request.id,
                }

            # Generate and upsert vector embeddings
            logger.info(f"Generating vector embeddings for document {document_id}")
            vector_service = VectorSearchService()
            success = vector_service.upsert_document(db, document_uuid)

            if not success:
                raise Exception(
                    "Vector upsert operation failed - service returned False"
                )

            # Update sync status to synced
            documents_db_service.update_document_sync_status(
                db, document_uuid, SyncStatusEnum.SYNCED.value
            )

        finally:
            db.close()

        logger.info(f"Successfully completed document processing for {document_id}")
        return {
            "status": "success",
            "document_id": document_id,
            "operation": operation,
            "task_id": self.request.id,
        }

    except Exception as e:
        logger.error(
            f"External document sync task failed for document {document_id}: {e}"
        )

        # Retry logic with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 60 * (2**self.request.retries)  # Exponential backoff
            logger.warning(
                f"Retrying external document sync task in {countdown} seconds (attempt {self.request.retries + 1}/{self.max_retries})"
            )
            raise self.retry(countdown=countdown, exc=e)
        else:
            logger.error(
                f"Max retries exceeded for external document sync task for document {document_id}"
            )
            # Mark document sync as failed
            try:
                db = SessionLocal()
                try:
                    documents_db_service.update_document_sync_status(
                        db, UUID(document_id), "failed"
                    )
                finally:
                    db.close()
            except Exception as update_error:
                logger.error(f"Failed to update document sync status: {update_error}")
            raise


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def batch_sync_documents_task(
    self, knowledge_base_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Async task to batch sync multiple documents to vector DB.

    This task can sync all documents or documents from a specific knowledge base.
    It processes documents in batches and triggers individual upsert tasks.

    Args:
        knowledge_base_id (str, optional): Knowledge base ID to filter documents

    Returns:
        Dict[str, Any]: Task result with status and processing summary
    """
    try:
        logger.info(
            f"Starting batch document sync task for KB {knowledge_base_id or 'all'}"
        )

        # Get database session
        db = SessionLocal()
        try:
            if knowledge_base_id:
                # Get documents for specific knowledge base
                documents, _ = documents_db_service.get_documents_by_knowledge_base(
                    db, UUID(knowledge_base_id), offset=0, limit=1000
                )
            else:
                # Get all documents
                documents, _ = documents_db_service.get_all_documents(
                    db, offset=0, limit=1000
                )

            # Trigger individual processing tasks for each document
            task_ids = []
            for document in documents:
                # Use the universal document processing task
                task = process_document_task.delay(str(document.id), "refresh")
                task_ids.append(task.id)

        finally:
            db.close()

        logger.info(f"Successfully triggered {len(task_ids)} document processing tasks")
        return {
            "status": "success",
            "knowledge_base_id": knowledge_base_id,
            "documents_processed": len(task_ids),
            "task_ids": task_ids,
            "task_id": self.request.id,
        }

    except Exception as e:
        logger.error(f"Batch document sync task failed: {e}")

        # Retry logic with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 60 * (2**self.request.retries)
            logger.warning(
                f"Retrying batch document sync task in {countdown} seconds (attempt {self.request.retries + 1}/{self.max_retries})"
            )
            raise self.retry(countdown=countdown, exc=e)
        else:
            logger.error("Max retries exceeded for batch document sync task")
            raise


async def _fetch_url_content(url: str) -> Optional[str]:
    """
    Fetch content from external URL.

    This function handles various web sources and can be extended
    to support different types of web crawlers and connectors.

    Supported content types:
    - HTML pages (text/html)
    - JSON content (application/json)
    - Plain text (text/*)
    - PDF documents (application/pdf)
    - Word documents (application/vnd.openxmlformats-officedocument.wordprocessingml.document)
    - Legacy Word documents (application/msword)
    - CSV files (text/csv)

    Note: Content type detection uses both HTTP headers and URL file extensions
    for better reliability, as many servers don't set proper Content-Type headers.
    """
    try:
        from pathlib import Path
        from urllib.parse import urlparse

        import aiohttp
        from bs4 import BeautifulSoup

        logger.info(f"Fetching content from URL: {url}")

        async with aiohttp.ClientSession() as session:
            async with session.get(
                url, timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    content_type = response.headers.get("content-type", "").lower()

                    # Also check file extension from URL as fallback
                    parsed_url = urlparse(url)
                    file_ext = Path(parsed_url.path).suffix.lower()

                    # Determine if this is a document that needs special processing
                    is_document_type = _is_document_type(content_type, file_ext)

                    if is_document_type:
                        # Document type - download and extract using ContentExtractor
                        detected_mime_type = _detect_mime_type(content_type, file_ext)
                        return await _process_document_from_url(
                            session, response, url, detected_mime_type
                        )

                    elif "text/html" in content_type:
                        # HTML content - extract text
                        html_content = await response.text()
                        soup = BeautifulSoup(html_content, "html.parser")

                        # Remove script and style elements
                        for script in soup(["script", "style"]):
                            script.decompose()

                        # Get text content
                        text_content = soup.get_text()

                        # Clean up whitespace
                        lines = (line.strip() for line in text_content.splitlines())
                        chunks = (
                            phrase.strip()
                            for line in lines
                            for phrase in line.split("  ")
                        )
                        text_content = " ".join(chunk for chunk in chunks if chunk)

                        return text_content

                    elif "application/json" in content_type:
                        # JSON content
                        json_content = await response.json()
                        return str(json_content)

                    elif "text/" in content_type:
                        # Plain text content
                        return await response.text()

                    else:
                        logger.warning(
                            f"Unsupported content type for URL {url}: {content_type} (file extension: {file_ext})"
                        )
                        return None

                else:
                    logger.error(f"Failed to fetch URL {url}: HTTP {response.status}")
                    return None

    except Exception as e:
        logger.error(f"Error fetching content from URL {url}: {str(e)}")
        return None


async def _process_document_from_url(
    session, response, url: str, content_type: str
) -> Optional[str]:
    """
    Process document content from URL using ContentExtractor.

    This helper function handles the temporary file creation, document download,
    content extraction, and cleanup in a clean, integrated way.

    Args:
        session: aiohttp session
        response: aiohttp response object
        url: URL being processed
        content_type: MIME type of the document

    Returns:
        Extracted text content or None if extraction fails
    """
    import os
    import tempfile
    from pathlib import Path

    # Get file extension from content type
    file_ext = DOCUMENT_TYPES.get(content_type, ".tmp")
    logger.info(f"Processing {content_type} document from URL: {url}")

    # Create temporary file for document
    temp_fd, temp_path = tempfile.mkstemp(suffix=file_ext)

    try:
        # Download document content
        document_content = await response.read()

        # Write to temporary file
        with os.fdopen(temp_fd, "wb") as tmp_file:
            tmp_file.write(document_content)

        # Extract content using ContentExtractor
        content_extractor = ContentExtractor()
        extracted_content = await content_extractor.extract_content(
            Path(temp_path), content_type
        )

        logger.info(
            f"Successfully extracted {len(extracted_content or '')} characters from {content_type} document"
        )
        return extracted_content

    except Exception as e:
        logger.error(
            f"Failed to process {content_type} document from URL {url}: {str(e)}"
        )
        return None

    finally:
        # Clean up temporary file
        try:
            os.unlink(temp_path)
        except OSError as e:
            logger.warning(f"Failed to clean up temporary file {temp_path}: {e}")


def _is_document_type(content_type: str, file_ext: str) -> bool:
    """
    Check if the content type or file extension indicates a document that needs special processing.

    Args:
        content_type: HTTP Content-Type header value (lowercased)
        file_ext: File extension from URL (lowercased, with dot)

    Returns:
        True if this is a document type that needs ContentExtractor processing
    """
    # Check content type first
    for doc_type in DOCUMENT_TYPES:
        if doc_type in content_type:
            return True

    # Check file extension as fallback
    if file_ext in EXT_TO_MIME:
        return True

    # Special case: generic content types that might be documents based on extension
    generic_types = {"application/octet-stream", "binary/octet-stream", ""}
    if (
        any(generic in content_type for generic in generic_types)
        and file_ext in EXT_TO_MIME
    ):
        return True

    return False


def _detect_mime_type(content_type: str, file_ext: str) -> str:
    """
    Detect the most likely MIME type based on content type header and file extension.

    Args:
        content_type: HTTP Content-Type header value (lowercased)
        file_ext: File extension from URL (lowercased, with dot)

    Returns:
        The detected MIME type string
    """
    # If we have a specific document content type, use it
    for doc_type in DOCUMENT_TYPES:
        if doc_type in content_type:
            return doc_type

    # Otherwise, try to determine from file extension
    if file_ext in EXT_TO_MIME:
        logger.info(
            f"Detected MIME type from extension {file_ext}: {EXT_TO_MIME[file_ext]}"
        )
        return EXT_TO_MIME[file_ext]

    # Default fallback
    return content_type or "application/octet-stream"

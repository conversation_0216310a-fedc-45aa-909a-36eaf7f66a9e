import enum
import uuid

from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Enum, ForeignKey, String, func
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship


class EventTypeEnum(enum.Enum):
    CREATED = "CREATED"
    UPDATED = "UPDATED"
    ESCALATED = "ESCALATED"
    RESOLVED = "RESOLVED"
    CLOSED = "CLOSED"
    COMMENT_ADDED = "COMMENT_ADDED"
    ASSIGNED = "ASSIGNED"
    PRIORITY_CHANGED = "PRIORITY_CHANGED"
    SEVERITY_CHANGED = "SEVERITY_CHANGED"
    ANALYSIS = "ANALYSIS"


class Event(Base):
    __tablename__ = "incident_events"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    event_name = Column(String(255), nullable=False)
    event_datetime = Column(DateTime(timezone=True), nullable=False)
    event_type = Column(Enum(EventTypeEnum), nullable=False)
    event_details = Column(JSON, nullable=False, default=dict)
    user_id = Column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
    )
    incident_id = Column(
        PG_UUID(as_uuid=True),
        ForeignKey("incidents.id", ondelete="CASCADE"),
        nullable=True,
    )
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    user = relationship("User", backref="events")
    incident = relationship("Incident", backref="events")

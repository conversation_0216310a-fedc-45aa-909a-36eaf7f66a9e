import uuid

from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, Text
from sqlalchemy.dialects.postgresql import UUID


class IncidentReport(Base):
    __tablename__ = "incident_report"

    executive_summary = Column(Text, primary_key=True)
    incident_id = Column(
        UUID(as_uuid=True),
        ForeignKey("incidents.id", ondelete="CASCADE"),
        nullable=False,
    )
    post_incident_actions = Column(JSON, nullable=True)
    retrospectives = Column(JSON, nullable=True)
    action_items = Column(JSON, nullable=True)

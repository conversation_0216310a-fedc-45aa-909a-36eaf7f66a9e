import enum
import uuid

from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Enum, ForeignKey, String, Text, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class DocumentTypeEnum(enum.Enum):
    """Enum for different document types."""

    PDF = "PDF"
    TEXT = "TEXT"
    URL = "URL"
    MARKDOWN = "MARKDOWN"
    DOCX = "DOCX"
    OTHER = "OTHER"


class SyncStatusEnum(enum.Enum):
    """Enum for document sync status."""

    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    SYNCED = "SYNCED"
    FAILED = "FAILED"


class Document(Base):
    __tablename__ = "documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    document_type = Column(Enum(DocumentTypeEnum), nullable=False)
    content = Column(Text, nullable=True)  # For text-based documents
    # Metadata for document management
    meta_data = Column(
        JSON, nullable=True
    )  # Additional metadata as JSON -> File Path, External Url, File size, MIME type, etc.
    # Timestamps and user tracking
    created_at = Column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )
    created_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=False
    )
    updated_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=False
    )

    # Sync tracking for external sources
    last_synced_at = Column(DateTime(timezone=True), nullable=True)
    sync_status = Column(
        Enum(SyncStatusEnum), nullable=False, default=SyncStatusEnum.PENDING
    )

    # Foreign key to knowledge base
    knowledge_base_id = Column(
        UUID(as_uuid=True),
        ForeignKey("knowledge_base.id", ondelete="CASCADE"),
        nullable=False,
    )

    # Relationships
    knowledge_base = relationship("KnowledgeBase", back_populates="documents")
    created_by_user = relationship("User", foreign_keys=[created_by])
    updated_by_user = relationship("User", foreign_keys=[updated_by])

    def __repr__(self):
        return (
            f"<Document(id={self.id}, name='{self.name}', type='{self.document_type}')>"
        )

import enum
import uuid

from database.core import Base
from sqlalchemy import Column, DateTime, Enum, ForeignKey, String, Text, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class KnowledgeBaseTypeEnum(enum.Enum):
    """Enum for different knowledge base types."""

    PROJECT_DOCUMENTATION = "PROJECT_DOCUMENTATION"
    EXTERNAL_DOCUMENTATION = "EXTERNAL_DOCUMENTATION"
    SYSTEM_ARCHITECTURE = "SYSTEM_ARCHITECTURE"
    SERVICE_DETAILS = "SERVICE_DETAILS"
    RUNBOOKS = "RUNBOOKS"
    OTHER = "OTHER"


class Service(Base):
    __tablename__ = "services"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    knowledge_base_id = Column(
        UUID(as_uuid=True),
        ForeignKey("knowledge_base.id", ondelete="CASCADE"),
        nullable=False,
    )
    knowledge_base = relationship("KnowledgeBase", back_populates="services")


class KnowledgeBase(Base):
    __tablename__ = "knowledge_base"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    kb_type = Column(Enum(KnowledgeBaseTypeEnum), nullable=False)
    created_at = Column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )
    created_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=False
    )
    updated_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=False
    )

    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
    )

    # Relationships
    project = relationship("Project", back_populates="knowledge_bases")
    documents = relationship(
        "Document", back_populates="knowledge_base", cascade="all, delete-orphan"
    )
    services = relationship(
        "Service", back_populates="knowledge_base", cascade="all, delete-orphan"
    )
    created_by_user = relationship("User", foreign_keys=[created_by])
    updated_by_user = relationship("User", foreign_keys=[updated_by])

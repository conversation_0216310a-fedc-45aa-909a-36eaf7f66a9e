import enum
import uuid

from database.core import Base
from sqlalchemy import (
    JSON,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Index,
    Text,
    UniqueConstraint,
    func,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from .runbooks import Runbook
from .user import User


class SeverityEnum(enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class PriorityEnum(enum.Enum):
    P0 = "p0"  # the site is down and all work stops, until this issue is resolved.
    P1 = "p1"  # finish this task to unblock someone else, required to be done before other things
    P2 = "p2"  # ordinary flow of work
    P3 = "p3"  # nice to have, but not required
    P4 = "p4"  # informational only


class IncidentTypeEnum(enum.Enum):
    OUTAGE = "outage"
    DEGRADATION = "degradation"
    SECURITY = "security"
    PERFORMANCE = "performance"
    OTHER = "other"


class StatusEnum(enum.Enum):
    OPEN = "open"
    ACTIVE = "active"
    RESOLVED = "resolved"
    CLOSED = "closed"


class Incident(Base):
    __tablename__ = "incidents"
    __table_args__ = (
        UniqueConstraint("incident_number", name="uq_incident_number"),
        Index("ix_status", "status"),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    incident_number = Column(Text, nullable=False, unique=True, index=True)
    title = Column(Text, nullable=False)
    summary = Column(Text, nullable=True)
    priority = Column(Enum(PriorityEnum), nullable=True)
    severity = Column(Enum(SeverityEnum), nullable=True)
    incident_type = Column(Enum(IncidentTypeEnum), nullable=False)
    status = Column(Enum(StatusEnum), nullable=False, default=StatusEnum.OPEN)
    reported_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True
    )
    reported_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    reporter = relationship("User", back_populates="reported_incidents")
    runbooks = relationship(
        "Runbook", back_populates="incident", cascade="all, delete-orphan"
    )
    incident_detail = relationship(
        "IncidentDetail",
        back_populates="incident",
        uselist=False,
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<Incident(id='{self.id}', incident_number='{self.incident_number}', title='{self.title}')>"


class IncidentDetail(Base):
    __tablename__ = "incident_details"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    incident_id = Column(
        UUID(as_uuid=True),
        ForeignKey("incidents.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
    )
    affected_services = Column(JSON, nullable=True, default=list)
    tags = Column(JSON, nullable=True, default=list)
    incident_details = Column(Text, nullable=True)
    attachments = Column(JSON, nullable=True, default=list)

    # Root cause analysis fields
    root_cause = Column(Text, nullable=True)
    immediate_action = Column(Text, nullable=True)
    impact_forecast = Column(Text, nullable=True)
    cascading_risks = Column(Text, nullable=True)

    # Relationships
    incident = relationship("Incident", back_populates="incident_detail")

    def __repr__(self):
        return f"<IncidentDetail(id='{self.id}', incident_id='{self.incident_id}')>"

import uuid

from database.core import Base
from sqlalchemy import Column, Computed, DateTime, ForeignKey, Interval, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class IncidentMetric(Base):
    __tablename__ = "incident_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    incident_id = Column(
        UUID(as_uuid=True),
        ForeignKey("incidents.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    # Metrics
    detected_time = Column(DateTime(timezone=True), nullable=True)
    reported_time = Column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )
    acknowledged_time = Column(DateTime(timezone=True), nullable=True)
    resolved_time = Column(DateTime(timezone=True), nullable=True)
    closed_time = Column(DateTime(timezone=True), nullable=True)

    # Computed fields
    time_to_report = Column(
        Interval,
        Computed(
            "reported_time - COALESCE(detected_time, reported_time)", persisted=True
        ),
        nullable=True,
    )
    time_to_acknowledge = Column(
        Interval,
        Computed("acknowledged_time - reported_time", persisted=True),
        nullable=True,
    )
    time_to_resolve = Column(
        Interval,
        Computed("resolved_time - acknowledged_time", persisted=True),
        nullable=True,
    )
    time_to_closure = Column(
        Interval,
        Computed("closed_time - resolved_time", persisted=True),
        nullable=True,
    )
    total_downtime = Column(
        Interval,
        Computed(
            "resolved_time - COALESCE(detected_time, reported_time)",
            persisted=True,
        ),
        nullable=True,
    )

    incident = relationship("Incident", backref="metrics")

    def __repr__(self):
        return f"<IncidentMetric(id='{self.id}', incident_id='{self.incident_id}', reported_time='{self.reported_time} ack_time={self.acknowledged_time} resolve_time={self.resolved_time} closed_time={self.closed_time}')>"

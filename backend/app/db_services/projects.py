from typing import List, Optional, <PERSON><PERSON>
from uuid import UUID

from database.core import DbSession
from entities.projects import Project
from sqlalchemy.orm import joinedload


def create_project(db: DbSession, project: Project) -> Project:
    """Create a new project."""
    db.add(project)
    db.commit()
    db.refresh(project)
    return project


def get_project_by_id(db: DbSession, project_id: UUID) -> Optional[Project]:
    """Get a project by its ID."""
    return (
        db.query(Project)
        .options(joinedload(Project.knowledge_bases))
        .filter(Project.id == project_id)
        .first()
    )


def get_projects_by_user(
    db: DbSession, user_id: UUID, offset: int = 0, limit: int = 10
) -> <PERSON>ple[List[Project], int]:
    """Get projects created by a specific user with pagination."""
    query = db.query(Project).filter(Project.created_by == user_id)
    total = query.count()
    projects = query.offset(offset).limit(limit).all()
    return projects, total


def get_all_projects(
    db: DbSession, offset: int = 0, limit: int = 10
) -> <PERSON><PERSON>[List[Project], int]:
    """Get all projects with pagination."""
    query = db.query(Project).options(joinedload(Project.knowledge_bases))
    total = query.count()
    projects = query.offset(offset).limit(limit).all()
    return projects, total


def update_project(
    db: DbSession, project_id: UUID, update_data: dict
) -> Optional[Project]:
    """Update a project."""
    project = get_project_by_id(db, project_id)
    if not project:
        return None

    for key, value in update_data.items():
        if hasattr(project, key):
            setattr(project, key, value)

    db.commit()
    db.refresh(project)
    return project


def delete_project(db: DbSession, project_id: UUID) -> bool:
    """Delete a project."""
    project = get_project_by_id(db, project_id)
    if not project:
        return False

    db.delete(project)
    db.commit()
    return True


def search_projects_by_name(
    db: DbSession, search_term: str, offset: int = 0, limit: int = 10
) -> Tuple[List[Project], int]:
    """Search projects by name."""
    query = db.query(Project).filter(Project.name.ilike(f"%{search_term}%"))
    total = query.count()
    projects = query.offset(offset).limit(limit).all()
    return projects, total

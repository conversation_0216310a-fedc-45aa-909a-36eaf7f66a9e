from datetime import datetime, timezone
from typing import List, Optional, Tuple
from uuid import UUID

from database.core import DbSession
from entities.documents import Document, DocumentTypeEnum, SyncStatusEnum
from sqlalchemy.orm import joinedload


def create_document(db: DbSession, document: Document) -> Document:
    """Create a new document."""
    db.add(document)
    db.commit()
    db.refresh(document)
    return document


def get_document_by_id(db: DbSession, document_id: UUID) -> Optional[Document]:
    """Get a document by its ID."""
    return (
        db.query(Document)
        .options(
            joinedload(Document.knowledge_base),
            joinedload(Document.created_by_user),
            joinedload(Document.updated_by_user),
        )
        .filter(Document.id == document_id)
        .first()
    )


def get_documents_by_knowledge_base(
    db: DbSession, knowledge_base_id: UUID, offset: int = 0, limit: int = 10
) -> <PERSON><PERSON>[List[Document], int]:
    """Get documents by knowledge base ID with pagination."""
    query = db.query(Document).filter(Document.knowledge_base_id == knowledge_base_id)
    total = query.count()
    documents = query.offset(offset).limit(limit).all()
    return documents, total


def get_documents_by_type(
    db: DbSession,
    document_type: DocumentTypeEnum,
    knowledge_base_id: Optional[UUID] = None,
    offset: int = 0,
    limit: int = 10,
) -> Tuple[List[Document], int]:
    """Get documents by type with optional knowledge base filter."""
    query = db.query(Document).filter(Document.document_type == document_type.value)

    if knowledge_base_id:
        query = query.filter(Document.knowledge_base_id == knowledge_base_id)

    total = query.count()
    documents = query.offset(offset).limit(limit).all()
    return documents, total


def update_document(
    db: DbSession, document_id: UUID, update_data: dict
) -> Optional[Document]:
    """Update a document."""
    document = get_document_by_id(db, document_id)
    if not document:
        return None

    for key, value in update_data.items():
        if hasattr(document, key):
            setattr(document, key, value)

    # Update the updated_at timestamp
    document.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(document)
    return document


def delete_document(db: DbSession, document_id: UUID) -> bool:
    """Delete a document."""
    document = get_document_by_id(db, document_id)
    if not document:
        return False

    db.delete(document)
    db.commit()
    return True


def search_documents_by_name(
    db: DbSession,
    search_term: str,
    knowledge_base_id: Optional[UUID] = None,
    offset: int = 0,
    limit: int = 10,
) -> Tuple[List[Document], int]:
    """Search documents by name."""
    query = db.query(Document).filter(Document.name.ilike(f"%{search_term}%"))

    if knowledge_base_id:
        query = query.filter(Document.knowledge_base_id == knowledge_base_id)

    total = query.count()
    documents = query.offset(offset).limit(limit).all()
    return documents, total


def get_documents_pending_sync(db: DbSession, limit: int = 50) -> List[Document]:
    """Get documents that need to be synced (for external URLs, etc.)."""
    return (
        db.query(Document)
        .filter(Document.sync_status == SyncStatusEnum.PENDING)
        .limit(limit)
        .all()
    )


def update_document_sync_status(
    db: DbSession,
    document_id: UUID,
    sync_status: str,
    last_synced_at: Optional[datetime] = None,
) -> Optional[Document]:
    """Update document sync status."""
    document = get_document_by_id(db, document_id)
    if not document:
        return None

    document.sync_status = sync_status
    if last_synced_at:
        document.last_synced_at = last_synced_at
    else:
        document.last_synced_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(document)
    return document


def get_all_documents(
    db: DbSession, offset: int = 0, limit: int = 10
) -> Tuple[List[Document], int]:
    """Get all documents with pagination."""
    query = db.query(Document).options(
        joinedload(Document.knowledge_base), joinedload(Document.created_by_user)
    )
    total = query.count()
    documents = query.offset(offset).limit(limit).all()
    return documents, total

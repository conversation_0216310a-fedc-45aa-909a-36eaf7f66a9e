from uuid import UUID

from database.core import DbSession
from entities.runbooks import Runbook, RunbookStep


def get_steps_by_runbook(db: DbSession, runbook_id: UUID):
    return (
        db.query(RunbookStep)
        .filter(RunbookStep.runbook_id == runbook_id)
        .order_by(RunbookStep.step_order)
        .all()
    )


def get_step_by_id(db: DbSession, step_id: UUID):
    return db.query(RunbookStep).filter_by(id=step_id).first()


def list_runbooks(db: DbSession, incident_id: UUID):
    return db.query(Runbook).filter_by(incident_id=incident_id).all()


def get_runbook_by_id(db: DbSession, runbook_id: UUID):
    return db.query(Runbook).filter_by(id=runbook_id).first()

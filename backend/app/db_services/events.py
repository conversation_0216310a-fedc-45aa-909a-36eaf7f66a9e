from typing import List, <PERSON>ple
from uuid import UUID

from database.core import DbSession
from entities.events import Event
from routes.events.models import EventResponse
from sqlalchemy.orm import joinedload


def get_incident_events(
    db: DbSession,
    incident_id: UUID,
    skip: int = 0,
    limit: int = 3,
    sort: str = "asc",
) -> Tuple[List[Event], int]:
    query = (
        db.query(Event)
        .options(joinedload(Event.user))
        .filter(Event.incident_id == incident_id)
    )
    total = query.count()
    if sort == "desc":
        results = (
            query.order_by(Event.event_datetime.desc()).offset(skip).limit(limit).all()
        )
    else:
        results = (
            query.order_by(Event.event_datetime.asc()).offset(skip).limit(limit).all()
        )
    return results, total


def get_event_by_id(db: DbSession, event_id: UUID):
    return (
        db.query(Event)
        .options(joinedload(Event.user))
        .filter(Event.id == event_id)
        .first()
    )


def get_incident_events_response(
    db: DbSession,
    incident_id: UUID,
    skip: int = 0,
    limit: int = 3,
    sort: str = "asc",
):
    events, total = get_incident_events(db, incident_id, skip, limit, sort)
    return [EventResponse.from_orm(event) for event in events], total

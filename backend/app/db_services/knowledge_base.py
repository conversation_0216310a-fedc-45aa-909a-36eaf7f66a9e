from typing import List, Optional, <PERSON><PERSON>
from uuid import UUID

from database.core import DbSession
from entities.knowledge_base import KnowledgeBase, KnowledgeBaseTypeEnum
from sqlalchemy.orm import joinedload


def create_knowledge_base(
    db: DbSession, knowledge_base: KnowledgeBase
) -> KnowledgeBase:
    """Create a new knowledge base."""
    db.add(knowledge_base)
    db.commit()
    db.refresh(knowledge_base)
    return knowledge_base


def get_kb_entry_by_id(db: DbSession, entry_id: UUID) -> Optional[KnowledgeBase]:
    """Get a knowledge base by its ID."""
    return (
        db.query(KnowledgeBase)
        .options(
            joinedload(KnowledgeBase.project),
            joinedload(KnowledgeBase.documents),
            joinedload(KnowledgeBase.services),
        )
        .filter(KnowledgeBase.id == entry_id)
        .first()
    )


def get_kb_entries_by_project_id(
    db: DbSession, project_id: UUID
) -> List[KnowledgeBase]:
    """Get all knowledge bases for a project."""
    return (
        db.query(KnowledgeBase)
        .options(joinedload(KnowledgeBase.documents))
        .filter(KnowledgeBase.project_id == project_id)
        .all()
    )


def get_kb_entry_by_project_and_type(
    db: DbSession, project_id: UUID, kb_type: KnowledgeBaseTypeEnum
) -> Optional[KnowledgeBase]:
    """Get a specific knowledge base by project and type."""
    return (
        db.query(KnowledgeBase)
        .filter(
            KnowledgeBase.project_id == project_id,
            KnowledgeBase.kb_type == kb_type.value,
        )
        .first()
    )


def get_kb_entries(
    db: DbSession, offset: int = 0, limit: int = 10
) -> Tuple[List[KnowledgeBase], int]:
    """Get all knowledge bases with pagination."""
    query = db.query(KnowledgeBase).options(
        joinedload(KnowledgeBase.project), joinedload(KnowledgeBase.documents)
    )
    total = query.count()
    items = query.offset(offset).limit(limit).all()
    return items, total


def update_knowledge_base(
    db: DbSession, kb_id: UUID, update_data: dict
) -> Optional[KnowledgeBase]:
    """Update a knowledge base."""
    kb = get_kb_entry_by_id(db, kb_id)
    if not kb:
        return None

    for key, value in update_data.items():
        if hasattr(kb, key):
            setattr(kb, key, value)

    db.commit()
    db.refresh(kb)
    return kb


def delete_knowledge_base(db: DbSession, kb_id: UUID) -> bool:
    """Delete a knowledge base."""
    kb = get_kb_entry_by_id(db, kb_id)
    if not kb:
        return False

    db.delete(kb)
    db.commit()
    return True

["tests/routes/auth/test_auth_controller.py::test_forgot_password", "tests/routes/auth/test_auth_controller.py::test_forgot_password_with_invalid_email", "tests/routes/auth/test_auth_controller.py::test_login", "tests/routes/auth/test_auth_controller.py::test_login_nonexistent_user", "tests/routes/auth/test_auth_controller.py::test_login_wrong_password", "tests/routes/auth/test_auth_controller.py::test_rate_limiting", "tests/routes/auth/test_auth_controller.py::test_register_duplicate_email", "tests/routes/auth/test_auth_controller.py::test_register_user", "tests/routes/auth/test_auth_controller.py::test_register_with_invalid_email", "tests/routes/auth/test_auth_controller.py::test_register_with_invalid_password", "tests/routes/auth/test_auth_controller.py::test_reset_password", "tests/routes/auth/test_auth_controller.py::test_reset_password_with_mismatched_passwords", "tests/routes/auth/test_auth_controller.py::test_reset_password_with_short_password", "tests/routes/auth/test_auth_controller.py::test_verify_token", "tests/routes/incidents/test_incidents_controller.py::test_create_incident", "tests/routes/incidents/test_incidents_controller.py::test_create_incident_validation_errors", "tests/routes/incidents/test_incidents_controller.py::test_create_incident_with_all_fields", "tests/routes/incidents/test_incidents_controller.py::test_create_incident_with_minimal_data", "tests/routes/incidents/test_incidents_controller.py::test_delete_incident", "tests/routes/incidents/test_incidents_controller.py::test_delete_nonexistent_incident", "tests/routes/incidents/test_incidents_controller.py::test_get_incident", "tests/routes/incidents/test_incidents_controller.py::test_get_incident_ai_analysis", "tests/routes/incidents/test_incidents_controller.py::test_get_incident_by_number", "tests/routes/incidents/test_incidents_controller.py::test_get_incident_details", "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_pagination", "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_filters", "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_multiple_filters", "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_priority_filter", "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_severity_filter", "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_status_filter", "tests/routes/incidents/test_incidents_controller.py::test_get_nonexistent_incident", "tests/routes/incidents/test_incidents_controller.py::test_get_nonexistent_incident_details", "tests/routes/incidents/test_incidents_controller.py::test_invalid_uuid_format", "tests/routes/incidents/test_incidents_controller.py::test_pagination_edge_cases", "tests/routes/incidents/test_incidents_controller.py::test_unauthorized_access", "tests/routes/incidents/test_incidents_controller.py::test_update_incident", "tests/routes/incidents/test_incidents_controller.py::test_update_incident_details", "tests/routes/incidents/test_incidents_controller.py::test_update_incident_partial", "tests/routes/incidents/test_incidents_controller.py::test_update_nonexistent_incident", "tests/routes/incidents/test_incidents_controller.py::test_update_nonexistent_incident_details", "tests/routes/users/test_users_controller.py::test_change_password_mismatch", "tests/routes/users/test_users_controller.py::test_change_password_success", "tests/routes/users/test_users_controller.py::test_change_password_wrong_current", "tests/routes/users/test_users_controller.py::test_delete_user", "tests/routes/users/test_users_controller.py::test_get_current_user", "tests/routes/users/test_users_controller.py::test_unauthorized_access"]
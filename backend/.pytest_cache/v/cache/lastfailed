{"tests/routes/incidents/test_incidents_improved.py::TestIncidentController::test_create_incident_with_vector_embedding": true, "tests/routes/incidents/test_incidents_improved.py::TestIncidentController::test_get_incident_success": true, "tests/routes/incidents/test_incidents_improved.py::TestIncidentController::test_update_incident_success": true, "tests/routes/incidents/test_incidents_improved.py::TestIncidentController::test_delete_incident_success": true, "tests/routes/incidents/test_incidents_improved.py::TestIncidentController::test_get_incidents_pagination": true, "tests/routes/incidents/test_incidents_improved.py::TestIncidentController::test_get_incident_ai_analysis_mocked": true, "tests/routes/incidents/test_incidents_improved.py::TestIncidentControllerIntegration::test_incident_lifecycle_with_mocked_services": true, "tests/test_basic_functionality.py::TestMockInfrastructure::test_mock_celery_app": true}
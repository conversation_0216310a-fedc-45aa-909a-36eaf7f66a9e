{"tests/routes/incidents/test_incidents_controller.py::test_get_incident": true, "tests/routes/incidents/test_incidents_controller.py::test_update_incident": true, "tests/routes/incidents/test_incidents_controller.py::test_delete_incident": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_pagination": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_filters": true, "tests/routes/incidents/test_incidents_controller.py::test_unauthorized_access": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incident_by_number": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incident_details": true, "tests/routes/incidents/test_incidents_controller.py::test_update_incident_details": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incident_ai_analysis": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_priority_filter": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_severity_filter": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_status_filter": true, "tests/routes/incidents/test_incidents_controller.py::test_create_incident_validation_errors": true, "tests/routes/incidents/test_incidents_controller.py::test_create_incident_with_minimal_data": true, "tests/routes/incidents/test_incidents_controller.py::test_update_incident_partial": true, "tests/routes/incidents/test_incidents_controller.py::test_get_nonexistent_incident": true, "tests/routes/incidents/test_incidents_controller.py::test_get_nonexistent_incident_details": true, "tests/routes/incidents/test_incidents_controller.py::test_update_nonexistent_incident": true, "tests/routes/incidents/test_incidents_controller.py::test_update_nonexistent_incident_details": true, "tests/routes/incidents/test_incidents_controller.py::test_delete_nonexistent_incident": true, "tests/routes/incidents/test_incidents_controller.py::test_get_incidents_with_multiple_filters": true, "tests/routes/incidents/test_incidents_controller.py::test_pagination_edge_cases": true, "tests/routes/incidents/test_incidents_controller.py::test_create_incident_with_all_fields": true, "tests/routes/incidents/test_incidents_controller.py::test_invalid_uuid_format": true, "tests/routes/users/test_users_controller.py::test_get_current_user": true, "tests/routes/users/test_users_controller.py::test_change_password_success": true, "tests/routes/users/test_users_controller.py::test_change_password_wrong_current": true, "tests/routes/users/test_users_controller.py::test_change_password_mismatch": true, "tests/routes/users/test_users_controller.py::test_delete_user": true}
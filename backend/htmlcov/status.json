{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "0b256d66e1c6dc06603718828a66790f", "files": {"z_c78f3e75ce3283c5___init___py": {"hash": "348ecc4af7e680fbee7b58df9fc60eee", "index": {"url": "z_c78f3e75ce3283c5___init___py.html", "file": "app/agents/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_agent_runner_py": {"hash": "237df4e5e729fb3ff393728382d7a2c8", "index": {"url": "z_c78f3e75ce3283c5_agent_runner_py.html", "file": "app/agents/agent_runner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_guardrail_py": {"hash": "2f3be4d748e5b72a5df03aa657ebef2c", "index": {"url": "z_c78f3e75ce3283c5_guardrail_py.html", "file": "app/agents/guardrail.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_787edb25a2b1efa0___init___py": {"hash": "6bc9ce555b05bb3399e06340e16a9753", "index": {"url": "z_787edb25a2b1efa0___init___py.html", "file": "app/agents/inbuilt_tools/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_main_agent_py": {"hash": "3d9ca960a2417db20893c9a58007432c", "index": {"url": "z_c78f3e75ce3283c5_main_agent_py.html", "file": "app/agents/main_agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b8613eece30fad71___init___py": {"hash": "c2829b46af7e0a8cedac7d665fd9b5a4", "index": {"url": "z_b8613eece30fad71___init___py.html", "file": "app/agents/mcp_agents/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_prompt_py": {"hash": "52c37bd0183b6c03594cc48d1e59e763", "index": {"url": "z_c78f3e75ce3283c5_prompt_py.html", "file": "app/agents/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cee8abac36a30d74___init___py": {"hash": "a5b6d18805cb33bef432e6bdc7dfbd6e", "index": {"url": "z_cee8abac36a30d74___init___py.html", "file": "app/agents/sub_agents/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e1fd0da3356317eb_agent_py": {"hash": "55553ac346583c6b88faa5d45835eff3", "index": {"url": "z_e1fd0da3356317eb_agent_py.html", "file": "app/agents/sub_agents/incident_manager/agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e1fd0da3356317eb_prompt_py": {"hash": "44d99340f1071c3cf2a8e58554e535b9", "index": {"url": "z_e1fd0da3356317eb_prompt_py.html", "file": "app/agents/sub_agents/incident_manager/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e1fd0da3356317eb_tools_py": {"hash": "5794e38f5ffa17e4f076e38fe8525f3d", "index": {"url": "z_e1fd0da3356317eb_tools_py.html", "file": "app/agents/sub_agents/incident_manager/tools.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 51, "n_excluded": 0, "n_missing": 37, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e1fd0da3356317eb_utils_py": {"hash": "bb00d954c4fde11612ce8adcca9ff3a9", "index": {"url": "z_e1fd0da3356317eb_utils_py.html", "file": "app/agents/sub_agents/incident_manager/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_161f8d3341fa06f0_agent_py": {"hash": "4214f25e9496621f2440619e1db526b5", "index": {"url": "z_161f8d3341fa06f0_agent_py.html", "file": "app/agents/sub_agents/log_analytics/agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_161f8d3341fa06f0_prompt_py": {"hash": "08830ce323f905d77f2f28dd1ab8719d", "index": {"url": "z_161f8d3341fa06f0_prompt_py.html", "file": "app/agents/sub_agents/log_analytics/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_161f8d3341fa06f0_tools_py": {"hash": "18f7cffa2b22a7ca75646849bdc2d6c9", "index": {"url": "z_161f8d3341fa06f0_tools_py.html", "file": "app/agents/sub_agents/log_analytics/tools.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_161f8d3341fa06f0_utils_py": {"hash": "4f7922bd8014bbd5d57468a3c6294532", "index": {"url": "z_161f8d3341fa06f0_utils_py.html", "file": "app/agents/sub_agents/log_analytics/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0a769a211490e8ea_agent_py": {"hash": "024f69deff62e4183fde631e6a41f9ce", "index": {"url": "z_0a769a211490e8ea_agent_py.html", "file": "app/agents/sub_agents/reporter_agent/agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0a769a211490e8ea_prompt_py": {"hash": "4670d9def9656eb19bb233c1e2c74943", "index": {"url": "z_0a769a211490e8ea_prompt_py.html", "file": "app/agents/sub_agents/reporter_agent/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0a769a211490e8ea_tools_py": {"hash": "48a62d11da27193ffbe1d59e77f3e8fd", "index": {"url": "z_0a769a211490e8ea_tools_py.html", "file": "app/agents/sub_agents/reporter_agent/tools.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0a769a211490e8ea_utils_py": {"hash": "fde8ab468d5aa55070c580b8fd7eb397", "index": {"url": "z_0a769a211490e8ea_utils_py.html", "file": "app/agents/sub_agents/reporter_agent/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1f3f7b75b124023d_agent_py": {"hash": "f13a0beca5142d66753871a798fd2ede", "index": {"url": "z_1f3f7b75b124023d_agent_py.html", "file": "app/agents/sub_agents/root_cause_analyzer/agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1f3f7b75b124023d_prompt_py": {"hash": "624cd5c327646e5e3a43addc59e6f117", "index": {"url": "z_1f3f7b75b124023d_prompt_py.html", "file": "app/agents/sub_agents/root_cause_analyzer/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1f3f7b75b124023d_utils_py": {"hash": "032063800058185c16d09d74937775c1", "index": {"url": "z_1f3f7b75b124023d_utils_py.html", "file": "app/agents/sub_agents/root_cause_analyzer/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0ab4ee082f0eac6f_agent_py": {"hash": "0af677f80c8175cd586f9c719e2f4fc0", "index": {"url": "z_0ab4ee082f0eac6f_agent_py.html", "file": "app/agents/sub_agents/runbook_generator_agent/agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0ab4ee082f0eac6f_prompt_py": {"hash": "8537ebdb97dc6bca2424e39ce987fb6b", "index": {"url": "z_0ab4ee082f0eac6f_prompt_py.html", "file": "app/agents/sub_agents/runbook_generator_agent/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0ab4ee082f0eac6f_utils_py": {"hash": "ceeab33e3dd58262346e5282420e915d", "index": {"url": "z_0ab4ee082f0eac6f_utils_py.html", "file": "app/agents/sub_agents/runbook_generator_agent/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_893193f367e1c78b_agent_py": {"hash": "c9b98f807bea25004d1d3701920f4cb5", "index": {"url": "z_893193f367e1c78b_agent_py.html", "file": "app/agents/sub_agents/summary_agent/agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_893193f367e1c78b_prompt_py": {"hash": "81ed7903b54a0ad8acf69148b36e05d3", "index": {"url": "z_893193f367e1c78b_prompt_py.html", "file": "app/agents/sub_agents/summary_agent/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_893193f367e1c78b_utils_py": {"hash": "4146b793c5ec5d488f83644dadb0e011", "index": {"url": "z_893193f367e1c78b_utils_py.html", "file": "app/agents/sub_agents/summary_agent/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1d3f85c8a27f28e0_agent_py": {"hash": "0f484462998a0c12dcd4f434b46ddec7", "index": {"url": "z_1d3f85c8a27f28e0_agent_py.html", "file": "app/agents/sub_agents/time_analytics/agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1d3f85c8a27f28e0_prompt_py": {"hash": "a57922ac510c816c14fa5494d3a586b5", "index": {"url": "z_1d3f85c8a27f28e0_prompt_py.html", "file": "app/agents/sub_agents/time_analytics/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1d3f85c8a27f28e0_tools_py": {"hash": "c3965f7aef3d9a366f0134268234dd29", "index": {"url": "z_1d3f85c8a27f28e0_tools_py.html", "file": "app/agents/sub_agents/time_analytics/tools.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b2eaf0545d221b91_agent_py": {"hash": "032ae0499f1bd39416f28e5642520348", "index": {"url": "z_b2eaf0545d221b91_agent_py.html", "file": "app/agents/sub_agents/user_preference_manager/agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b2eaf0545d221b91_prompt_py": {"hash": "887c08a9ceb8fc17896d332a266f9696", "index": {"url": "z_b2eaf0545d221b91_prompt_py.html", "file": "app/agents/sub_agents/user_preference_manager/prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b2eaf0545d221b91_tools_py": {"hash": "47332334172a69e766e403606ae71ab4", "index": {"url": "z_b2eaf0545d221b91_tools_py.html", "file": "app/agents/sub_agents/user_preference_manager/tools.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_utils_py": {"hash": "13ea491a107a82d68791bdfdeb70af07", "index": {"url": "z_c78f3e75ce3283c5_utils_py.html", "file": "app/agents/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7589b33177cad060___init___py": {"hash": "c1a0b2b458ebd36f03fc445a945f0820", "index": {"url": "z_7589b33177cad060___init___py.html", "file": "app/connectors/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7589b33177cad060_base_connector_py": {"hash": "777bed1e838f659e93db6c0e8bc8f139", "index": {"url": "z_7589b33177cad060_base_connector_py.html", "file": "app/connectors/base_connector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1be82776c9085b60___init___py": {"hash": "9e50bedaf759bec5f7a537ce250f5db8", "index": {"url": "z_1be82776c9085b60___init___py.html", "file": "app/connectors/github/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1be82776c9085b60_data_models_py": {"hash": "5a444aa362ca743fcb06c6cb61292070", "index": {"url": "z_1be82776c9085b60_data_models_py.html", "file": "app/connectors/github/data_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1be82776c9085b60_github_connector_py": {"hash": "92adcd6dbdfce105ecc2f887e86e1157", "index": {"url": "z_1be82776c9085b60_github_connector_py.html", "file": "app/connectors/github/github_connector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 219, "n_excluded": 0, "n_missing": 194, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_33d94c5b4d36d1aa_core_py": {"hash": "959b022a1e791d20b019db87beb66c72", "index": {"url": "z_33d94c5b4d36d1aa_core_py.html", "file": "app/database/core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7998bb0617f094a5_documents_py": {"hash": "dddcfd3a652c589e9a1175a37f82ebf8", "index": {"url": "z_7998bb0617f094a5_documents_py.html", "file": "app/db_services/documents.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7998bb0617f094a5_events_py": {"hash": "eaaa20db9b5e747c6dd83be9e4dae0af", "index": {"url": "z_7998bb0617f094a5_events_py.html", "file": "app/db_services/events.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7998bb0617f094a5_incident_py": {"hash": "bf534d5fc71caac6fdc6916ae055f77e", "index": {"url": "z_7998bb0617f094a5_incident_py.html", "file": "app/db_services/incident.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7998bb0617f094a5_jobs_py": {"hash": "8c0e626a0e8f3abc28e514556421960f", "index": {"url": "z_7998bb0617f094a5_jobs_py.html", "file": "app/db_services/jobs.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7998bb0617f094a5_knowledge_base_py": {"hash": "61d294b1859c3d7c09cdf0a4da298db1", "index": {"url": "z_7998bb0617f094a5_knowledge_base_py.html", "file": "app/db_services/knowledge_base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7998bb0617f094a5_projects_py": {"hash": "02d7ce5ef8f38d1a50c3e1c0fee52cff", "index": {"url": "z_7998bb0617f094a5_projects_py.html", "file": "app/db_services/projects.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7998bb0617f094a5_runbooks_py": {"hash": "46dfe1afcd82e23bab27e29bc7e4d910", "index": {"url": "z_7998bb0617f094a5_runbooks_py.html", "file": "app/db_services/runbooks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7998bb0617f094a5_users_py": {"hash": "50fd42a2c56f0cfeef0348530c1ca031", "index": {"url": "z_7998bb0617f094a5_users_py.html", "file": "app/db_services/users.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_61aebbf4dc8e82ed___init___py.html", "file": "app/entities/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_documents_py": {"hash": "e117e24dd50408ee12088cec1a5f1791", "index": {"url": "z_61aebbf4dc8e82ed_documents_py.html", "file": "app/entities/documents.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_events_py": {"hash": "74ca3975ef843f37a56201412eb8580b", "index": {"url": "z_61aebbf4dc8e82ed_events_py.html", "file": "app/entities/events.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_incident_py": {"hash": "bb5dac1cdc7f051284d6fb8fb0eb45db", "index": {"url": "z_61aebbf4dc8e82ed_incident_py.html", "file": "app/entities/incident.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_incident_metrics_py": {"hash": "2df4ed35faf61f24f812b552216b2dc7", "index": {"url": "z_61aebbf4dc8e82ed_incident_metrics_py.html", "file": "app/entities/incident_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_incident_report_py": {"hash": "ac4596cb89059efb48ed62af52990740", "index": {"url": "z_61aebbf4dc8e82ed_incident_report_py.html", "file": "app/entities/incident_report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_job_py": {"hash": "4282aac28dedefa6f55f9523eae5d453", "index": {"url": "z_61aebbf4dc8e82ed_job_py.html", "file": "app/entities/job.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_knowledge_base_py": {"hash": "7828e1bb1a500d780232cb7ffef82a6d", "index": {"url": "z_61aebbf4dc8e82ed_knowledge_base_py.html", "file": "app/entities/knowledge_base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_projects_py": {"hash": "23a4063d0196143792b38953a137efb8", "index": {"url": "z_61aebbf4dc8e82ed_projects_py.html", "file": "app/entities/projects.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_runbooks_py": {"hash": "6d98e5a1fb0c40981983b9f5c116764d", "index": {"url": "z_61aebbf4dc8e82ed_runbooks_py.html", "file": "app/entities/runbooks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_61aebbf4dc8e82ed_user_py": {"hash": "236bf9735077e38111aa4b7d7d450136", "index": {"url": "z_61aebbf4dc8e82ed_user_py.html", "file": "app/entities/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "c1aa4c0de4b98a4b702e029e756f560c", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c6de83248c84ada5___init___py": {"hash": "dae4561aa3f9048522ea0475bcc00a7d", "index": {"url": "z_c6de83248c84ada5___init___py.html", "file": "app/routes/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_961d52a827ead929___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_961d52a827ead929___init___py.html", "file": "app/routes/agents/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_961d52a827ead929_controller_py": {"hash": "e810093d91645cbb02435ce4c0ebcdd3", "index": {"url": "z_961d52a827ead929_controller_py.html", "file": "app/routes/agents/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 85, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_961d52a827ead929_models_py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_961d52a827ead929_models_py.html", "file": "app/routes/agents/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_961d52a827ead929_service_py": {"hash": "a484d3e238a95090e4a17c262a22c605", "index": {"url": "z_961d52a827ead929_service_py.html", "file": "app/routes/agents/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_69681eb17b7d35c5___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_69681eb17b7d35c5___init___py.html", "file": "app/routes/auth/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_69681eb17b7d35c5_controller_py": {"hash": "8a7758b76079f9f6c3c24f9c53760818", "index": {"url": "z_69681eb17b7d35c5_controller_py.html", "file": "app/routes/auth/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_69681eb17b7d35c5_models_py": {"hash": "4170276d813862f52377b51b85f1a3b8", "index": {"url": "z_69681eb17b7d35c5_models_py.html", "file": "app/routes/auth/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_69681eb17b7d35c5_service_py": {"hash": "08e561b8c40c5602d7bad3461195c5f1", "index": {"url": "z_69681eb17b7d35c5_service_py.html", "file": "app/routes/auth/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de01cdf35b22c110_controller_py": {"hash": "c9db374bb21ccc0649cc4b0073ca1efa", "index": {"url": "z_de01cdf35b22c110_controller_py.html", "file": "app/routes/dashboard/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de01cdf35b22c110_models_py": {"hash": "812e36c55605c71f7622128113d8300f", "index": {"url": "z_de01cdf35b22c110_models_py.html", "file": "app/routes/dashboard/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de01cdf35b22c110_service_py": {"hash": "63949a290e122bedd844d708c342a181", "index": {"url": "z_de01cdf35b22c110_service_py.html", "file": "app/routes/dashboard/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e5d508c905d0e90c___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_e5d508c905d0e90c___init___py.html", "file": "app/routes/data_bank/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e5d508c905d0e90c_controller_py": {"hash": "979f4dbd9c5ccc2267a1e9c2c29f321f", "index": {"url": "z_e5d508c905d0e90c_controller_py.html", "file": "app/routes/data_bank/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e5d508c905d0e90c_models_py": {"hash": "1673094746d30ea8c50aa67ad3ea6385", "index": {"url": "z_e5d508c905d0e90c_models_py.html", "file": "app/routes/data_bank/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e5d508c905d0e90c_service_py": {"hash": "2fa404fe52eb1e4248fb94c50d019825", "index": {"url": "z_e5d508c905d0e90c_service_py.html", "file": "app/routes/data_bank/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2f2a9d10427d122f___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_2f2a9d10427d122f___init___py.html", "file": "app/routes/events/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2f2a9d10427d122f_controller_py": {"hash": "840d64fe0028b47bdf58b3bacab88d1c", "index": {"url": "z_2f2a9d10427d122f_controller_py.html", "file": "app/routes/events/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2f2a9d10427d122f_models_py": {"hash": "5760092ac548b88f8e58fd3d8a51d027", "index": {"url": "z_2f2a9d10427d122f_models_py.html", "file": "app/routes/events/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2f2a9d10427d122f_service_py": {"hash": "a05d9f22cb8175f8d70c769e14f44df9", "index": {"url": "z_2f2a9d10427d122f_service_py.html", "file": "app/routes/events/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_65913829e64f7d99___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_65913829e64f7d99___init___py.html", "file": "app/routes/incident_metrics/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_65913829e64f7d99_controller_py": {"hash": "89cc02b79b2dcc77d09c88fe73d547be", "index": {"url": "z_65913829e64f7d99_controller_py.html", "file": "app/routes/incident_metrics/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_65913829e64f7d99_models_py": {"hash": "da3daadda3455ba7ed6264463c65d7dd", "index": {"url": "z_65913829e64f7d99_models_py.html", "file": "app/routes/incident_metrics/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_65913829e64f7d99_service_py": {"hash": "c0268dc0636cbba2cbd8dd849a19face", "index": {"url": "z_65913829e64f7d99_service_py.html", "file": "app/routes/incident_metrics/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53411b2bde4672bc_controller_py": {"hash": "4441ec7f2bc00978c60bbbefda510816", "index": {"url": "z_53411b2bde4672bc_controller_py.html", "file": "app/routes/incident_report/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53411b2bde4672bc_models_py": {"hash": "49803089401f49efd574e8368b19c738", "index": {"url": "z_53411b2bde4672bc_models_py.html", "file": "app/routes/incident_report/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53411b2bde4672bc_service_py": {"hash": "00ec578b3ce75a1c5d55cc8d1d2e875d", "index": {"url": "z_53411b2bde4672bc_service_py.html", "file": "app/routes/incident_report/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_375a9ff46253c3da___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_375a9ff46253c3da___init___py.html", "file": "app/routes/incidents/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_375a9ff46253c3da_controller_py": {"hash": "c251af8f9e0688bdf1a680ee02d7e4a0", "index": {"url": "z_375a9ff46253c3da_controller_py.html", "file": "app/routes/incidents/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_375a9ff46253c3da_models_py": {"hash": "cedb2da2b3b1e8f2a8a547f8e4353a48", "index": {"url": "z_375a9ff46253c3da_models_py.html", "file": "app/routes/incidents/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_375a9ff46253c3da_service_py": {"hash": "5a64763f9c35a5b990aa3a10d39e3603", "index": {"url": "z_375a9ff46253c3da_service_py.html", "file": "app/routes/incidents/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 221, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_78b934913049066c_controller_py": {"hash": "21a29399c30a49300c48a5254b00366a", "index": {"url": "z_78b934913049066c_controller_py.html", "file": "app/routes/jobs/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_78b934913049066c_models_py": {"hash": "b6f9921ff3bdffca8391c1503c9b0e3c", "index": {"url": "z_78b934913049066c_models_py.html", "file": "app/routes/jobs/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_78b934913049066c_service_py": {"hash": "542038127324d9fc73fdef9f6b150c8e", "index": {"url": "z_78b934913049066c_service_py.html", "file": "app/routes/jobs/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eadef2affc5f816e___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_eadef2affc5f816e___init___py.html", "file": "app/routes/knowledge_base/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eadef2affc5f816e_controller_py": {"hash": "e33ecb6244f400ca483f2c145c122633", "index": {"url": "z_eadef2affc5f816e_controller_py.html", "file": "app/routes/knowledge_base/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 144, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eadef2affc5f816e_models_py": {"hash": "a4a1cccae37f650314e0f9dc3cb131c7", "index": {"url": "z_eadef2affc5f816e_models_py.html", "file": "app/routes/knowledge_base/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eadef2affc5f816e_service_py": {"hash": "04d5179d77ecdb09648f3c7ae1f80572", "index": {"url": "z_eadef2affc5f816e_service_py.html", "file": "app/routes/knowledge_base/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 239, "n_excluded": 0, "n_missing": 211, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49e29148a8e3bdca___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_49e29148a8e3bdca___init___py.html", "file": "app/routes/logs/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49e29148a8e3bdca_controller_py": {"hash": "2b108e9e662e6c687891bf2f25c1bf12", "index": {"url": "z_49e29148a8e3bdca_controller_py.html", "file": "app/routes/logs/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49e29148a8e3bdca_models_py": {"hash": "efa44d646fd0f0c627d7c3c123a9df2e", "index": {"url": "z_49e29148a8e3bdca_models_py.html", "file": "app/routes/logs/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49e29148a8e3bdca_service_py": {"hash": "60bd7922ffbe79f1cf2324bb24d88be6", "index": {"url": "z_49e29148a8e3bdca_service_py.html", "file": "app/routes/logs/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_922414243b5e276e___init___py": {"hash": "be641a2d1b9b1a566d975a13fead6d48", "index": {"url": "z_922414243b5e276e___init___py.html", "file": "app/routes/projects/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_922414243b5e276e_controller_py": {"hash": "37958da23ea9a4d26f660a8ed1cafa07", "index": {"url": "z_922414243b5e276e_controller_py.html", "file": "app/routes/projects/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_922414243b5e276e_models_py": {"hash": "37f35905fe306fb1363d27f6912fa97b", "index": {"url": "z_922414243b5e276e_models_py.html", "file": "app/routes/projects/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_922414243b5e276e_service_py": {"hash": "d71257eab2a957607fca44d3a31416ee", "index": {"url": "z_922414243b5e276e_service_py.html", "file": "app/routes/projects/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f89002852d4634c0___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_f89002852d4634c0___init___py.html", "file": "app/routes/runbooks/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f89002852d4634c0_controller_py": {"hash": "c8032b75bff71e7df7607f5efcc8d35c", "index": {"url": "z_f89002852d4634c0_controller_py.html", "file": "app/routes/runbooks/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f89002852d4634c0_models_py": {"hash": "f2ecf9ae4729a4878e7e07b71180a47b", "index": {"url": "z_f89002852d4634c0_models_py.html", "file": "app/routes/runbooks/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f89002852d4634c0_service_py": {"hash": "7d1c3546755f6075d84c379eec1934a1", "index": {"url": "z_f89002852d4634c0_service_py.html", "file": "app/routes/runbooks/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 126, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0150f696ad884303___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_0150f696ad884303___init___py.html", "file": "app/routes/users/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0150f696ad884303_controller_py": {"hash": "33524cea24a147682788636500f549aa", "index": {"url": "z_0150f696ad884303_controller_py.html", "file": "app/routes/users/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0150f696ad884303_models_py": {"hash": "de745b52ea947f991a4b09c0f7ce1c9a", "index": {"url": "z_0150f696ad884303_models_py.html", "file": "app/routes/users/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0150f696ad884303_service_py": {"hash": "301ce0ffd2770f6a1cb5deb2a002769e", "index": {"url": "z_0150f696ad884303_service_py.html", "file": "app/routes/users/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69___init___py": {"hash": "d0e9717969c8a6466b2097cf7769725b", "index": {"url": "z_c318f3fa19a49f69___init___py.html", "file": "app/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_content_extractor_py": {"hash": "bf2dcd8e15e006dfc49212d5771f1399", "index": {"url": "z_c318f3fa19a49f69_content_extractor_py.html", "file": "app/services/content_extractor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 98, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_file_manager_py": {"hash": "290a0e77de5f8930f2d5dd183d2c6299", "index": {"url": "z_c318f3fa19a49f69_file_manager_py.html", "file": "app/services/file_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f83b30fdb6a6182___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_6f83b30fdb6a6182___init___py.html", "file": "app/tasks/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f83b30fdb6a6182_github_py": {"hash": "90ec8137a3e4111a95ec30b2a48d8d3a", "index": {"url": "z_6f83b30fdb6a6182_github_py.html", "file": "app/tasks/github.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f83b30fdb6a6182_jira_py": {"hash": "e6a840b135ed6755ac15d64527586377", "index": {"url": "z_6f83b30fdb6a6182_jira_py.html", "file": "app/tasks/jira.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f83b30fdb6a6182_service_now_py": {"hash": "49699afdaea2ed83c940d53750fe6589", "index": {"url": "z_6f83b30fdb6a6182_service_now_py.html", "file": "app/tasks/service_now.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f83b30fdb6a6182_vector_db_py": {"hash": "ad90234748e48b7c75e8f16a8a9b262d", "index": {"url": "z_6f83b30fdb6a6182_vector_db_py.html", "file": "app/tasks/vector_db.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 0, "n_missing": 202, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16_celery_worker_py": {"hash": "a01838631ae4984750bea31b36eba264", "index": {"url": "z_748a0465d46c2a16_celery_worker_py.html", "file": "app/utils/celery_worker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16_exceptions_py": {"hash": "1f879ebace821e4f75167b118f16473f", "index": {"url": "z_748a0465d46c2a16_exceptions_py.html", "file": "app/utils/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16_logger_py": {"hash": "fe2df7c39ddee24f58383be5c6214108", "index": {"url": "z_748a0465d46c2a16_logger_py.html", "file": "app/utils/logger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16_rate_limiter_py": {"hash": "a749cb5a67ee08919af986aed514018b", "index": {"url": "z_748a0465d46c2a16_rate_limiter_py.html", "file": "app/utils/rate_limiter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e47291130daa1918___init___py": {"hash": "99073562184dc684c04f0e1c4121dea7", "index": {"url": "z_e47291130daa1918___init___py.html", "file": "app/vector_db/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e47291130daa1918_base_connector_py": {"hash": "60b78a3d52dcdfa17fecda1fa396a1ee", "index": {"url": "z_e47291130daa1918_base_connector_py.html", "file": "app/vector_db/base_connector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e47291130daa1918_embeddings_py": {"hash": "06a1b802a83a268f0e1b29c5a032c669", "index": {"url": "z_e47291130daa1918_embeddings_py.html", "file": "app/vector_db/embeddings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e47291130daa1918_models_py": {"hash": "b0c21c903a99fbb72daadb55c7eca6cb", "index": {"url": "z_e47291130daa1918_models_py.html", "file": "app/vector_db/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e47291130daa1918_qdrant_connector_py": {"hash": "d9e0b2dfdc286810b50f1eb3e6ef9d99", "index": {"url": "z_e47291130daa1918_qdrant_connector_py.html", "file": "app/vector_db/qdrant_connector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e47291130daa1918_search_service_py": {"hash": "3e95e39d5dec927ecfb728956d9d81cb", "index": {"url": "z_e47291130daa1918_search_service_py.html", "file": "app/vector_db/search_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">45%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 19:29 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html">app/agents/__init__.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html">app/agents/agent_runner.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="34 54">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_guardrail_py.html">app/agents/guardrail.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_guardrail_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="6 24">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_787edb25a2b1efa0___init___py.html">app/agents/inbuilt_tools/__init__.py</a></td>
                <td class="name left"><a href="z_787edb25a2b1efa0___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_main_agent_py.html">app/agents/main_agent.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_main_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8613eece30fad71___init___py.html">app/agents/mcp_agents/__init__.py</a></td>
                <td class="name left"><a href="z_b8613eece30fad71___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_prompt_py.html">app/agents/prompt.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cee8abac36a30d74___init___py.html">app/agents/sub_agents/__init__.py</a></td>
                <td class="name left"><a href="z_cee8abac36a30d74___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_agent_py.html">app/agents/sub_agents/incident_manager/agent.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_prompt_py.html">app/agents/sub_agents/incident_manager/prompt.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html">app/agents/sub_agents/incident_manager/tools.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="14 51">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_utils_py.html">app/agents/sub_agents/incident_manager/utils.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_agent_py.html">app/agents/sub_agents/log_analytics/agent.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_prompt_py.html">app/agents/sub_agents/log_analytics/prompt.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html">app/agents/sub_agents/log_analytics/tools.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="12 39">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html">app/agents/sub_agents/log_analytics/utils.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_agent_py.html">app/agents/sub_agents/reporter_agent/agent.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_prompt_py.html">app/agents/sub_agents/reporter_agent/prompt.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>109</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="17 109">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html">app/agents/sub_agents/reporter_agent/utils.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="3 44">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_agent_py.html">app/agents/sub_agents/root_cause_analyzer/agent.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_prompt_py.html">app/agents/sub_agents/root_cause_analyzer/prompt.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html">app/agents/sub_agents/root_cause_analyzer/utils.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_agent_py.html">app/agents/sub_agents/runbook_generator_agent/agent.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_prompt_py.html">app/agents/sub_agents/runbook_generator_agent/prompt.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html">app/agents/sub_agents/runbook_generator_agent/utils.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="7 13">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_agent_py.html">app/agents/sub_agents/summary_agent/agent.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_prompt_py.html">app/agents/sub_agents/summary_agent/prompt.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html">app/agents/sub_agents/summary_agent/utils.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="4 19">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1d3f85c8a27f28e0_agent_py.html">app/agents/sub_agents/time_analytics/agent.py</a></td>
                <td class="name left"><a href="z_1d3f85c8a27f28e0_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1d3f85c8a27f28e0_prompt_py.html">app/agents/sub_agents/time_analytics/prompt.py</a></td>
                <td class="name left"><a href="z_1d3f85c8a27f28e0_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1d3f85c8a27f28e0_tools_py.html">app/agents/sub_agents/time_analytics/tools.py</a></td>
                <td class="name left"><a href="z_1d3f85c8a27f28e0_tools_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b2eaf0545d221b91_agent_py.html">app/agents/sub_agents/user_preference_manager/agent.py</a></td>
                <td class="name left"><a href="z_b2eaf0545d221b91_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b2eaf0545d221b91_prompt_py.html">app/agents/sub_agents/user_preference_manager/prompt.py</a></td>
                <td class="name left"><a href="z_b2eaf0545d221b91_prompt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b2eaf0545d221b91_tools_py.html">app/agents/sub_agents/user_preference_manager/tools.py</a></td>
                <td class="name left"><a href="z_b2eaf0545d221b91_tools_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_utils_py.html">app/agents/utils.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="19 21">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060___init___py.html">app/connectors/__init__.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t19">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t19"><data value='IncidentDetailsAIResponse'>IncidentDetailsAIResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t35">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t35"><data value='SaveType'>SaveType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t42">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t42"><data value='ConnectorError'>ConnectorError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t48">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t48"><data value='DatabaseError'>DatabaseError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t54">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t54"><data value='RateLimitError'>RateLimitError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t70">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t70"><data value='BaseConnector'>BaseConnector</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="56 60">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60___init___py.html">app/connectors/github/__init__.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_data_models_py.html#t7">app/connectors/github/data_models.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_data_models_py.html#t7"><data value='GitHubIssueData'>GitHubIssueData</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_data_models_py.html">app/connectors/github/data_models.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_data_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t20">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t20"><data value='GitHubConnector'>GitHubConnector</data></a></td>
                <td>194</td>
                <td>194</td>
                <td>0</td>
                <td class="right" data-ratio="0 194">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33d94c5b4d36d1aa_core_py.html">app/database/core.py</a></td>
                <td class="name left"><a href="z_33d94c5b4d36d1aa_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="17 18">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>68</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="16 68">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html">app/db_services/events.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="9 18">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="27 42">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html">app/db_services/jobs.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="12 38">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="12 44">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html">app/db_services/runbooks.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_users_py.html">app/db_services/users.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_users_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed___init___py.html">app/entities/__init__.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html#t10">app/entities/documents.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html#t10"><data value='DocumentTypeEnum'>DocumentTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html#t21">app/entities/documents.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html#t21"><data value='SyncStatusEnum'>SyncStatusEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html#t30">app/entities/documents.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html#t30"><data value='Document'>Document</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html">app/entities/documents.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_events_py.html#t10">app/entities/events.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_events_py.html#t10"><data value='EventTypeEnum'>EventTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_events_py.html#t23">app/entities/events.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_events_py.html#t23"><data value='Event'>Event</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_events_py.html">app/entities/events.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_events_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t23">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t23"><data value='SeverityEnum'>SeverityEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t30">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t30"><data value='PriorityEnum'>PriorityEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t38">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t38"><data value='IncidentTypeEnum'>IncidentTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t46">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t46"><data value='StatusEnum'>StatusEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t53">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t53"><data value='Incident'>Incident</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t97">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t97"><data value='IncidentDetail'>IncidentDetail</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="62 62">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_metrics_py.html#t9">app/entities/incident_metrics.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_metrics_py.html#t9"><data value='IncidentMetric'>IncidentMetric</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_metrics_py.html">app/entities/incident_metrics.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_report_py.html#t8">app/entities/incident_report.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_report_py.html#t8"><data value='IncidentReport'>IncidentReport</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_report_py.html">app/entities/incident_report.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_report_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html#t12">app/entities/job.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html#t12"><data value='JobStatusEnum'>JobStatusEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html#t22">app/entities/job.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html#t22"><data value='JobTypeEnum'>JobTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html#t33">app/entities/job.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html#t33"><data value='Job'>Job</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html">app/entities/job.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html#t10">app/entities/knowledge_base.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html#t10"><data value='KnowledgeBaseTypeEnum'>KnowledgeBaseTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html#t21">app/entities/knowledge_base.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html#t21"><data value='Service'>Service</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html#t35">app/entities/knowledge_base.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html#t35"><data value='KnowledgeBase'>KnowledgeBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html">app/entities/knowledge_base.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_projects_py.html#t9">app/entities/projects.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_projects_py.html#t9"><data value='Project'>Project</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_projects_py.html">app/entities/projects.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_projects_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t20">app/entities/runbooks.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t20"><data value='StepStatusEnum'>StepStatusEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t26">app/entities/runbooks.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t26"><data value='RunbookTypeEnum'>RunbookTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t34">app/entities/runbooks.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t34"><data value='Runbook'>Runbook</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t71">app/entities/runbooks.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t71"><data value='RunbookStep'>RunbookStep</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html">app/entities/runbooks.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html#t10">app/entities/user.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html#t10"><data value='UserRoleEnum'>UserRoleEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html#t16">app/entities/user.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html#t16"><data value='User'>User</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html">app/entities/user.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="32 33">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c6de83248c84ada5___init___py.html">app/routes/__init__.py</a></td>
                <td class="name left"><a href="z_c6de83248c84ada5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929___init___py.html">app/routes/agents/__init__.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html">app/routes/agents/controller.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>85</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="24 85">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_models_py.html">app/routes/agents/models.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>107</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="41 107">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5___init___py.html">app/routes/auth/__init__.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html">app/routes/auth/controller.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="47 64">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t6">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t6"><data value='USERROLES'>USERROLES</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t10">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t10"><data value='CreateUserRequest'>CreateUserRequest</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t24">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t24"><data value='Token'>Token</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t31">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t31"><data value='TokenData'>TokenData</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t40">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t40"><data value='ForgotPasswordRequest'>ForgotPasswordRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t44">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t44"><data value='RefreshTokenRequest'>RefreshTokenRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t48">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t48"><data value='ResetForgetPassword'>ResetForgetPassword</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>102</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="78 102">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html">app/routes/dashboard/controller.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="22 82">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t7">app/routes/dashboard/models.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t7"><data value='KeyPerformanceMetrics'>KeyPerformanceMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t14">app/routes/dashboard/models.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t14"><data value='IncidentOverview'>IncidentOverview</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t21">app/routes/dashboard/models.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t21"><data value='TimeSeriesData'>TimeSeriesData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t26">app/routes/dashboard/models.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t26"><data value='SeverityDistribution'>SeverityDistribution</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t33">app/routes/dashboard/models.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t33"><data value='ResolutionPerformance'>ResolutionPerformance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t41">app/routes/dashboard/models.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html#t41"><data value='DashboardMetrics'>DashboardMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html">app/routes/dashboard/models.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html">app/routes/dashboard/service.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>91</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="16 91">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c___init___py.html">app/routes/data_bank/__init__.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html">app/routes/data_bank/controller.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="20 70">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t7">app/routes/data_bank/models.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t7"><data value='SimilarityResult'>SimilarityResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t19">app/routes/data_bank/models.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t19"><data value='SimilaritySearchResponse'>SimilaritySearchResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t29">app/routes/data_bank/models.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t29"><data value='CombinedSimilarityResponse'>CombinedSimilarityResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t43">app/routes/data_bank/models.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t43"><data value='TextSearchRequest'>TextSearchRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t65">app/routes/data_bank/models.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html#t65"><data value='DocumentData'>DocumentData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html">app/routes/data_bank/models.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html">app/routes/data_bank/service.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>87</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="20 87">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f___init___py.html">app/routes/events/__init__.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html">app/routes/events/controller.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="16 50">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html#t11">app/routes/events/models.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html#t11"><data value='EventCreate'>EventCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html#t18">app/routes/events/models.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html#t18"><data value='EventResponse'>EventResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html#t28">app/routes/events/models.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html#t28"><data value='Config'>EventResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html#t32">app/routes/events/models.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html#t32"><data value='PaginatedEventResponse'>PaginatedEventResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html">app/routes/events/models.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html">app/routes/events/service.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="15 65">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99___init___py.html">app/routes/incident_metrics/__init__.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html">app/routes/incident_metrics/controller.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="14 41">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t8">app/routes/incident_metrics/models.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t8"><data value='IncidentMetricCreate'>IncidentMetricCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t16">app/routes/incident_metrics/models.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t16"><data value='IncidentMetricResponse'>IncidentMetricResponse</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t49">app/routes/incident_metrics/models.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t49"><data value='Config'>IncidentMetricResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t53">app/routes/incident_metrics/models.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t53"><data value='IncidentMetricUpdate'>IncidentMetricUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html">app/routes/incident_metrics/models.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html">app/routes/incident_metrics/service.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="11 53">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_controller_py.html">app/routes/incident_report/controller.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="10 14">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_models_py.html#t7">app/routes/incident_report/models.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_models_py.html#t7"><data value='IncidentReportResponse'>IncidentReportResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_models_py.html#t14">app/routes/incident_report/models.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_models_py.html#t14"><data value='Config'>IncidentReportResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_models_py.html">app/routes/incident_report/models.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_service_py.html">app/routes/incident_report/service.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da___init___py.html">app/routes/incidents/__init__.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>157</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="98 157">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t11">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t11"><data value='IncidentCreate'>IncidentCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t27">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t27"><data value='IncidentRead'>IncidentRead</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t39">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t39"><data value='Config'>IncidentRead.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t44">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t44"><data value='IncidentDetailsRead'>IncidentDetailsRead</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t51">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t51"><data value='Config'>IncidentDetailsRead.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t56">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t56"><data value='IncidentDetailsUpdate'>IncidentDetailsUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t63">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t63"><data value='IncidentUpdate'>IncidentUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t75">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t75"><data value='IncidentFilter'>IncidentFilter</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t83">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t83"><data value='PaginatedIncidentResponse'>PaginatedIncidentResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t91">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t91"><data value='IncidentAIAnalysisRead'>IncidentAIAnalysisRead</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t98">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t98"><data value='Config'>IncidentAIAnalysisRead.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t102">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t102"><data value='SimilarIncidentRead'>SimilarIncidentRead</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t106">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t106"><data value='Config'>SimilarIncidentRead.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t110">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html#t110"><data value='SimilarIncidentsResponse'>SimilarIncidentsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>84</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="84 84">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>221</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="143 221">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_controller_py.html">app/routes/jobs/controller.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="22 75">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t9">app/routes/jobs/models.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t9"><data value='JobBase'>JobBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t16">app/routes/jobs/models.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t16"><data value='JobCreate'>JobCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t20">app/routes/jobs/models.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t20"><data value='JobResponse'>JobResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t25">app/routes/jobs/models.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t25"><data value='Config'>JobResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t29">app/routes/jobs/models.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t29"><data value='PaginatedResponse'>PaginatedResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t36">app/routes/jobs/models.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_models_py.html#t36"><data value='JobStatusResponse'>JobStatusResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_models_py.html">app/routes/jobs/models.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_service_py.html">app/routes/jobs/service.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>73</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="15 73">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e___init___py.html">app/routes/knowledge_base/__init__.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>177</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="33 177">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t10">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t10"><data value='KnowledgeBaseCreate'>KnowledgeBaseCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t17">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t17"><data value='KnowledgeBaseUpdate'>KnowledgeBaseUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t22">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t22"><data value='DocumentInfo'>DocumentInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t29">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t29"><data value='Config'>DocumentInfo.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t33">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t33"><data value='KnowledgeBaseResponse'>KnowledgeBaseResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t45">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t45"><data value='Config'>KnowledgeBaseResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t50">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t50"><data value='DocumentListResponse'>DocumentListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t60">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t60"><data value='Config'>DocumentListResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t64">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t64"><data value='DocumentResponse'>DocumentResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t79">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t79"><data value='Config'>DocumentResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t83">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t83"><data value='PaginatedDocumentResponse'>PaginatedDocumentResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t91">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t91"><data value='DocumentSearchRequest'>DocumentSearchRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t100">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t100"><data value='DocumentSearchResult'>DocumentSearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t108">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t108"><data value='Config'>DocumentSearchResult.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t112">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t112"><data value='DocumentSearchResponse'>DocumentSearchResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t118">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t118"><data value='DocumentUploadResponse'>DocumentUploadResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t127">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t127"><data value='Config'>DocumentUploadResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t131">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html#t131"><data value='DocumentFileUpdate'>DocumentFileUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>97</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="97 97">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>239</td>
                <td>211</td>
                <td>0</td>
                <td class="right" data-ratio="28 239">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca___init___py.html">app/routes/logs/__init__.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_controller_py.html">app/routes/logs/controller.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t8">app/routes/logs/models.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t8"><data value='PaginatedLogsResult'>PaginatedLogsResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t14">app/routes/logs/models.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t14"><data value='LogQueryParams'>LogQueryParams</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t45">app/routes/logs/models.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t45"><data value='LogEntry'>LogEntry</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t51">app/routes/logs/models.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t51"><data value='LogsResponse'>LogsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html">app/routes/logs/models.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t22">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t22"><data value='BaseConnector'>BaseConnector</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t35">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t35"><data value='LokiConnector'>LokiConnector</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="19 50">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e___init___py.html">app/routes/projects/__init__.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html">app/routes/projects/controller.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>89</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="20 89">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t8">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t8"><data value='ProjectCreate'>ProjectCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t13">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t13"><data value='ProjectUpdate'>ProjectUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t18">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t18"><data value='KnowledgeBaseInfo'>KnowledgeBaseInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t25">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t25"><data value='Config'>KnowledgeBaseInfo.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t29">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t29"><data value='ProjectResponse'>ProjectResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t38">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t38"><data value='Config'>ProjectResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t42">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t42"><data value='ProjectListResponse'>ProjectListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t51">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t51"><data value='Config'>ProjectListResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t55">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html#t55"><data value='PaginatedProjectResponse'>PaginatedProjectResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>91</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="17 91">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0___init___py.html">app/routes/runbooks/__init__.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>134</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="27 134">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t11">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t11"><data value='RunbookStepBase'>RunbookStepBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t27">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t27"><data value='RunbookStepUpdate'>RunbookStepUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t32">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t32"><data value='RunbookStepResponse'>RunbookStepResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t37">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t37"><data value='Config'>RunbookStepResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t41">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t41"><data value='RunbookBase'>RunbookBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t49">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t49"><data value='RunbookCreate'>RunbookCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t53">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t53"><data value='RunbookUpdate'>RunbookUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t57">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t57"><data value='RunbookResponse'>RunbookResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t62">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html#t62"><data value='Config'>RunbookResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>126</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="21 126">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303___init___py.html">app/routes/users/__init__.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html">app/routes/users/controller.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="32 38">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t6">app/routes/users/models.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t6"><data value='UserResponse'>UserResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t13">app/routes/users/models.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t13"><data value='Config'>UserResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t17">app/routes/users/models.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t17"><data value='PasswordChange'>PasswordChange</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_models_py.html">app/routes/users/models.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_service_py.html">app/routes/users/service.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="28 35">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t18">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t18"><data value='ContentExtractor'>ContentExtractor</data></a></td>
                <td>98</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="0 98">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t48">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t48"><data value='FileManager'>FileManager</data></a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182___init___py.html">app/tasks/__init__.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html">app/tasks/github.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>109</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="14 109">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_jira_py.html">app/tasks/jira.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_jira_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_service_now_py.html">app/tasks/service_now.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_service_now_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>226</td>
                <td>202</td>
                <td>0</td>
                <td class="right" data-ratio="24 226">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_celery_worker_py.html">app/utils/celery_worker.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_celery_worker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t4">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t4"><data value='UserError'>UserError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t10">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t10"><data value='UserNotFoundError'>UserNotFoundError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t18">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t18"><data value='PasswordMismatchError'>PasswordMismatchError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t23">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t23"><data value='InvalidPasswordError'>InvalidPasswordError</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t28">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t28"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html">app/utils/logger.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="34 36">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_rate_limiter_py.html">app/utils/rate_limiter.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_rate_limiter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918___init___py.html">app/vector_db/__init__.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t8">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t8"><data value='VectorDBConnector'>VectorDBConnector</data></a></td>
                <td>8</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="3 8">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_embeddings_py.html">app/vector_db/embeddings.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_embeddings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="7 29">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_models_py.html#t4">app/vector_db/models.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_models_py.html#t4"><data value='CollectionType'>CollectionType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_models_py.html">app/vector_db/models.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t30">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t30"><data value='QdrantConnector'>QdrantConnector</data></a></td>
                <td>65</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="23 65">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t24">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t24"><data value='VectorSearchService'>VectorSearchService</data></a></td>
                <td>74</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="2 74">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>5337</td>
                <td>2954</td>
                <td>0</td>
                <td class="right" data-ratio="2383 5337">45%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 19:29 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

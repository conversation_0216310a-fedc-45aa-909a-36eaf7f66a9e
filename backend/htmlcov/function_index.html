<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">45%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 19:31 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html">app/agents/__init__.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html#t24">app/agents/agent_runner.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html#t24"><data value='get_or_create_session'>get_or_create_session</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html#t37">app/agents/agent_runner.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html#t37"><data value='get_runner'>get_runner</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html#t46">app/agents/agent_runner.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html#t46"><data value='call_agent_async'>call_agent_async</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html#t74">app/agents/agent_runner.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html#t74"><data value='main'>main</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html">app/agents/agent_runner.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_agent_runner_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="19 23">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_guardrail_py.html#t9">app/agents/guardrail.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_guardrail_py.html#t9"><data value='block_keyword_guardrail'>block_keyword_guardrail</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_guardrail_py.html">app/agents/guardrail.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_guardrail_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_787edb25a2b1efa0___init___py.html">app/agents/inbuilt_tools/__init__.py</a></td>
                <td class="name left"><a href="z_787edb25a2b1efa0___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_main_agent_py.html">app/agents/main_agent.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_main_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8613eece30fad71___init___py.html">app/agents/mcp_agents/__init__.py</a></td>
                <td class="name left"><a href="z_b8613eece30fad71___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_prompt_py.html">app/agents/prompt.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cee8abac36a30d74___init___py.html">app/agents/sub_agents/__init__.py</a></td>
                <td class="name left"><a href="z_cee8abac36a30d74___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_agent_py.html">app/agents/sub_agents/incident_manager/agent.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_prompt_py.html">app/agents/sub_agents/incident_manager/prompt.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html#t16">app/agents/sub_agents/incident_manager/tools.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html#t16"><data value='get_db_session'>get_db_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html#t24">app/agents/sub_agents/incident_manager/tools.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html#t24"><data value='get_incident_details'>get_incident_details</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html#t92">app/agents/sub_agents/incident_manager/tools.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html#t92"><data value='get_similar_incidents'>get_similar_incidents</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html">app/agents/sub_agents/incident_manager/tools.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_tools_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_utils_py.html#t1">app/agents/sub_agents/incident_manager/utils.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_utils_py.html#t1"><data value='build_query'>build_query</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_utils_py.html#t7">app/agents/sub_agents/incident_manager/utils.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_utils_py.html#t7"><data value='pre_process'>pre_process</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e1fd0da3356317eb_utils_py.html">app/agents/sub_agents/incident_manager/utils.py</a></td>
                <td class="name left"><a href="z_e1fd0da3356317eb_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_agent_py.html">app/agents/sub_agents/log_analytics/agent.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_prompt_py.html">app/agents/sub_agents/log_analytics/prompt.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t11">app/agents/sub_agents/log_analytics/tools.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t11"><data value='get_current_datetime'>get_current_datetime</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t22">app/agents/sub_agents/log_analytics/tools.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t22"><data value='fetch_logs'>fetch_logs</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t52">app/agents/sub_agents/log_analytics/tools.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t52"><data value='generate_summary'>generate_summary</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t70">app/agents/sub_agents/log_analytics/tools.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t70"><data value='generate_insights'>generate_insights</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t132">app/agents/sub_agents/log_analytics/tools.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html#t132"><data value='analyze_security_events'>analyze_security_events</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html">app/agents/sub_agents/log_analytics/tools.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_tools_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html#t1">app/agents/sub_agents/log_analytics/utils.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html#t1"><data value='build_query'>build_query</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html#t5">app/agents/sub_agents/log_analytics/utils.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html#t5"><data value='pre_process'>pre_process</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html#t9">app/agents/sub_agents/log_analytics/utils.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html#t9"><data value='post_process'>post_process</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html">app/agents/sub_agents/log_analytics/utils.py</a></td>
                <td class="name left"><a href="z_161f8d3341fa06f0_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_agent_py.html">app/agents/sub_agents/reporter_agent/agent.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_prompt_py.html">app/agents/sub_agents/reporter_agent/prompt.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t14">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t14"><data value='get_db_session'>get_db_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t22">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t22"><data value='get_incident_details'>get_incident_details</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t84">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t84"><data value='get_incident_timeline'>get_incident_timeline</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t118">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t118"><data value='get_incident_runbooks'>get_incident_runbooks</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t163">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t163"><data value='get_incident_metrics'>get_incident_metrics</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t181">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t181"><data value='format_duration'>get_incident_metrics.format_duration</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t232">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t232"><data value='get_incident_ai_analysis'>get_incident_ai_analysis</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t256">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html#t256"><data value='validate_incident_exists'>validate_incident_exists</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html">app/agents/sub_agents/reporter_agent/tools.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_tools_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html#t1">app/agents/sub_agents/reporter_agent/utils.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html#t1"><data value='build_query'>build_query</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html#t15">app/agents/sub_agents/reporter_agent/utils.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html#t15"><data value='pre_process'>pre_process</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html#t20">app/agents/sub_agents/reporter_agent/utils.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html#t20"><data value='post_process'>post_process</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html">app/agents/sub_agents/reporter_agent/utils.py</a></td>
                <td class="name left"><a href="z_0a769a211490e8ea_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_agent_py.html">app/agents/sub_agents/root_cause_analyzer/agent.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_prompt_py.html">app/agents/sub_agents/root_cause_analyzer/prompt.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html#t6">app/agents/sub_agents/root_cause_analyzer/utils.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html#t6"><data value='build_query'>build_query</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html#t24">app/agents/sub_agents/root_cause_analyzer/utils.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html#t24"><data value='pre_process'>pre_process</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html#t28">app/agents/sub_agents/root_cause_analyzer/utils.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html#t28"><data value='post_process'>post_process</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html">app/agents/sub_agents/root_cause_analyzer/utils.py</a></td>
                <td class="name left"><a href="z_1f3f7b75b124023d_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_agent_py.html">app/agents/sub_agents/runbook_generator_agent/agent.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_prompt_py.html">app/agents/sub_agents/runbook_generator_agent/prompt.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html#t8">app/agents/sub_agents/runbook_generator_agent/utils.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html#t8"><data value='build_query'>build_query</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html#t39">app/agents/sub_agents/runbook_generator_agent/utils.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html#t39"><data value='pre_process'>pre_process</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html#t43">app/agents/sub_agents/runbook_generator_agent/utils.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html#t43"><data value='post_process'>post_process</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html">app/agents/sub_agents/runbook_generator_agent/utils.py</a></td>
                <td class="name left"><a href="z_0ab4ee082f0eac6f_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_agent_py.html">app/agents/sub_agents/summary_agent/agent.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_prompt_py.html">app/agents/sub_agents/summary_agent/prompt.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html#t6">app/agents/sub_agents/summary_agent/utils.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html#t6"><data value='build_query'>build_query</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html#t41">app/agents/sub_agents/summary_agent/utils.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html#t41"><data value='pre_process'>pre_process</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html#t46">app/agents/sub_agents/summary_agent/utils.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html#t46"><data value='post_process'>post_process</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html">app/agents/sub_agents/summary_agent/utils.py</a></td>
                <td class="name left"><a href="z_893193f367e1c78b_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1d3f85c8a27f28e0_agent_py.html">app/agents/sub_agents/time_analytics/agent.py</a></td>
                <td class="name left"><a href="z_1d3f85c8a27f28e0_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1d3f85c8a27f28e0_prompt_py.html">app/agents/sub_agents/time_analytics/prompt.py</a></td>
                <td class="name left"><a href="z_1d3f85c8a27f28e0_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1d3f85c8a27f28e0_tools_py.html#t4">app/agents/sub_agents/time_analytics/tools.py</a></td>
                <td class="name left"><a href="z_1d3f85c8a27f28e0_tools_py.html#t4"><data value='get_current_time'>get_current_time</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1d3f85c8a27f28e0_tools_py.html">app/agents/sub_agents/time_analytics/tools.py</a></td>
                <td class="name left"><a href="z_1d3f85c8a27f28e0_tools_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b2eaf0545d221b91_agent_py.html">app/agents/sub_agents/user_preference_manager/agent.py</a></td>
                <td class="name left"><a href="z_b2eaf0545d221b91_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b2eaf0545d221b91_prompt_py.html">app/agents/sub_agents/user_preference_manager/prompt.py</a></td>
                <td class="name left"><a href="z_b2eaf0545d221b91_prompt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b2eaf0545d221b91_tools_py.html#t4">app/agents/sub_agents/user_preference_manager/tools.py</a></td>
                <td class="name left"><a href="z_b2eaf0545d221b91_tools_py.html#t4"><data value='update_user_preference'>update_user_preference</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b2eaf0545d221b91_tools_py.html">app/agents/sub_agents/user_preference_manager/tools.py</a></td>
                <td class="name left"><a href="z_b2eaf0545d221b91_tools_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_utils_py.html#t13">app/agents/utils.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_utils_py.html#t13"><data value='handle_agent_request'>handle_agent_request</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_utils_py.html">app/agents/utils.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060___init___py.html">app/connectors/__init__.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t61">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t61"><data value='db_session'>db_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t77">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t77"><data value='init__'>BaseConnector.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t83">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t83"><data value='register_callback'>BaseConnector.register_callback</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t87">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t87"><data value='get_incident_number'>BaseConnector.get_incident_number</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t91">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t91"><data value='generate_incident_details_with_ai'>BaseConnector._generate_incident_details_with_ai</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t123">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t123"><data value='read_project_info'>BaseConnector.read_project_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t128">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t128"><data value='read_open_issues'>BaseConnector.read_open_issues</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t135">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t135"><data value='read_closed_issues'>BaseConnector.read_closed_issues</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t142">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t142"><data value='post_comment'>BaseConnector.post_comment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t146">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t146"><data value='add_to_db'>BaseConnector.add_to_db</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t156">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t156"><data value='update_db'>BaseConnector.update_db</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t170">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t170"><data value='fetch_existing_incidents'>BaseConnector.fetch_existing_incidents</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t182">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t182"><data value='save_incidents'>BaseConnector.save_incidents</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t238">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t238"><data value='incident_field_matching'>BaseConnector.incident_field_matching</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t247">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t247"><data value='sync_issues'>BaseConnector.sync_issues</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t254">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html#t254"><data value='import_issues'>BaseConnector.import_issues</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html">app/connectors/base_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_base_connector_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>56</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="56 56">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60___init___py.html">app/connectors/github/__init__.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_data_models_py.html#t19">app/connectors/github/data_models.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_data_models_py.html#t19"><data value='from_github_issue'>GitHubIssueData.from_github_issue</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_data_models_py.html">app/connectors/github/data_models.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_data_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t25">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t25"><data value='init__'>GitHubConnector.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t42">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t42"><data value='initialize_github_client'>GitHubConnector._initialize_github_client</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t95">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t95"><data value='initialize_repository'>GitHubConnector._initialize_repository</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t103">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t103"><data value='check_rate_limit'>GitHubConnector._check_rate_limit</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t119">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t119"><data value='read_project_info'>GitHubConnector.read_project_info</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t132">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t132"><data value='fetch_readme'>GitHubConnector._fetch_readme</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t140">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t140"><data value='read_open_issues'>GitHubConnector.read_open_issues</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t150">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t150"><data value='read_closed_issues'>GitHubConnector.read_closed_issues</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t160">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t160"><data value='post_comment'>GitHubConnector.post_comment</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t178">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t178"><data value='fetch_issues_by_state'>GitHubConnector._fetch_issues_by_state</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t240">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t240"><data value='convert_issue_to_dict'>GitHubConnector._convert_issue_to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t252">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t252"><data value='incident_field_matching'>GitHubConnector.incident_field_matching</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t338">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t338"><data value='sync_issues'>GitHubConnector.sync_issues</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t367">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t367"><data value='process_sync_updates'>GitHubConnector._process_sync_updates</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t410">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html#t410"><data value='import_issues'>GitHubConnector.import_issues</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html">app/connectors/github/github_connector.py</a></td>
                <td class="name left"><a href="z_1be82776c9085b60_github_connector_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33d94c5b4d36d1aa_core_py.html#t36">app/database/core.py</a></td>
                <td class="name left"><a href="z_33d94c5b4d36d1aa_core_py.html#t36"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33d94c5b4d36d1aa_core_py.html">app/database/core.py</a></td>
                <td class="name left"><a href="z_33d94c5b4d36d1aa_core_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="13 14">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t10">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t10"><data value='create_document'>create_document</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t18">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t18"><data value='get_document_by_id'>get_document_by_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t32">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t32"><data value='get_documents_by_knowledge_base'>get_documents_by_knowledge_base</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t42">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t42"><data value='get_documents_by_type'>get_documents_by_type</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t60">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t60"><data value='update_document'>update_document</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t80">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t80"><data value='delete_document'>delete_document</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t91">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t91"><data value='search_documents_by_name'>search_documents_by_name</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t109">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t109"><data value='get_documents_pending_sync'>get_documents_pending_sync</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t119">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t119"><data value='update_document_sync_status'>update_document_sync_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t141">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html#t141"><data value='get_all_documents'>get_all_documents</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html">app/db_services/documents.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_documents_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html#t10">app/db_services/events.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html#t10"><data value='get_incident_events'>get_incident_events</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html#t34">app/db_services/events.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html#t34"><data value='get_event_by_id'>get_event_by_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html#t43">app/db_services/events.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html#t43"><data value='get_incident_events_response'>get_incident_events_response</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html">app/db_services/events.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_events_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t8">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t8"><data value='get_recent_incidents'>get_recent_incidents</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t25">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t25"><data value='get_incident_by_id'>get_incident_by_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t30">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t30"><data value='get_incident_by_number'>get_incident_by_number</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t37">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t37"><data value='get_incident_details'>get_incident_details</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t46">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t46"><data value='get_incident_ai_analysis'>get_incident_ai_analysis</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t67">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t67"><data value='save_incident_ai_analysis'>save_incident_ai_analysis</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t88">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html#t88"><data value='save_incident_summary'>save_incident_summary</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html">app/db_services/incident.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_incident_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html#t5">app/db_services/jobs.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html#t5"><data value='list_running_jobs'>list_running_jobs</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html#t13">app/db_services/jobs.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html#t13"><data value='list_all_jobs'>list_all_jobs</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html#t17">app/db_services/jobs.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html#t17"><data value='get_job_by_job_id'>get_job_by_job_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html">app/db_services/jobs.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_jobs_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t9">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t9"><data value='create_knowledge_base'>create_knowledge_base</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t19">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t19"><data value='get_kb_entry_by_id'>get_kb_entry_by_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t33">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t33"><data value='get_kb_entries_by_project_id'>get_kb_entries_by_project_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t45">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t45"><data value='get_kb_entry_by_project_and_type'>get_kb_entry_by_project_and_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t59">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t59"><data value='get_kb_entries'>get_kb_entries</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t71">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t71"><data value='update_knowledge_base'>update_knowledge_base</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t88">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html#t88"><data value='delete_knowledge_base'>delete_knowledge_base</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html">app/db_services/knowledge_base.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_knowledge_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t9">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t9"><data value='create_project'>create_project</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t17">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t17"><data value='get_project_by_id'>get_project_by_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t27">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t27"><data value='get_projects_by_user'>get_projects_by_user</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t37">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t37"><data value='get_all_projects'>get_all_projects</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t47">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t47"><data value='update_project'>update_project</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t64">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t64"><data value='delete_project'>delete_project</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t75">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html#t75"><data value='search_projects_by_name'>search_projects_by_name</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html">app/db_services/projects.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_projects_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html#t7">app/db_services/runbooks.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html#t7"><data value='get_steps_by_runbook'>get_steps_by_runbook</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html#t16">app/db_services/runbooks.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html#t16"><data value='get_step_by_id'>get_step_by_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html#t20">app/db_services/runbooks.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html#t20"><data value='list_runbooks'>list_runbooks</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html#t24">app/db_services/runbooks.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html#t24"><data value='get_runbook_by_id'>get_runbook_by_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html">app/db_services/runbooks.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_runbooks_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_users_py.html#t7">app/db_services/users.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_users_py.html#t7"><data value='get_user_by_id'>get_user_by_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7998bb0617f094a5_users_py.html">app/db_services/users.py</a></td>
                <td class="name left"><a href="z_7998bb0617f094a5_users_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed___init___py.html">app/entities/__init__.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html#t77">app/entities/documents.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html#t77"><data value='repr__'>Document.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html">app/entities/documents.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_documents_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_events_py.html">app/entities/events.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_events_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t93">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t93"><data value='repr__'>Incident.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t121">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html#t121"><data value='repr__'>IncidentDetail.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html">app/entities/incident.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>62</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="62 62">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_metrics_py.html#t63">app/entities/incident_metrics.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_metrics_py.html#t63"><data value='repr__'>IncidentMetric.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_metrics_py.html">app/entities/incident_metrics.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_metrics_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_report_py.html">app/entities/incident_report.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_incident_report_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html#t56">app/entities/job.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html#t56"><data value='repr__'>Job.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html">app/entities/job.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_job_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html">app/entities/knowledge_base.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_knowledge_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_projects_py.html">app/entities/projects.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_projects_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t67">app/entities/runbooks.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t67"><data value='repr__'>Runbook.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t109">app/entities/runbooks.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html#t109"><data value='repr__'>RunbookStep.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html">app/entities/runbooks.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_runbooks_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html#t40">app/entities/user.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html#t40"><data value='repr__'>User.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html">app/entities/user.py</a></td>
                <td class="name left"><a href="z_61aebbf4dc8e82ed_user_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t22">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t22"><data value='log_requests'>log_requests</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t59">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t59"><data value='health'>health</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t67">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t67"><data value='startup_event'>startup_event</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c6de83248c84ada5___init___py.html#t19">app/routes/__init__.py</a></td>
                <td class="name left"><a href="z_c6de83248c84ada5___init___py.html#t19"><data value='register_routes'>register_routes</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c6de83248c84ada5___init___py.html">app/routes/__init__.py</a></td>
                <td class="name left"><a href="z_c6de83248c84ada5___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929___init___py.html">app/routes/agents/__init__.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t19">app/routes/agents/controller.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t19"><data value='query_agent'>query_agent</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t38">app/routes/agents/controller.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t38"><data value='analyze_incident_root_cause'>analyze_incident_root_cause</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t72">app/routes/agents/controller.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t72"><data value='generate_runbook_steps'>generate_runbook_steps</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t156">app/routes/agents/controller.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t156"><data value='generate_incident_report'>generate_incident_report</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t182">app/routes/agents/controller.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t182"><data value='log_analytics_agent'>log_analytics_agent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t191">app/routes/agents/controller.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html#t191"><data value='incident_manager_agent'>incident_manager_agent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html">app/routes/agents/controller.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_models_py.html">app/routes/agents/models.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t29">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t29"><data value='handle_general_query'>handle_general_query</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t46">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t46"><data value='handle_root_cause_agent_request'>handle_root_cause_agent_request</data></a></td>
                <td>17</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="11 17">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t78">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t78"><data value='handle_runbook_agent_request'>handle_runbook_agent_request</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t119">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t119"><data value='handle_reporter_agent_request'>handle_reporter_agent_request</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t141">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t141"><data value='handle_log_analytics_agent_request'>handle_log_analytics_agent_request</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t159">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t159"><data value='handle_summary_agent_request'>handle_summary_agent_request</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t182">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html#t182"><data value='handle_incident_manager_agent_request'>handle_incident_manager_agent_request</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_961d52a827ead929_service_py.html">app/routes/agents/service.py</a></td>
                <td class="name left"><a href="z_961d52a827ead929_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5___init___py.html">app/routes/auth/__init__.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t17">app/routes/auth/controller.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t17"><data value='create_user'>create_user</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t31">app/routes/auth/controller.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t31"><data value='login_for_access_token'>login_for_access_token</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t45">app/routes/auth/controller.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t45"><data value='verify_user_token'>verify_user_token</data></a></td>
                <td>5</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="2 5">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t54">app/routes/auth/controller.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t54"><data value='forgot_password'>forgot_password</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t70">app/routes/auth/controller.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t70"><data value='reset_password'>reset_password</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t80">app/routes/auth/controller.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html#t80"><data value='refresh_token'>refresh_token</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html">app/routes/auth/controller.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t17">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t17"><data value='password_strength'>CreateUserRequest.password_strength</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t34">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t34"><data value='get_uuid'>TokenData.get_uuid</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t54">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t54"><data value='password_strength'>ResetForgetPassword.password_strength</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t61">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html#t61"><data value='passwords_match'>ResetForgetPassword.passwords_match</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html">app/routes/auth/models.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t37">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t37"><data value='verify_password'>verify_password</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t41">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t41"><data value='get_password_hash'>get_password_hash</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t45">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t45"><data value='authenticate_user'>authenticate_user</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t53">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t53"><data value='create_user'>create_user</data></a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t80">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t80"><data value='create_access_token'>create_access_token</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t90">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t90"><data value='create_refresh_token'>create_refresh_token</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t100">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t100"><data value='login_for_access_token'>login_for_access_token</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t122">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t122"><data value='verify_token'>verify_token</data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t141">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t141"><data value='refresh_access_token'>refresh_access_token</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t172">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t172"><data value='get_current_user'>get_current_user</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t179">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t179"><data value='get_user_details'>get_user_details</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t189">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t189"><data value='get_all_users'>get_all_users</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t201">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t201"><data value='forgot_password'>forgot_password</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t215">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html#t215"><data value='reset_password'>reset_password</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html">app/routes/auth/service.py</a></td>
                <td class="name left"><a href="z_69681eb17b7d35c5_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t23">app/routes/dashboard/controller.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t23"><data value='get_dashboard_metrics'>get_dashboard_metrics</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t42">app/routes/dashboard/controller.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t42"><data value='get_key_performance_metrics'>get_key_performance_metrics</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t63">app/routes/dashboard/controller.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t63"><data value='get_incident_overview'>get_incident_overview</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t82">app/routes/dashboard/controller.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t82"><data value='get_incidents_over_time'>get_incidents_over_time</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t101">app/routes/dashboard/controller.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t101"><data value='get_incidents_by_severity'>get_incidents_by_severity</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t122">app/routes/dashboard/controller.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html#t122"><data value='get_resolution_performance'>get_resolution_performance</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html">app/routes/dashboard/controller.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html">app/routes/dashboard/models.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t22">app/routes/dashboard/service.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t22"><data value='get_dashboard_metrics'>get_dashboard_metrics</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t56">app/routes/dashboard/service.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t56"><data value='get_key_performance_metrics'>get_key_performance_metrics</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t103">app/routes/dashboard/service.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t103"><data value='get_incident_overview'>get_incident_overview</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t136">app/routes/dashboard/service.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t136"><data value='get_incidents_over_time'>get_incidents_over_time</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t164">app/routes/dashboard/service.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t164"><data value='get_incidents_by_severity'>get_incidents_by_severity</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t190">app/routes/dashboard/service.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html#t190"><data value='get_resolution_performance'>get_resolution_performance</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html">app/routes/dashboard/service.py</a></td>
                <td class="name left"><a href="z_de01cdf35b22c110_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c___init___py.html">app/routes/data_bank/__init__.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t25">app/routes/data_bank/controller.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t25"><data value='upsert_incident'>upsert_incident</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t70">app/routes/data_bank/controller.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t70"><data value='delete_incident'>delete_incident</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t106">app/routes/data_bank/controller.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t106"><data value='similarity_search'>similarity_search</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t150">app/routes/data_bank/controller.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t150"><data value='trigger_embedding_job'>trigger_embedding_job</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t207">app/routes/data_bank/controller.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html#t207"><data value='document_similarity_search'>document_similarity_search</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html">app/routes/data_bank/controller.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html">app/routes/data_bank/models.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t42">app/routes/data_bank/service.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t42"><data value='upsert_incident'>upsert_incident</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t88">app/routes/data_bank/service.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t88"><data value='delete_incident'>delete_incident</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t127">app/routes/data_bank/service.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t127"><data value='search_similar_content'>search_similar_content</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t206">app/routes/data_bank/service.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t206"><data value='search_similar_incidents'>search_similar_incidents</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t258">app/routes/data_bank/service.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t258"><data value='get_similar_incidents'>get_similar_incidents</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t321">app/routes/data_bank/service.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html#t321"><data value='search_similar_documents'>search_similar_documents</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html">app/routes/data_bank/service.py</a></td>
                <td class="name left"><a href="z_e5d508c905d0e90c_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f___init___py.html">app/routes/events/__init__.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html#t17">app/routes/events/controller.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html#t17"><data value='create_event'>create_event</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html#t29">app/routes/events/controller.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html#t29"><data value='get_incident_events'>get_incident_events</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html#t66">app/routes/events/controller.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html#t66"><data value='get_event'>get_event</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html#t78">app/routes/events/controller.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html#t78"><data value='delete_event'>delete_event</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html">app/routes/events/controller.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html">app/routes/events/models.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html#t17">app/routes/events/service.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html#t17"><data value='create_event_entry'>create_event_entry</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html#t63">app/routes/events/service.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html#t63"><data value='get_incident_events'>get_incident_events</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html#t89">app/routes/events/service.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html#t89"><data value='get_event_by_id'>get_event_by_id</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html#t102">app/routes/events/service.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html#t102"><data value='delete_event_entry'>delete_event_entry</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html">app/routes/events/service.py</a></td>
                <td class="name left"><a href="z_2f2a9d10427d122f_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99___init___py.html">app/routes/incident_metrics/__init__.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html#t16">app/routes/incident_metrics/controller.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html#t16"><data value='get_incident_metrics'>get_incident_metrics</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html#t46">app/routes/incident_metrics/controller.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html#t46"><data value='create_incident_metric'>create_incident_metric</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html#t71">app/routes/incident_metrics/controller.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html#t71"><data value='update_incident_metric'>update_incident_metric</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html">app/routes/incident_metrics/controller.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t37">app/routes/incident_metrics/models.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html#t37"><data value='serialize_timedelta'>IncidentMetricResponse.serialize_timedelta</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html">app/routes/incident_metrics/models.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html#t18">app/routes/incident_metrics/service.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html#t18"><data value='get_incident_metrics'>get_incident_metrics</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html#t44">app/routes/incident_metrics/service.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html#t44"><data value='create_incident_metric'>create_incident_metric</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html#t68">app/routes/incident_metrics/service.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html#t68"><data value='update_incident_metric'>update_incident_metric</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html">app/routes/incident_metrics/service.py</a></td>
                <td class="name left"><a href="z_65913829e64f7d99_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_controller_py.html#t15">app/routes/incident_report/controller.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_controller_py.html#t15"><data value='get_incident_report'>get_incident_report</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_controller_py.html">app/routes/incident_report/controller.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_models_py.html">app/routes/incident_report/models.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_service_py.html#t8">app/routes/incident_report/service.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_service_py.html#t8"><data value='get_incident_report_by_incident_id'>get_incident_report_by_incident_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53411b2bde4672bc_service_py.html">app/routes/incident_report/service.py</a></td>
                <td class="name left"><a href="z_53411b2bde4672bc_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da___init___py.html">app/routes/incidents/__init__.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t21">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t21"><data value='get_incidents'>get_incidents</data></a></td>
                <td>13</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="8 13">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t70">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t70"><data value='create_incident'>create_incident</data></a></td>
                <td>10</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="5 10">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t91">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t91"><data value='get_incident'>get_incident</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t110">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t110"><data value='get_incident_details'>get_incident_details</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t131">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t131"><data value='get_incident_ai_analysis'>get_incident_ai_analysis</data></a></td>
                <td>21</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="13 21">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t185">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t185"><data value='get_similar_incidents'>get_similar_incidents</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t213">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t213"><data value='update_incident'>update_incident</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t236">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t236"><data value='update_incident_details'>update_incident_details</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t265">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t265"><data value='delete_incident'>delete_incident</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t305">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t305"><data value='regenerate_incident_summary'>regenerate_incident_summary</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t329">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html#t329"><data value='generate_incident_pdf_endpoint'>generate_incident_pdf_endpoint</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html">app/routes/incidents/controller.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html">app/routes/incidents/models.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>84</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="84 84">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t36">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t36"><data value='generate_incident_number'>generate_incident_number</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t41">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t41"><data value='create_incident'>create_incident</data></a></td>
                <td>23</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="17 23">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t103">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t103"><data value='get_incident'>get_incident</data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t125">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t125"><data value='get_incident_details'>get_incident_details</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t138">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t138"><data value='get_incident_ai_analysis'>get_incident_ai_analysis</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t154">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t154"><data value='get_similar_incidents'>get_similar_incidents</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t195">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t195"><data value='update_incident'>update_incident</data></a></td>
                <td>33</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="23 33">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t260">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t260"><data value='update_incident_details'>update_incident_details</data></a></td>
                <td>33</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="22 33">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t328">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t328"><data value='delete_incident'>delete_incident</data></a></td>
                <td>25</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="16 25">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t390">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t390"><data value='get_incidents'>get_incidents</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t414">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t414"><data value='regenerate_incident_summary'>regenerate_incident_summary</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t450">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html#t450"><data value='generate_incident_pdf'>generate_incident_pdf</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html">app/routes/incidents/service.py</a></td>
                <td class="name left"><a href="z_375a9ff46253c3da_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t22">app/routes/jobs/controller.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t22"><data value='list_jobs'>list_jobs</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t41">app/routes/jobs/controller.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t41"><data value='list_running_jobs'>list_running_jobs</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t57">app/routes/jobs/controller.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t57"><data value='import_from_github'>import_from_github</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t86">app/routes/jobs/controller.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t86"><data value='sync_from_github'>sync_from_github</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t151">app/routes/jobs/controller.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_controller_py.html#t151"><data value='get_job_status'>get_job_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_controller_py.html">app/routes/jobs/controller.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_models_py.html">app/routes/jobs/models.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_service_py.html#t15">app/routes/jobs/service.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_service_py.html#t15"><data value='get_job_status'>get_job_status</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_service_py.html#t91">app/routes/jobs/service.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_service_py.html#t91"><data value='create_job'>create_job</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_service_py.html#t114">app/routes/jobs/service.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_service_py.html#t114"><data value='list_jobs'>list_jobs</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_service_py.html#t147">app/routes/jobs/service.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_service_py.html#t147"><data value='list_running_jobs'>list_running_jobs</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78b934913049066c_service_py.html">app/routes/jobs/service.py</a></td>
                <td class="name left"><a href="z_78b934913049066c_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e___init___py.html">app/routes/knowledge_base/__init__.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t22">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t22"><data value='create_knowledge_base'>create_knowledge_base</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t39">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t39"><data value='get_knowledge_bases'>get_knowledge_bases</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t56">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t56"><data value='get_knowledge_base'>get_knowledge_base</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t101">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t101"><data value='update_knowledge_base'>update_knowledge_base</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t119">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t119"><data value='delete_knowledge_base'>delete_knowledge_base</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t137">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t137"><data value='get_knowledge_base_documents'>get_knowledge_base_documents</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t184">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t184"><data value='get_knowledge_base_document'>get_knowledge_base_document</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t217">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t217"><data value='create_document'>create_document</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t300">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t300"><data value='update_document'>update_document</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t362">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t362"><data value='delete_document'>delete_document</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t390">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html#t390"><data value='download_document_file'>download_document_file</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html">app/routes/knowledge_base/controller.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html">app/routes/knowledge_base/models.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>97</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="97 97">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t25">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t25"><data value='create_kb_entry'>create_kb_entry</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t68">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t68"><data value='update_kb_entry'>update_kb_entry</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t99">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t99"><data value='get_kb_entry_by_id'>get_kb_entry_by_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t111">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t111"><data value='get_kb_entries_by_project_id'>get_kb_entries_by_project_id</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t129">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t129"><data value='delete_kb_entry'>delete_kb_entry</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t157">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t157"><data value='get_document_by_id'>get_document_by_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t169">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t169"><data value='get_documents_by_knowledge_base'>get_documents_by_knowledge_base</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t204">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t204"><data value='create_document'>create_document</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t280">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t280"><data value='update_document'>update_document</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t346">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t346"><data value='delete_document'>delete_document</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t391">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t391"><data value='create_document_from_url'>create_document_from_url</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t442">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t442"><data value='create_document_from_text'>create_document_from_text</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t490">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html#t490"><data value='update_document_content'>update_document_content</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html">app/routes/knowledge_base/service.py</a></td>
                <td class="name left"><a href="z_eadef2affc5f816e_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca___init___py.html">app/routes/logs/__init__.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_controller_py.html#t13">app/routes/logs/controller.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_controller_py.html#t13"><data value='get_logs'>get_logs</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_controller_py.html#t21">app/routes/logs/controller.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_controller_py.html#t21"><data value='get_labels'>get_labels</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_controller_py.html">app/routes/logs/controller.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t37">app/routes/logs/models.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html#t37"><data value='check_required_fields'>LogQueryParams.check_required_fields</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html">app/routes/logs/models.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t23">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t23"><data value='init__'>BaseConnector.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t26">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t26"><data value='get_logs'>BaseConnector.get_logs</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t29">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t29"><data value='post_process_logs'>BaseConnector.post_process_logs</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t36">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t36"><data value='get_logs'>LokiConnector.get_logs</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t72">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t72"><data value='post_process_logs'>LokiConnector.post_process_logs</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t125">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t125"><data value='fetch_logs_from_loki'>fetch_logs_from_loki</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t184">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html#t184"><data value='fetch_labels_from_loki'>fetch_labels_from_loki</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html">app/routes/logs/service.py</a></td>
                <td class="name left"><a href="z_49e29148a8e3bdca_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e___init___py.html">app/routes/projects/__init__.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t19">app/routes/projects/controller.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t19"><data value='create_project'>create_project</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t34">app/routes/projects/controller.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t34"><data value='get_user_projects'>get_user_projects</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t80">app/routes/projects/controller.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t80"><data value='search_projects'>search_projects</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t127">app/routes/projects/controller.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t127"><data value='get_project'>get_project</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t165">app/routes/projects/controller.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t165"><data value='update_project'>update_project</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t183">app/routes/projects/controller.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html#t183"><data value='delete_project'>delete_project</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html">app/routes/projects/controller.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_models_py.html">app/routes/projects/models.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t16">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t16"><data value='create_project'>create_project</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t36">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t36"><data value='get_project_by_id'>get_project_by_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t48">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t48"><data value='update_project'>update_project</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t87">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t87"><data value='delete_project'>delete_project</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t120">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t120"><data value='get_user_projects'>get_user_projects</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t143">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t143"><data value='get_all_projects'>get_all_projects</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t160">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html#t160"><data value='search_projects'>search_projects</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_922414243b5e276e_service_py.html">app/routes/projects/service.py</a></td>
                <td class="name left"><a href="z_922414243b5e276e_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0___init___py.html">app/routes/runbooks/__init__.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t21">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t21"><data value='create_runbook'>create_runbook</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t53">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t53"><data value='list_runbooks'>list_runbooks</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t72">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t72"><data value='update_runbook'>update_runbook</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t96">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t96"><data value='delete_runbook'>delete_runbook</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t120">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t120"><data value='get_steps'>get_steps</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t164">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t164"><data value='generate_steps'>generate_steps</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t202">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t202"><data value='update_step'>update_step</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t243">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html#t243"><data value='delete_runbook_step'>delete_runbook_step</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html">app/routes/runbooks/controller.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html">app/routes/runbooks/models.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t20">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t20"><data value='list_runbooks'>list_runbooks</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t31">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t31"><data value='create_runbook'>create_runbook</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t45">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t45"><data value='update_runbook'>update_runbook</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t70">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t70"><data value='delete_runbook'>delete_runbook</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t93">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t93"><data value='generate_steps'>generate_steps</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t154">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t154"><data value='update_runbook_step'>update_runbook_step</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t206">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html#t206"><data value='delete_runbook_step'>delete_runbook_step</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html">app/routes/runbooks/service.py</a></td>
                <td class="name left"><a href="z_f89002852d4634c0_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303___init___py.html">app/routes/users/__init__.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html#t16">app/routes/users/controller.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html#t16"><data value='get_current_user'>get_current_user</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html#t30">app/routes/users/controller.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html#t30"><data value='change_password'>change_password</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html#t45">app/routes/users/controller.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html#t45"><data value='delete_user'>delete_user</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html">app/routes/users/controller.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_controller_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t23">app/routes/users/models.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t23"><data value='password_strength'>PasswordChange.password_strength</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t30">app/routes/users/models.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_models_py.html#t30"><data value='passwords_match'>PasswordChange.passwords_match</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_models_py.html">app/routes/users/models.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_service_py.html#t18">app/routes/users/service.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_service_py.html#t18"><data value='change_password'>change_password</data></a></td>
                <td>18</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="14 18">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_service_py.html#t47">app/routes/users/service.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_service_py.html#t47"><data value='delete_user'>delete_user</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0150f696ad884303_service_py.html">app/routes/users/service.py</a></td>
                <td class="name left"><a href="z_0150f696ad884303_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t21">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t21"><data value='init__'>ContentExtractor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t32">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t32"><data value='extract_content'>ContentExtractor.extract_content</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t71">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t71"><data value='extract_text'>ContentExtractor._extract_text</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t90">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t90"><data value='extract_json'>ContentExtractor._extract_json</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t101">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t101"><data value='extract_csv'>ContentExtractor._extract_csv</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t121">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t121"><data value='extract_pdf'>ContentExtractor._extract_pdf</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t168">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t168"><data value='extract_docx'>ContentExtractor._extract_docx</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t188">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html#t188"><data value='extract_doc'>ContentExtractor._extract_doc</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html">app/services/content_extractor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_content_extractor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t51">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t51"><data value='init__'>FileManager.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t55">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t55"><data value='save_uploaded_file'>FileManager.save_uploaded_file</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t139">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t139"><data value='delete_file'>FileManager.delete_file</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t170">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t170"><data value='update_file'>FileManager.update_file</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t197">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t197"><data value='validate_file'>FileManager._validate_file</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t229">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html#t229"><data value='format_file_size'>FileManager._format_file_size</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html">app/services/file_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182___init___py.html">app/tasks/__init__.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t15">app/tasks/github.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t15"><data value='get_github_config'>get_github_config</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t31">app/tasks/github.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t31"><data value='github_import'>github_import</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t90">app/tasks/github.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t90"><data value='progress_callback'>github_import.progress_callback</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t171">app/tasks/github.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t171"><data value='incident_sync'>incident_sync</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t229">app/tasks/github.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t229"><data value='progress_callback'>incident_sync.progress_callback</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t312">app/tasks/github.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html#t312"><data value='validate_github_repo_format'>validate_github_repo_format</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html">app/tasks/github.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_github_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_jira_py.html#t5">app/tasks/jira.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_jira_py.html#t5"><data value='jira_import'>jira_import</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_jira_py.html">app/tasks/jira.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_jira_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_service_now_py.html#t5">app/tasks/service_now.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_service_now_py.html#t5"><data value='service_now_import'>service_now_import</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_service_now_py.html">app/tasks/service_now.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_service_now_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t37">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t37"><data value='upsert_incident_embedding_task'>upsert_incident_embedding_task</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t106">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t106"><data value='upsert_document_embedding_task'>upsert_document_embedding_task</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t177">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t177"><data value='process_document_task'>process_document_task</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t349">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t349"><data value='batch_sync_documents_task'>batch_sync_documents_task</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t417">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t417"><data value='fetch_url_content'>_fetch_url_content</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t513">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t513"><data value='process_document_from_url'>_process_document_from_url</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t575">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t575"><data value='is_document_type'>_is_document_type</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t606">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html#t606"><data value='detect_mime_type'>_detect_mime_type</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html">app/tasks/vector_db.py</a></td>
                <td class="name left"><a href="z_6f83b30fdb6a6182_vector_db_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_celery_worker_py.html">app/utils/celery_worker.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_celery_worker_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t11">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t11"><data value='init__'>UserNotFoundError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t19">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t19"><data value='init__'>PasswordMismatchError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t24">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t24"><data value='init__'>InvalidPasswordError.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t29">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html#t29"><data value='init__'>AuthenticationError.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html">app/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t11">app/utils/logger.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t11"><data value='setup_logger'>setup_logger</data></a></td>
                <td>19</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="18 19">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t61">app/utils/logger.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t61"><data value='get_app_logger'>get_app_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t70">app/utils/logger.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t70"><data value='get_controller_logger'>get_controller_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t74">app/utils/logger.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t74"><data value='get_service_logger'>get_service_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t78">app/utils/logger.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t78"><data value='get_task_logger'>get_task_logger</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t82">app/utils/logger.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html#t82"><data value='get_middleware_logger'>get_middleware_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html">app/utils/logger.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_logger_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_rate_limiter_py.html">app/utils/rate_limiter.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_rate_limiter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918___init___py.html">app/vector_db/__init__.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t18">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t18"><data value='init__'>VectorDBConnector.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t24">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t24"><data value='create_client'>VectorDBConnector._create_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t34">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t34"><data value='create_collections_if_not_exist'>VectorDBConnector._create_collections_if_not_exist</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t41">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t41"><data value='upsert_vector'>VectorDBConnector._upsert_vector</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t65">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t65"><data value='delete_vector'>VectorDBConnector._delete_vector</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t81">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html#t81"><data value='find_similar_vectors'>VectorDBConnector._find_similar_vectors</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html">app/vector_db/base_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_base_connector_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_embeddings_py.html#t12">app/vector_db/embeddings.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_embeddings_py.html#t12"><data value='generate_embedding'>generate_embedding</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_embeddings_py.html">app/vector_db/embeddings.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_embeddings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_models_py.html">app/vector_db/models.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t43">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t43"><data value='init__'>QdrantConnector.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t49">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t49"><data value='create_client'>QdrantConnector._create_client</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t60">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t60"><data value='create_collections_if_not_exist'>QdrantConnector._create_collections_if_not_exist</data></a></td>
                <td>14</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="9 14">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t96">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t96"><data value='upsert_vector'>QdrantConnector._upsert_vector</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t150">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t150"><data value='delete_vector'>QdrantConnector._delete_vector</data></a></td>
                <td>15</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="8 15">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t192">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html#t192"><data value='find_similar_vectors'>QdrantConnector._find_similar_vectors</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html">app/vector_db/qdrant_connector.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_qdrant_connector_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t27">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t27"><data value='init__'>VectorSearchService.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t35">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t35"><data value='search_similar_incidents_by_text'>VectorSearchService.search_similar_incidents_by_text</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t53">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t53"><data value='search_similar_incidents_by_id'>VectorSearchService.search_similar_incidents_by_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t86">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t86"><data value='search_similar_documents_by_text'>VectorSearchService.search_similar_documents_by_text</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t109">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t109"><data value='process_incident_results'>VectorSearchService._process_incident_results</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t142">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t142"><data value='process_document_results'>VectorSearchService._process_document_results</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t169">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t169"><data value='upsert_incident'>VectorSearchService.upsert_incident</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t208">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t208"><data value='upsert_document'>VectorSearchService.upsert_document</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t255">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t255"><data value='delete_incident_vector'>VectorSearchService.delete_incident_vector</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t259">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html#t259"><data value='delete_document_vector'>VectorSearchService.delete_document_vector</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html">app/vector_db/search_service.py</a></td>
                <td class="name left"><a href="z_e47291130daa1918_search_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>5337</td>
                <td>2953</td>
                <td>0</td>
                <td class="right" data-ratio="2384 5337">45%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-20 19:31 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

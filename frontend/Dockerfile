# Stage 1: Build the React application
FROM node:22-alpine AS build
WORKDIR /app
COPY package.json .
COPY pnpm-lock.yaml .
RUN npm install -g pnpm && pnpm install
COPY . .
RUN pnpm build

# Stage 2: Serve the built application with Nginx
FROM nginx:alpine
RUN apk add --no-cache curl && rm /etc/nginx/conf.d/default.conf
COPY nginx/nginx.conf /etc/nginx/conf.d
COPY nginx/config-env.sh /docker-entrypoint.d/40-config-env.sh
RUN chmod +x /docker-entrypoint.d/40-config-env.sh
COPY --from=build /app/dist /usr/share/nginx/html

# Default config for local development
ENV API_URL=http://localhost:2000/api

EXPOSE 2000
CMD ["nginx", "-g", "daemon off;"]

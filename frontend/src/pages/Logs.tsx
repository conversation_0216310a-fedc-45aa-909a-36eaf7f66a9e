import {
  ActionIcon,
  Badge,
  Button,
  Container,
  Flex,
  Group,
  MultiSelect,
  Popover,
  rem,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import '@mantine/dates/styles.css';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { ClockArrowUp, Copy, Filter, PlusIcon, Search, X } from 'lucide-react';
import { useEffect, useState, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router';
import { Column } from '../components/DataTable';
import { InfiniteDataTable } from '../components/InfiniteDataTable';
import { queryLabels, useInfiniteLogs } from '../hooks/useApi';
import { LogEntry } from '../types/logsType';
import { buildLokiQuery } from '../utils/logql';
dayjs.extend(utc);

const logLevelColors = {
  info: 'green',
  warning: 'orange',
  error: 'red',
  debug: 'blue',
  unknown: 'gray',
} as const;

const logLevels = Object.keys(logLevelColors);

const formatDate = (date: Date | null): string | null =>
  date && dayjs(date).isValid()
    ? dayjs(date).utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
    : null;

export default function Logs() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const logLimit = 100;

  // Initialize date state from URL or today range
  const [startDate, setStartDate] = useState(() =>
    searchParams.get('startDate')
      ? dayjs(searchParams.get('startDate')).toDate()
      : dayjs.utc().startOf('day').toDate(),
  );
  const [endDate, setEndDate] = useState(() =>
    searchParams.get('endDate')
      ? dayjs(searchParams.get('endDate')).toDate()
      : dayjs.utc().toDate(),
  );
  const [filterOpened, setFilterOpened] = useState(false);
  const [timeRangeOpened, setTimeRangeOpened] = useState(false);

  const [searchQuery, setSearchQuery] = useState(
    searchParams.get('query') || '',
  );
  const [selectedServices, setSelectedServices] = useState(
    searchParams.getAll('service'),
  );
  const [selectedLogLevels, setSelectedLogLevels] = useState(
    searchParams.getAll('logLevel').length > 0
      ? searchParams.getAll('logLevel')
      : logLevels,
  );
  const [selectedHostnames, setSelectedHostnames] = useState(
    searchParams.getAll('hostnames'),
  );

  // Build the query for infinite logs
  const lokiQuery = useMemo(
    () =>
      buildLokiQuery({
        job: '',
        selectedHostnames,
        selectedServices,
        filename: '',
        extraLabels: {},
        searchExpression: searchQuery,
        labelFormat: {},
      }),
    [selectedHostnames, selectedServices, searchQuery],
  );

  // Use infinite query for logs
  const {
    data: infiniteLogsData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteLogs(
    lokiQuery,
    formatDate(startDate) || dayjs.utc().startOf('day').toISOString(),
    formatDate(endDate) || dayjs.utc().toISOString(),
    logLimit,
    'backward',
  );

  // Flatten all pages of logs data
  const allLogs = useMemo(() => {
    return infiniteLogsData?.pages.flatMap(page => page.data) || [];
  }, [infiniteLogsData]);

  // Fetch labels for filters - using the first page data for filter options
  const { data: labelsData } = queryLabels(
    formatDate(startDate) || dayjs.utc().startOf('day').toISOString(),
    formatDate(endDate) || dayjs.utc().toISOString(),
    buildLokiQuery({}),
  );

  console.log('labelsData', labelsData);

  // Update URL params on filter changes
  useEffect(() => {
    const newParams = new URLSearchParams();

    if (searchQuery) newParams.set('query', searchQuery);

    const formattedStartDate = formatDate(startDate);
    const formattedEndDate = formatDate(endDate);
    if (formattedStartDate) newParams.set('startDate', formattedStartDate);
    if (formattedEndDate) newParams.set('endDate', formattedEndDate);

    selectedHostnames.forEach(hostname => {
      newParams.append('hostnames', hostname);
    });

    selectedServices.forEach(service => {
      newParams.append('service', service);
    });

    selectedLogLevels.forEach(level => {
      newParams.append('logLevel', level);
    });

    // Only update if params changed
    const currentParams = new URLSearchParams(searchParams);
    if (newParams.toString() !== currentParams.toString()) {
      setSearchParams(newParams);
    }
  }, [
    searchQuery,
    selectedHostnames,
    selectedServices,
    selectedLogLevels,
    startDate,
    endDate,
    setSearchParams,
  ]);

  // Toggle log level filter on/off
  const toggleLogLevel = (level: string) => {
    setSelectedLogLevels(prev =>
      prev.includes(level) ? prev.filter(l => l !== level) : [...prev, level],
    );
  };

  // Copy a log line text to clipboard with formatted time
  const copyLogLine = async (log: LogEntry) => {
    try {
      const timeStr = dayjs(log.timestamp / 1000000)
        .local()
        .format('YYYY-MM-DD HH:mm:ss');
      const logLine = `${timeStr} [${log.labels.hostname}] ${log.line}`;
      await navigator.clipboard.writeText(logLine);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  // Create incident from log entry
  const createIncidentFromLog = (log: LogEntry) => {
    const timeStr = dayjs(log.timestamp / 1000000)
      .local()
      .format('YYYY-MM-DD HH:mm:ss');
    const incidentData = {
      title: `Incident from ${log.labels.service_name}`,
      details: `
      This incident was automatically created from a log entry.
      ${log.line}
      This log entry was detected in the logs of ${log.labels.service_name} on ${log.labels.hostname} at ${timeStr}.
      `,
      services: [log.labels.service_name],
    };
    navigate('/incident/create', { state: { incidentData } });
  };

  // Search similar logs by keywords from log line
  const searchSimilarLogs = (log: LogEntry) => {
    const messagePart = log.line.split(/:\s(.+)/)[1] || log.line;

    // Attempt to match structured error-like patterns
    const errorPattern =
      /(\b\w+\b\s+(failed|unable|denied|refused|error|unknown)\s+to\s+\b\w+(?:\s+\b\w+)?)/i;
    const errorMatch = messagePart.match(errorPattern);

    if (errorMatch) {
      const errorWords = errorMatch[1]
        .split(' ')
        .filter(word => word.length > 2);
      const lastErrorWords = errorWords.slice(-3).join(' ');
      setSearchQuery(lastErrorWords);
      return;
    }

    // Clean the message while preserving dots/hyphens in service names
    const cleanedLine = messagePart
      .replace(/[^\w\s.\-]/g, ' ') // Preserve service names
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // Step 1: Split into words
    const words = cleanedLine.split(' ').filter(word => word.length > 2); // Filter out short/noise words

    // Step 2: Take the last 3 words as fallback
    const lastWords = words.slice(-3).join(' ');

    setSearchQuery(lastWords);
  };

  // Filter logs based on selected log levels
  const filteredLogData = useMemo(() => {
    return allLogs.filter((log: LogEntry) =>
      selectedLogLevels.includes(log.labels.detected_level.toLowerCase()),
    );
  }, [allLogs, selectedLogLevels]);

  // Define columns for the DataTable
  const columns: Column<LogEntry>[] = [
    {
      key: 'timestamp',
      header: 'Time',
      render: (log: LogEntry) => (
        <Text fw={500} size="sm">
          {dayjs(log.timestamp / 1000000)
            .local()
            .format('YYYY-MM-DD HH:mm:ss')}
        </Text>
      ),
    },
    {
      key: 'hostname',
      header: 'Hostname',
      render: (log: LogEntry) => <Text size="sm">{log.labels.hostname}</Text>,
    },
    {
      key: 'service',
      header: 'Service',
      render: (log: LogEntry) => (
        <Text size="sm">{log.labels.service_name}</Text>
      ),
    },
    {
      key: 'filename',
      header: 'Filename',
      render: (log: LogEntry) => <Text size="sm">{log.labels.filename}</Text>,
    },
    {
      key: 'logLevel',
      header: 'Log Level',
      render: (log: LogEntry) => (
        <Badge
          variant="light"
          color={
            logLevelColors[
              log.labels.detected_level.toLowerCase() as keyof typeof logLevelColors
            ] || 'gray'
          }
          size="xs"
        >
          {log.labels.detected_level.toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'line',
      header: 'Line',
      render: (log: LogEntry) => (
        <Text size="sm" maw={350} truncate="end">
          {log.line}
        </Text>
      ),
      expandedContent: (log: LogEntry) => (
        <Text size="sm" style={{ whiteSpace: 'pre-wrap' }}>
          {log.line}
        </Text>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (log: LogEntry) => (
        <Group gap="xs">
          <Tooltip label="Copy log line">
            <ActionIcon
              variant="subtle"
              color="gray"
              size="sm"
              onClick={e => {
                e.stopPropagation();
                copyLogLine(log);
              }}
            >
              <Copy size={14} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Create incident from log">
            <ActionIcon
              variant="subtle"
              color="orange"
              size="sm"
              onClick={e => {
                e.stopPropagation();
                createIncidentFromLog(log);
              }}
            >
              <PlusIcon size={14} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Search similar logs">
            <ActionIcon
              variant="subtle"
              color="blue"
              size="sm"
              onClick={e => {
                e.stopPropagation();
                searchSimilarLogs(log);
              }}
            >
              <Search size={14} />
            </ActionIcon>
          </Tooltip>
        </Group>
      ),
    },
  ];

  return (
    <Container fluid className="w-full">
      <Stack gap="xl">
        {/* Page Header */}
        <Flex justify="space-between" align="center">
          <Title order={1} size={rem(36)} fw={600} c="var(--color-primary)">
            Logs
          </Title>
        </Flex>

        {/* Search and Filters */}
        <Stack gap="md">
          <Flex justify="space-between" align="center" gap="md" wrap="wrap">
            <TextInput
              placeholder="Search Logs"
              leftSection={<Search size={16} />}
              rightSection={
                searchQuery && (
                  <Tooltip label="Clear search">
                    <ActionIcon
                      variant="subtle"
                      color="gray"
                      size="sm"
                      onClick={() => setSearchQuery('')}
                    >
                      <X size={14} />
                    </ActionIcon>
                  </Tooltip>
                )
              }
              flex={1}
              miw={300}
              radius="md"
              value={searchQuery}
              onChange={e => setSearchQuery(e.currentTarget.value)}
            />
            <Popover
              opened={timeRangeOpened}
              position="bottom-end"
              shadow="md"
              withArrow
            >
              <Popover.Target>
                <Tooltip label="Select time range">
                  <ActionIcon
                    variant="outline"
                    color="var(--color-primary)"
                    size="lg"
                    onClick={() => setTimeRangeOpened(o => !o)}
                  >
                    <ClockArrowUp size={16} />
                  </ActionIcon>
                </Tooltip>
              </Popover.Target>
              <Popover.Dropdown>
                <Stack gap="md" w={320}>
                  <DateTimePicker
                    label="Start Date & Time"
                    placeholder="Select start date and time"
                    value={startDate}
                    onChange={(value: any) => setStartDate(value)}
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                  <DateTimePicker
                    label="End Date & Time"
                    placeholder="Select end date and time"
                    value={endDate}
                    onChange={(value: any) => setEndDate(value)}
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    clearable
                  />
                </Stack>
              </Popover.Dropdown>
            </Popover>

            <Group gap="sm">
              <Popover
                opened={filterOpened}
                onChange={setFilterOpened}
                position="bottom-end"
                shadow="md"
                withArrow
              >
                <Popover.Target>
                  <Tooltip label="Filter logs">
                    <ActionIcon
                      variant="outline"
                      color="var(--color-primary)"
                      size="lg"
                      onClick={() => setFilterOpened(o => !o)}
                    >
                      <Filter size={16} />
                    </ActionIcon>
                  </Tooltip>
                </Popover.Target>
                <Popover.Dropdown>
                  <Stack gap="md" w={320}>
                    <Text size="sm" fw={500}>
                      Filter Options
                    </Text>
                    <MultiSelect
                      label="Hostname"
                      placeholder="Select hostnames"
                      data={Array.from(
                        new Set(
                          allLogs.map((log: LogEntry) => log.labels.hostname) ||
                            [],
                        ),
                      ).map((hostname: string) => ({
                        value: hostname,
                        label: hostname,
                      }))}
                      value={selectedHostnames}
                      onChange={setSelectedHostnames}
                      clearable
                      radius="md"
                    />
                    <MultiSelect
                      label="Services"
                      placeholder="Select services"
                      data={Array.from(
                        new Set(
                          allLogs.map(
                            (log: LogEntry) => log.labels.service_name,
                          ) || [],
                        ),
                      ).map((service: string) => ({
                        value: service,
                        label: service,
                      }))}
                      value={selectedServices}
                      onChange={setSelectedServices}
                      clearable
                      radius="md"
                    />
                    <MultiSelect
                      label="Log Levels"
                      placeholder="Select log levels"
                      data={logLevels.map(level => ({
                        value: level,
                        label: level.toUpperCase(),
                      }))}
                      value={selectedLogLevels}
                      onChange={setSelectedLogLevels}
                      clearable
                      radius="md"
                    />
                  </Stack>
                </Popover.Dropdown>
              </Popover>
            </Group>
          </Flex>

          {/* Active Filters Summary */}
          {(selectedServices.length > 0 || selectedLogLevels.length < 5) && (
            <Flex gap="xs" mt="sm" align="center">
              <Text size="sm" c="dimmed">
                Active filters:
              </Text>

              {selectedServices.map(service => (
                <Badge
                  key={service}
                  variant="light"
                  color="blue"
                  size="sm"
                  style={{ cursor: 'pointer' }}
                  onClick={() =>
                    setSelectedServices(prev => prev.filter(s => s !== service))
                  }
                >
                  {service} ×
                </Badge>
              ))}
              {selectedLogLevels.length < 5 &&
                logLevels
                  .filter(level => !selectedLogLevels.includes(level))
                  .map(level => (
                    <Badge
                      key={level}
                      variant="light"
                      color="orange"
                      size="sm"
                      style={{ cursor: 'pointer' }}
                      onClick={() => toggleLogLevel(level)}
                    >
                      -{level} ×
                    </Badge>
                  ))}
              <Button
                variant="subtle"
                size="xs"
                onClick={() => {
                  setSelectedServices([]);
                  setSelectedLogLevels(logLevels);
                }}
              >
                Clear all
              </Button>
            </Flex>
          )}

          <InfiniteDataTable
            data={filteredLogData}
            columns={columns}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
            fetchNextPage={fetchNextPage}
            isLoading={isLoading}
            keyExtractor={(log: LogEntry) => log.timestamp.toString()}
          />
        </Stack>
      </Stack>
    </Container>
  );
}

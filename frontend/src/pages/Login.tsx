import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  Card,
  Checkbox,
  Group,
  Image,
  LoadingOverlay,
  Text,
  TextInput,
} from '@mantine/core';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import LoginBackground from '../assets/LoginBackground.svg';
import Logo from '../assets/logos/Logo.svg';
import { useAuth } from '../contexts/AuthContext';

// Define validation schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean(),
});

type LoginFormData = z.infer<typeof loginSchema>;

const Login = () => {
  const { login, isLoggingIn, loginError } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
    mode: 'onChange',
  });

  const rememberMe = watch('rememberMe');

  const onSubmit = async (data: LoginFormData) => {
    login({ username: data.username, password: data.password });
  };

  return (
    <div
      className="w-screen h-screen flex items-center justify-center"
      style={{
        backgroundColor: 'var(--color-primary)',
        backgroundImage: `url(${LoginBackground})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        position: 'relative',
        fontFamily: 'var(--font-family-base)',
      }}
    >
      <div className="absolute top-10 left-10">
        <div className="text-white flex items-center gap-2">
          <Group className="pb-1">
            <Image src={Logo} alt="Abilytics" w={110} h={25} />
          </Group>
        </div>
      </div>

      <div className="w-full max-w-lg mx-auto px-4 relative z-10">
        <div className="text-center text-white mb-8">
          <h2 className="text-4xl font-semibold mb-4">
            Sign in to your account
          </h2>
          <p className="text-lg font-light">
            Access Abilytics to help you swiftly tackle issues and maintain high
            service standards.
          </p>
        </div>

        <Card shadow="md" radius="md" p="xl" withBorder className="w-full">
          <div className="relative">
            <LoadingOverlay visible={isLoggingIn} />

            <form onSubmit={handleSubmit(onSubmit)}>
              <TextInput
                placeholder="Username or Email"
                {...register('username')}
                error={errors.username?.message}
                required
                mb="md"
                size="md"
                styles={{
                  input: { fontFamily: 'var(--font-family-base)' },
                  label: { fontFamily: 'var(--font-family-base)' },
                }}
              />

              <TextInput
                placeholder="Password"
                type="password"
                {...register('password')}
                error={errors.password?.message}
                required
                mb="md"
                size="md"
                styles={{
                  input: { fontFamily: 'var(--font-family-base)' },
                  label: { fontFamily: 'var(--font-family-base)' },
                }}
              />

              <Group justify="space-between" mb="md">
                <Checkbox
                  label="Remember me"
                  {...register('rememberMe')}
                  checked={rememberMe}
                  styles={{ label: { fontFamily: 'var(--font-family-base)' } }}
                />
                <Text
                  component="a"
                  href="#"
                  size="sm"
                  style={{
                    color: 'var(--color-primary)',
                    fontFamily: 'var(--font-family-base)',
                  }}
                >
                  Forgot password?
                </Text>
              </Group>

              {loginError && (
                <Text
                  color="var(--error)"
                  size="sm"
                  mb="md"
                  style={{ fontFamily: 'var(--font-family-base)' }}
                >
                  {loginError instanceof Error
                    ? loginError.message
                    : 'Invalid credentials. Please try again.'}
                </Text>
              )}

              <Button
                fullWidth
                type="submit"
                color="var(--color-primary)"
                disabled={isLoggingIn}
                size="md"
                className="mb-4"
                style={{
                  backgroundColor: 'var(--color-primary)',
                  fontFamily: 'var(--font-family-base)',
                }}
              >
                Sign In
              </Button>

              <Button
                fullWidth
                variant="outline"
                color="gray"
                className="mb-6"
                style={{ fontFamily: 'var(--font-family-base)' }}
                leftSection={
                  <img
                    src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"
                    width={18}
                    height={18}
                    alt="Google"
                  />
                }
              >
                Sign in with Google
              </Button>

              <Text
                ta="center"
                size="sm"
                style={{ fontFamily: 'var(--font-family-base)' }}
              >
                Don't have an account?{' '}
                <Text
                  component="span"
                  style={{
                    color: 'var(--color-primary)',
                    fontFamily: 'var(--font-family-base)',
                    fontWeight: 500,
                  }}
                >
                  Sign up
                </Text>
              </Text>
            </form>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;

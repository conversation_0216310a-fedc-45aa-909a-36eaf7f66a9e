import {
  Button,
  Container,
  Grid,
  Group,
  LoadingOverlay,
  Modal,
  Pagination,
  Paper,
  Stack,
  Text,
  TextInput,
  Title,
} from '@mantine/core';
import { Plus, Search } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';
import BreadcrumbNavigation from '../components/BreadcrumbNavigation';
import CreateProjectModal from '../components/Project/CreateProjectModal';
import EditProjectModal from '../components/Project/EditProjectModal';
import ProjectCard from '../components/Project/ProjectCard';
import { useProjectContext } from '../contexts/ProjectContext';
import {
  useCreateProject,
  useDeleteProject,
  useProjects,
  useUpdateProject,
} from '../hooks/useKnowledgeBase';
import {
  ProjectCreate,
  ProjectListItem,
  ProjectUpdate,
} from '../types/KnowledgeBaseTypes';

export default function Projects() {
  const navigate = useNavigate();
  const { selectedProject, setSelectedProject } = useProjectContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedProjectForAction, setSelectedProjectForAction] =
    useState<ProjectListItem | null>(null);

  const pageSize = 12;
  const offset = (currentPage - 1) * pageSize;

  // Data fetching
  const { data: projectsData, isLoading: projectsLoading } = useProjects(
    offset,
    pageSize,
  );

  // Mutations
  const createProjectMutation = useCreateProject();
  const updateProjectMutation = useUpdateProject();
  const deleteProjectMutation = useDeleteProject();

  // Generate breadcrumbs
  const breadcrumbs = [{ title: 'Projects', to: null }];

  // Handle actions
  const handleCreateProject = async (data: ProjectCreate) => {
    try {
      const newProject = await createProjectMutation.mutateAsync(data);
      toast.success('Project created successfully');
      setCreateModalOpen(false);

      // Set as selected project if it's the first one
      if (!selectedProject) {
        setSelectedProject(newProject);
      }
    } catch (error) {
      toast.error('Failed to create project');
    }
  };

  const handleEditProject = async (data: ProjectUpdate) => {
    if (!selectedProjectForAction) return;

    try {
      const updatedProject = await updateProjectMutation.mutateAsync({
        projectId: selectedProjectForAction.id,
        data,
      });
      toast.success('Project updated successfully');
      setEditModalOpen(false);
      setSelectedProjectForAction(null);

      // Update selected project if it was the one being edited
      if (selectedProject?.id === selectedProjectForAction.id) {
        setSelectedProject(updatedProject);
      }
    } catch (error) {
      toast.error('Failed to update project');
    }
  };

  const handleDeleteProject = async () => {
    if (!selectedProjectForAction) return;

    try {
      await deleteProjectMutation.mutateAsync(selectedProjectForAction.id);
      toast.success('Project deleted successfully');
      setDeleteModalOpen(false);

      // Clear selected project if it was the one being deleted
      if (selectedProject?.id === selectedProjectForAction.id) {
        setSelectedProject(null);
      }

      setSelectedProjectForAction(null);
    } catch (error) {
      toast.error('Failed to delete project');
    }
  };

  const handleProjectSelect = (project: ProjectListItem) => {
    setSelectedProject(project);
    toast.success(`Selected project: ${project.name}`);
  };

  const handleProjectEdit = (project: ProjectListItem) => {
    setSelectedProjectForAction(project);
    setEditModalOpen(true);
  };

  const handleProjectDelete = (project: ProjectListItem) => {
    setSelectedProjectForAction(project);
    setDeleteModalOpen(true);
  };

  const handleViewKnowledgeBase = (project: ProjectListItem) => {
    setSelectedProject(project);
    navigate('/knowledge-base');
  };

  const isLoading =
    projectsLoading ||
    createProjectMutation.isPending ||
    updateProjectMutation.isPending ||
    deleteProjectMutation.isPending;

  return (
    <Container fluid p="md">
      <Paper p="lg" radius="md" withBorder shadow="sm" pos="relative">
        <LoadingOverlay visible={isLoading} />

        <Stack gap="md">
          {/* Breadcrumbs */}
          <BreadcrumbNavigation items={breadcrumbs} />

          {/* Header */}
          <Group justify="space-between" align="center">
            <div>
              <Title order={1} size="h2" c="var(--color-primary)">
                Projects
              </Title>
              <Text size="sm" c="dimmed">
                Manage your projects
              </Text>
            </div>

            <Button
              leftSection={<Plus size={16} />}
              onClick={() => setCreateModalOpen(true)}
              style={{ backgroundColor: 'var(--color-primary)' }}
            >
              Create Project
            </Button>
          </Group>

          {/* Search */}
          <Group>
            <TextInput
              placeholder="Search projects..."
              leftSection={<Search size={16} />}
              value={searchQuery}
              onChange={e => setSearchQuery(e.currentTarget.value)}
              style={{ flex: 1 }}
            />
          </Group>

          {/* Content Grid */}
          <Grid>
            {projectsData?.items.length === 0 ? (
              <Grid.Col span={12}>
                <Paper
                  p="xl"
                  radius="md"
                  withBorder
                  style={{ textAlign: 'center' }}
                >
                  <Stack gap="md" align="center">
                    <Text size="lg" fw={500} c="dimmed">
                      No projects found
                    </Text>
                    <Text size="sm" c="dimmed">
                      Create your first project to get started with knowledge
                      management
                    </Text>
                    <Button
                      leftSection={<Plus size={16} />}
                      onClick={() => setCreateModalOpen(true)}
                      style={{ backgroundColor: 'var(--color-primary)' }}
                    >
                      Create Project
                    </Button>
                  </Stack>
                </Paper>
              </Grid.Col>
            ) : (
              projectsData?.items
                .filter(
                  project =>
                    !searchQuery ||
                    project.name
                      .toLowerCase()
                      .includes(searchQuery.toLowerCase()) ||
                    project.description
                      .toLowerCase()
                      .includes(searchQuery.toLowerCase()),
                )
                .map(project => (
                  <Grid.Col
                    key={project.id}
                    span={{ base: 12, sm: 6, md: 4, lg: 3 }}
                  >
                    <ProjectCard
                      project={project}
                      onEdit={handleProjectEdit}
                      onDelete={handleProjectDelete}
                      onViewKnowledgeBase={handleViewKnowledgeBase}
                      isSelected={selectedProject?.id === project.id}
                      onSelect={() => handleProjectSelect(project)}
                    />
                  </Grid.Col>
                ))
            )}
          </Grid>

          {/* Pagination */}
          {projectsData && projectsData.total > pageSize && (
            <Group justify="center">
              <Pagination
                total={Math.ceil(projectsData.total / pageSize)}
                value={currentPage}
                onChange={setCurrentPage}
              />
            </Group>
          )}
        </Stack>

        {/* Modals */}
        <CreateProjectModal
          opened={createModalOpen}
          onClose={() => setCreateModalOpen(false)}
          onSubmit={handleCreateProject}
          loading={createProjectMutation.isPending}
        />

        <EditProjectModal
          opened={editModalOpen}
          onClose={() => {
            setEditModalOpen(false);
            setSelectedProjectForAction(null);
          }}
          onSubmit={handleEditProject}
          project={selectedProjectForAction}
          loading={updateProjectMutation.isPending}
        />

        <Modal
          opened={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setSelectedProjectForAction(null);
          }}
          title="Delete Project"
          size="sm"
        >
          <Stack gap="md">
            <Text>
              Are you sure you want to delete the project "
              {selectedProjectForAction?.name}"? This action cannot be undone
              and will also delete all associated knowledge bases and documents.
            </Text>
            <Group justify="flex-end" gap="sm">
              <Button
                variant="outline"
                onClick={() => {
                  setDeleteModalOpen(false);
                  setSelectedProjectForAction(null);
                }}
              >
                Cancel
              </Button>
              <Button
                color="red"
                onClick={handleDeleteProject}
                loading={deleteProjectMutation.isPending}
              >
                Delete Project
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Paper>
    </Container>
  );
}

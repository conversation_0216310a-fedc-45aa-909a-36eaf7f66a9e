import {
  ActionIcon,
  Avatar,
  Button,
  Group,
  TextInput,
  Tooltip,
} from '@mantine/core';
import { Search, UserPlus, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Column, DataTable } from '../components/DataTable';

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
}

export default function Users() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Mock data for now - replace with actual API call
    const mockUsers = [
      {
        id: '1',
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
      },
      {
        id: '2',
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
      },
      {
        id: '3',
        email: 'bob.joh<PERSON>@example.com',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
      },
      {
        id: '4',
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
      },
      {
        id: '5',
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
      },
      {
        id: '6',
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
      },
      {
        id: '7',
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: 'Stark',
      },
    ];

    setUsers(mockUsers);
    setLoading(false);
  }, []);

  const columns: Column<User>[] = [
    {
      key: 'name',
      header: 'Name',
      render: (user: User) => (
        <Group gap="sm">
          <Avatar
            size={26}
            radius={26}
            src={`https://ui-avatars.com/api/?name=${user.first_name}+${user.last_name}&background=random`}
          />
          <span className="font-semibold">
            {user.first_name} {user.last_name}
          </span>
        </Group>
      ),
    },
    {
      key: 'email',
      header: 'Email',
      render: (user: User) => <span className="text-sm">{user.email}</span>,
    },
    {
      key: 'role',
      header: 'Role',
      render: () => <span className="text-sm font-medium">User</span>,
    },
  ];

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-4xl font-semibold text-primary">Users</h2>
        <div className="flex items-center gap-4">
          <TextInput
            placeholder="Search users..."
            leftSection={<Search size={16} />}
            rightSection={
              searchQuery && (
                <Tooltip label="Clear search">
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    size="sm"
                    onClick={() => setSearchQuery('')}
                  >
                    <X size={14} />
                  </ActionIcon>
                </Tooltip>
              )
            }
            className="w-[300px]"
            radius="md"
            value={searchQuery}
            onChange={event => setSearchQuery(event.currentTarget.value)}
          />
          <Button
            leftSection={<UserPlus size={16} />}
            variant="filled"
            color="var(--color-primary)"
            className="bg-primary text-white hover:bg-primary/90"
          >
            Add User
          </Button>
        </div>
      </div>

      {loading ? (
        <div>Loading users...</div>
      ) : (
        <DataTable
          data={users}
          columns={columns}
          itemsPerPage={7}
          keyExtractor={(user: User) => user.id}
        />
      )}
    </div>
  );
}

import {
  Button,
  Container,
  Grid,
  Group,
  LoadingOverlay,
  Modal,
  Pagination,
  Paper,
  Stack,
  Text,
  TextInput,
  Title,
} from '@mantine/core';
import { Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { toast } from 'react-toastify';
import BreadcrumbNavigation from '../components/BreadcrumbNavigation';
import CreateKnowledgeBaseModal from '../components/KnowledgeBase/CreateKnowledgeBaseModal';
import DocumentCard from '../components/KnowledgeBase/DocumentCard';
import EditKnowledgeBaseModal from '../components/KnowledgeBase/EditKnowledgeBaseModal';
import FileUploadModal from '../components/KnowledgeBase/FileUploadModal';
import KnowledgeBaseCard from '../components/KnowledgeBase/KnowledgeBaseCard';
import { useProjectContext } from '../contexts/ProjectContext';
import {
  useCreateDocument,
  useCreateKnowledgeBase,
  useDeleteDocument,
  useDeleteKnowledgeBase,
  useDocuments,
  useDownloadDocument,
  useKnowledgeBase,
  useKnowledgeBases,
  useUpdateKnowledgeBase,
} from '../hooks/useKnowledgeBase';
import {
  DocumentCreate,
  DocumentListItem,
  KnowledgeBase,
  KnowledgeBaseCreate,
  KnowledgeBaseUpdate,
} from '../types/KnowledgeBaseTypes';

export default function KnowledgeBasePage() {
  const { kbId } = useParams<{ kbId?: string }>();
  const navigate = useNavigate();
  const { selectedProject } = useProjectContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [createKbModalOpen, setCreateKbModalOpen] = useState(false);
  const [editKbModalOpen, setEditKbModalOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedKb, setSelectedKb] = useState<KnowledgeBase | null>(null);
  const [selectedKbForUpload, setSelectedKbForUpload] = useState<string>('');

  const pageSize = 12;
  const offset = (currentPage - 1) * pageSize;

  if (!selectedProject) return null;
  // Data fetching based on current view
  const { data: knowledgeBases, isLoading: projectLoading } = useKnowledgeBases(
    selectedProject.id,
  );
  const { data: currentKb, isLoading: kbLoading } = useKnowledgeBase(
    kbId || '',
  );
  const { data: documentsData, isLoading: documentsLoading } = useDocuments(
    kbId || '',
    offset,
    pageSize,
  );

  // Mutations
  const createKbMutation = useCreateKnowledgeBase();
  const updateKbMutation = useUpdateKnowledgeBase();
  const deleteKbMutation = useDeleteKnowledgeBase();
  const createDocumentMutation = useCreateDocument();
  const deleteDocumentMutation = useDeleteDocument();
  const downloadDocumentMutation = useDownloadDocument();

  // Determine current view
  const isKbView = !!kbId;
  const isKbListView = !kbId;

  // Generate breadcrumbs
  const breadcrumbs = isKbView
    ? [
        { title: 'Knowledge Base', to: '/knowledge-base' },
        { title: currentKb?.name || 'Loading...', to: null },
      ]
    : [{ title: 'Knowledge Base', to: null }];

  // Handle actions
  const handleCreateKnowledgeBase = async (data: KnowledgeBaseCreate) => {
    try {
      await createKbMutation.mutateAsync({
        ...data,
        project_id: selectedProject.id,
      });
      toast.success('Knowledge base created successfully');
      setCreateKbModalOpen(false);
    } catch (error) {
      toast.error('Failed to create knowledge base');
    }
  };

  const handleEditKnowledgeBase = async (data: KnowledgeBaseUpdate) => {
    if (!selectedKb) return;

    try {
      await updateKbMutation.mutateAsync({
        kbId: selectedKb.id,
        data,
      });
      toast.success('Knowledge base updated successfully');
      setEditKbModalOpen(false);
      setSelectedKb(null);
    } catch (error) {
      toast.error('Failed to update knowledge base');
    }
  };

  const handleUploadDocument = async (data: DocumentCreate) => {
    try {
      await createDocumentMutation.mutateAsync({
        kbId: selectedKbForUpload,
        data,
      });
      toast.success('Document uploaded successfully');
      setUploadModalOpen(false);
      setSelectedKbForUpload('');
    } catch (error) {
      toast.error('Failed to upload document');
    }
  };

  const handleDeleteKnowledgeBase = async () => {
    if (!selectedKb) return;

    try {
      await deleteKbMutation.mutateAsync(selectedKb.id);
      toast.success('Knowledge base deleted successfully');
      setDeleteModalOpen(false);
      setSelectedKb(null);
    } catch (error) {
      toast.error('Failed to delete knowledge base');
    }
  };

  const handleDeleteDocument = async (doc: DocumentListItem) => {
    try {
      await deleteDocumentMutation.mutateAsync({
        kbId: doc.knowledge_base_id,
        docId: doc.id,
      });
      toast.success('Document deleted successfully');
    } catch (error) {
      toast.error('Failed to delete document');
    }
  };

  const handleDownloadDocument = async (doc: DocumentListItem) => {
    try {
      const blob = await downloadDocumentMutation.mutateAsync({
        kbId: doc.knowledge_base_id,
        docId: doc.id,
      });

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = doc.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      toast.error('Failed to download document');
    }
  };

  // Loading state
  const isLoading = projectLoading || kbLoading || documentsLoading;

  return (
    <Container fluid p="md">
      <Paper p="lg" radius="md" withBorder shadow="sm" pos="relative">
        <LoadingOverlay visible={isLoading} />

        <Stack gap="md">
          {/* Breadcrumbs */}
          <BreadcrumbNavigation items={breadcrumbs} />

          {/* Header */}
          <Group justify="space-between" align="center">
            <div>
              <Title order={1} size="h2" c="var(--color-primary)">
                {isKbView ? currentKb?.name : 'Knowledge Base'}
              </Title>
              <Text size="sm" c="dimmed">
                {isKbView
                  ? 'Documents in this knowledge base'
                  : selectedProject
                    ? `Knowledge bases in ${selectedProject.name}`
                    : 'Select a project to view knowledge bases'}
              </Text>
            </div>

            <Group gap="sm">
              {isKbListView && selectedProject && (
                <Button
                  leftSection={<Plus size={16} />}
                  onClick={() => setCreateKbModalOpen(true)}
                  style={{ backgroundColor: 'var(--color-primary)' }}
                >
                  Create Knowledge Base
                </Button>
              )}
              {isKbView && (
                <Button
                  leftSection={<Plus size={16} />}
                  onClick={() => {
                    setSelectedKbForUpload(kbId || '');
                    setUploadModalOpen(true);
                  }}
                  style={{ backgroundColor: 'var(--color-primary)' }}
                >
                  Upload Document
                </Button>
              )}
              {!selectedProject && (
                <Button variant="outline" onClick={() => navigate('/projects')}>
                  Go to Projects
                </Button>
              )}
            </Group>
          </Group>

          {/* Search */}
          <TextInput
            placeholder="Search..."
            leftSection={<Search size={16} />}
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            style={{ maxWidth: 400 }}
          />

          {/* Content Grid */}
          <Grid>
            {!selectedProject ? (
              <Grid.Col span={12}>
                <Paper
                  p="xl"
                  radius="md"
                  withBorder
                  style={{ textAlign: 'center' }}
                >
                  <Stack gap="md" align="center">
                    <Text size="lg" fw={500} c="dimmed">
                      No project selected
                    </Text>
                    <Text size="sm" c="dimmed">
                      Please select a project from the Projects page to view its
                      knowledge bases
                    </Text>
                    <Button
                      variant="outline"
                      onClick={() => navigate('/projects')}
                    >
                      Go to Projects
                    </Button>
                  </Stack>
                </Paper>
              </Grid.Col>
            ) : isKbListView ? (
              <>
                {knowledgeBases?.length === 0 ? (
                  <Grid.Col span={12}>
                    <Paper
                      p="xl"
                      radius="md"
                      withBorder
                      style={{ textAlign: 'center' }}
                    >
                      <Stack gap="md" align="center">
                        <Text size="lg" fw={500} c="dimmed">
                          No knowledge bases found
                        </Text>
                        <Text size="sm" c="dimmed">
                          Create your first knowledge base to organize your
                          documentation
                        </Text>
                        <Button
                          leftSection={<Plus size={16} />}
                          onClick={() => setCreateKbModalOpen(true)}
                          style={{ backgroundColor: 'var(--color-primary)' }}
                        >
                          Create Knowledge Base
                        </Button>
                      </Stack>
                    </Paper>
                  </Grid.Col>
                ) : (
                  knowledgeBases
                    ?.filter(
                      kb =>
                        !searchQuery ||
                        kb.name
                          .toLowerCase()
                          .includes(searchQuery.toLowerCase()) ||
                        (kb.description &&
                          kb.description
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase())),
                    )
                    .map(kb => (
                      <Grid.Col
                        key={kb.id}
                        span={{ base: 12, sm: 6, md: 4, lg: 3 }}
                      >
                        <KnowledgeBaseCard
                          knowledgeBase={kb}
                          onEdit={kbItem => {
                            setSelectedKb(kbItem);
                            setEditKbModalOpen(true);
                          }}
                          onDelete={kbItem => {
                            setSelectedKb(kbItem);
                            setDeleteModalOpen(true);
                          }}
                          onUploadDocument={kbItem => {
                            setSelectedKbForUpload(kbItem.id);
                            setUploadModalOpen(true);
                          }}
                        />
                      </Grid.Col>
                    ))
                )}
              </>
            ) : null}

            {isKbView && (
              <>
                {!documentsData?.items || documentsData.items.length === 0 ? (
                  <Grid.Col span={12}>
                    <Paper
                      p="xl"
                      radius="md"
                      withBorder
                      style={{ textAlign: 'center' }}
                    >
                      <Stack gap="md" align="center">
                        <Text size="lg" fw={500} c="dimmed">
                          No documents found
                        </Text>
                        <Text size="sm" c="dimmed">
                          Upload your first document to start building your
                          knowledge base
                        </Text>
                        <Button
                          leftSection={<Plus size={16} />}
                          onClick={() => {
                            setSelectedKbForUpload(kbId || '');
                            setUploadModalOpen(true);
                          }}
                          style={{ backgroundColor: 'var(--color-primary)' }}
                        >
                          Upload Document
                        </Button>
                      </Stack>
                    </Paper>
                  </Grid.Col>
                ) : (
                  documentsData?.items.map(doc => (
                    <Grid.Col
                      key={doc.id}
                      span={{ base: 12, sm: 6, md: 4, lg: 3 }}
                    >
                      <DocumentCard
                        document={doc}
                        onView={document => {
                          // Handle view document
                          console.log('View document:', document);
                        }}
                        onEdit={document => {
                          // Handle edit document - not implemented yet
                          console.log('Edit document:', document);
                        }}
                        onDelete={document => {
                          handleDeleteDocument(document);
                        }}
                        onDownload={handleDownloadDocument}
                      />
                    </Grid.Col>
                  ))
                )}
              </>
            )}
          </Grid>

          {/* Pagination */}
          {isKbView && documentsData && documentsData.total > pageSize && (
            <Group justify="center">
              <Pagination
                total={Math.ceil(documentsData.total / pageSize)}
                value={currentPage}
                onChange={setCurrentPage}
              />
            </Group>
          )}
        </Stack>

        {/* Modals */}
        <CreateKnowledgeBaseModal
          opened={createKbModalOpen}
          onClose={() => setCreateKbModalOpen(false)}
          onSubmit={handleCreateKnowledgeBase}
          projectId={selectedProject?.id || ''}
          loading={createKbMutation.isPending}
        />

        <EditKnowledgeBaseModal
          opened={editKbModalOpen}
          onClose={() => {
            setEditKbModalOpen(false);
            setSelectedKb(null);
          }}
          onSubmit={handleEditKnowledgeBase}
          knowledgeBase={selectedKb}
          loading={updateKbMutation.isPending}
        />

        <FileUploadModal
          opened={uploadModalOpen}
          onClose={() => {
            setUploadModalOpen(false);
            setSelectedKbForUpload('');
          }}
          onSubmit={handleUploadDocument}
          loading={createDocumentMutation.isPending}
        />

        <Modal
          opened={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setSelectedKb(null);
          }}
          title="Delete Knowledge Base"
          size="sm"
        >
          <Stack gap="md">
            <Text>
              Are you sure you want to delete the knowledge base "
              {selectedKb?.name}"? This action cannot be undone and will also
              delete all associated documents.
            </Text>
            <Group justify="flex-end" gap="sm">
              <Button
                variant="outline"
                onClick={() => {
                  setDeleteModalOpen(false);
                  setSelectedKb(null);
                }}
              >
                Cancel
              </Button>
              <Button
                color="red"
                leftSection={<Trash2 size={16} />}
                onClick={handleDeleteKnowledgeBase}
                loading={deleteKbMutation.isPending}
              >
                Delete Knowledge Base
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Paper>
    </Container>
  );
}

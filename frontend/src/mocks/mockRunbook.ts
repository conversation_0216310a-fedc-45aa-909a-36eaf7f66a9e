import { Runbook, RunbookStep } from '../types/RunbookType';

export const mockRunbookSteps: RunbookStep = {
  step_order: 1,
  id: '1',
  title: 'Stop traffic to payment service',
  description: 'Redirect traffic away from the problematic service version',
  details: 'Stop traffic to payment service',
  notes: 'Service traffic redirected to stable version',
  executed_by_user: {
    id: '1',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    role: 'engineer',
  },
  executed_at: '2025-05-10T10:00:00Z',
  status: 'successful',
  expected_result: 'Service traffic redirected to stable version',
  runbook_id: '1',
  created_at: '2025-05-10T10:00:00Z',
};

export const mockRunbook: Runbook = {
  id: 'rollback-payment-service',
  incident_id: '1',
  title: 'Payment Service Rollback',
  purpose: 'Emergency rollback procedure for payment service deployments',
  type: 'rollback',
  details: 'Emergency rollback procedure for payment service deployments',
  created_at: '2025-05-10T10:00:00Z',
  steps: [mockRunbookSteps],
};

import { Incident, User, AffectedService } from '../types/IncidentType';
import {
  SEVERITY,
  STATUS,
  PRIORITY,
  INCIDENT_TYPE,
  SERVICE_STATUS,
  IMPACT,
} from '../constants/types';

export const mockUser: User = {
  id: 'mock-user-001',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  email: '<EMAIL>',
  avatar: 'https://ui-avatars.com/api/?name=<PERSON>',
  role: 'Engineer',
};

export const mockAffectedService: AffectedService = {
  id: 'mock-service-001',
  name: 'API Gateway',
  status: SERVICE_STATUS.DEGRADED,
  details: [
    'Service experiencing high latency',
    'Intermittent failures observed',
  ],
  impact: IMPACT.HIGH,
  dependencies: ['Database', 'Cache Layer'],
};

export const mockIncident: Incident = {
  id: 'mock-incident-001',
  incident_number: 'INC-001',
  title: 'Service Degradation - API Gateway',
  summary: 'API Gateway experiencing high latency and intermittent failures',
  incident_type: INCIDENT_TYPE.DEGRADATION,
  priority: PRIORITY.P1,
  severity: SEVERITY.HIGH,
  status: STATUS.OPEN,
  reported_at: new Date().toISOString(),
  reporter: mockUser,
  assignedUsers: [mockUser],
  tags: ['api', 'gateway', 'latency', 'production'],
  affectedServices: [mockAffectedService],
  timeline: {
    created: new Date().toISOString(),
    events: [
      {
        id: 'mock-event-001',
        timestamp: new Date().toISOString(),
        author: mockUser,
        type: 'alert',
        content: 'Incident reported and investigation started',
        visibility: 'public',
      },
    ],
    lastUpdated: new Date().toISOString(),
  },
};

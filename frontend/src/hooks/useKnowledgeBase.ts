import {
  useMutation,
  UseMutationResult,
  useQuery,
  useQueryClient,
  UseQueryResult,
} from '@tanstack/react-query';
import {
  createDocument,
  createKnowledgeBase,
  deleteDocument,
  deleteKnowledgeBase,
  downloadDocument,
  getKnowledgeBaseById,
  getKnowledgeBaseDocument,
  getKnowledgeBaseDocuments,
  getKnowledgeBases,
  updateDocument,
  updateKnowledgeBase,
} from '../api/knowledgeBaseApi';
import {
  createProject,
  deleteProject,
  getProjectById,
  getProjects,
  updateProject,
} from '../api/projectsApi';
import {
  Document,
  DocumentCreate,
  DocumentUpdate,
  DocumentUploadResponse,
  KnowledgeBase,
  KnowledgeBaseCreate,
  KnowledgeBaseUpdate,
  PaginatedDocumentResponse,
  PaginatedProjectResponse,
  Project,
  ProjectCreate,
  ProjectUpdate,
} from '../types/KnowledgeBaseTypes';

// Query keys
export const QUERY_KEYS = {
  PROJECTS: 'projects',
  PROJECT: 'project',
  KNOWLEDGE_BASES: 'knowledgeBases',
  KNOWLEDGE_BASE: 'knowledgeBase',
  DOCUMENTS: 'documents',
  DOCUMENT: 'document',
} as const;

// Project hooks
export const useProjects = (
  offset: number = 0,
  limit: number = 10,
): UseQueryResult<PaginatedProjectResponse, Error> => {
  return useQuery({
    queryKey: [QUERY_KEYS.PROJECTS, offset, limit],
    queryFn: () => getProjects(offset, limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProject = (
  projectId: string,
  options?: { enabled?: boolean },
): UseQueryResult<Project, Error> => {
  return useQuery({
    queryKey: [QUERY_KEYS.PROJECT, projectId],
    queryFn: () => getProjectById(projectId),
    enabled: !!projectId && options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateProject = (): UseMutationResult<
  Project,
  Error,
  ProjectCreate,
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROJECTS] });
    },
  });
};

export const useUpdateProject = (): UseMutationResult<
  Project,
  Error,
  { projectId: string; data: ProjectUpdate },
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ projectId, data }) => updateProject(projectId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROJECTS] });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PROJECT, variables.projectId],
      });
    },
  });
};

export const useDeleteProject = (): UseMutationResult<
  void,
  Error,
  string,
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteProject,
    onSuccess: (_, projectId) => {
      // Invalidate projects list
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROJECTS] });
      // Remove all project-related queries (this will cascade to remove nested queries)
      queryClient.removeQueries({ queryKey: [QUERY_KEYS.PROJECT, projectId] });
    },
  });
};

// Knowledge Base hooks
export const useKnowledgeBases = (
  projectId: string,
): UseQueryResult<KnowledgeBase[], Error> => {
  return useQuery({
    queryKey: [QUERY_KEYS.PROJECT, projectId, QUERY_KEYS.KNOWLEDGE_BASES],
    queryFn: () => getKnowledgeBases(projectId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useKnowledgeBase = (
  kbId: string,
  options?: { enabled?: boolean },
): UseQueryResult<KnowledgeBase, Error> => {
  return useQuery({
    queryKey: [QUERY_KEYS.KNOWLEDGE_BASE, kbId],
    queryFn: () => getKnowledgeBaseById(kbId),
    enabled: !!kbId && options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateKnowledgeBase = (): UseMutationResult<
  KnowledgeBase,
  Error,
  KnowledgeBaseCreate,
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createKnowledgeBase,
    onSuccess: data => {
      queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.PROJECT,
          data.project_id,
          QUERY_KEYS.KNOWLEDGE_BASES,
        ],
      });
    },
  });
};

export const useUpdateKnowledgeBase = (): UseMutationResult<
  KnowledgeBase,
  Error,
  { kbId: string; data: KnowledgeBaseUpdate },
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ kbId, data }) => updateKnowledgeBase(kbId, data),
    onSuccess: (data, variables) => {
      // Invalidate knowledge bases list for the project
      queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.PROJECT,
          data.project_id,
          QUERY_KEYS.KNOWLEDGE_BASES,
        ],
      });
      // Invalidate the specific knowledge base
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASE, variables.kbId],
      });
    },
  });
};

export const useDeleteKnowledgeBase = (): UseMutationResult<
  void,
  Error,
  string,
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteKnowledgeBase,
    onSuccess: (_, kbId) => {
      // Need to get the knowledge base data before deletion to know which project to invalidate
      // Since we can't get it after deletion, we invalidate broadly
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PROJECT],
        predicate: query => query.queryKey.includes(QUERY_KEYS.KNOWLEDGE_BASES),
      });
      // Remove the specific knowledge base from cache
      queryClient.removeQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASE, kbId],
      });
      // Remove all documents associated with this knowledge base
      queryClient.removeQueries({
        queryKey: [QUERY_KEYS.DOCUMENTS, kbId],
      });
      // Remove individual document queries for this KB (using hierarchical structure)
      queryClient.removeQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASE, kbId, QUERY_KEYS.DOCUMENT],
      });
    },
  });
};

// Document hooks
export const useDocuments = (
  kbId: string,
  offset: number = 0,
  limit: number = 10,
  options?: { enabled?: boolean },
): UseQueryResult<PaginatedDocumentResponse, Error> => {
  return useQuery({
    queryKey: [QUERY_KEYS.DOCUMENTS, kbId, offset, limit],
    queryFn: () => getKnowledgeBaseDocuments(kbId, offset, limit),
    enabled: !!kbId && options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useDocument = (
  kbId: string,
  docId: string,
): UseQueryResult<Document, Error> => {
  return useQuery({
    queryKey: [QUERY_KEYS.KNOWLEDGE_BASE, kbId, QUERY_KEYS.DOCUMENT, docId],
    queryFn: () => getKnowledgeBaseDocument(kbId, docId),
    enabled: !!kbId && !!docId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateDocument = (): UseMutationResult<
  DocumentUploadResponse,
  Error,
  { kbId: string; data: DocumentCreate },
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ kbId, data }) => createDocument(kbId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DOCUMENTS, variables.kbId],
      });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASE, variables.kbId],
      });
    },
  });
};

export const useUpdateDocument = (): UseMutationResult<
  Document,
  Error,
  { kbId: string; docId: string; data: DocumentUpdate },
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ kbId, docId, data }) => updateDocument(kbId, docId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DOCUMENTS, variables.kbId],
      });
      queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.KNOWLEDGE_BASE,
          variables.kbId,
          QUERY_KEYS.DOCUMENT,
          variables.docId,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASE, variables.kbId],
      });
    },
  });
};

export const useDeleteDocument = (): UseMutationResult<
  void,
  Error,
  { kbId: string; docId: string },
  unknown
> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ kbId, docId }) => deleteDocument(kbId, docId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DOCUMENTS, variables.kbId],
      });
      queryClient.removeQueries({
        queryKey: [
          QUERY_KEYS.KNOWLEDGE_BASE,
          variables.kbId,
          QUERY_KEYS.DOCUMENT,
          variables.docId,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASE, variables.kbId],
      });
    },
  });
};

export const useDownloadDocument = (): UseMutationResult<
  Blob,
  Error,
  { kbId: string; docId: string },
  unknown
> => {
  return useMutation({
    mutationFn: ({ kbId, docId }) => downloadDocument(kbId, docId),
  });
};

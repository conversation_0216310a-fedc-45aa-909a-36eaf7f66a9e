import { useState, useEffect } from 'react';
import { Incident } from '../types/IncidentType';
import { queryIncidentById } from './useApi';

interface UseIncidentReturn {
  incident: Incident | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
  updateIncident: (updates: Partial<Incident>) => void;
}

export const useIncident = (incidentId: string): UseIncidentReturn => {
  const { data, isLoading, error, refetch } = queryIncidentById(incidentId);
  const [incident, setIncident] = useState<Incident | null>(null);

  useEffect(() => {
    setIncident(data ?? null);
  }, [data]);

  const updateIncident = (updates: Partial<Incident>) => {
    if (incident) {
      setIncident({ ...incident, ...updates });
    }
  };

  return {
    incident,
    loading: isLoading,
    error: error?.message ?? null,
    refetch,
    updateIncident,
  };
};

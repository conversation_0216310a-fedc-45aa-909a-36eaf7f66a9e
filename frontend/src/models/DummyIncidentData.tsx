import { Incident } from '../types/IncidentType';
import { INCIDENT_TYPE } from '../constants/types';

// Sample incident data
export const incidentData: Incident[] = [
  {
    id: '11111111-1111-1111-1111-111111111111',
    incident_number: 'INC-001',
    title: 'Service Outage - API Gateway',
    summary:
      'External API endpoints unreachable, affecting client applications. The main API gateway is experiencing intermittent failures causing timeout errors for all external requests.',
    incident_type: INCIDENT_TYPE.OUTAGE,
    severity: 'high',
    priority: 'p1',
    status: 'open',
    reported_at: '2025-05-25T03:55:00.000Z',
    reporter: {
      id: 'user-001',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-1.png',
      role: 'DevOps Engineer',
    },
    assignedUsers: [
      {
        id: 'user-002',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-4.png',
        role: 'Senior Developer',
      },
      {
        id: 'user-003',

        first_name: 'Michael',
        last_name: 'Chen',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-5.png',
        role: 'Platform Engineer',
      },
    ],
    tags: ['api-gateway', 'outage', 'timeout', 'external-services'],
    affectedServices: [
      {
        id: 'service-001',
        name: 'API Gateway',
        status: 'Down',
        details: [
          'All external API endpoints unreachable',
          'Timeout errors after 30 seconds',
          'Internal health checks failing',
        ],
        impact: 'High',
        dependencies: ['Load Balancer', 'Authentication Service'],
      },
      {
        id: 'service-002',
        name: 'External Integrations',
        status: 'Degraded',
        details: [
          'Payment processing delayed',
          'Third-party notifications failing',
        ],
        impact: 'Medium',
        dependencies: ['API Gateway'],
      },
    ],
    timeline: {
      created: '2025-05-25T03:55:00.000Z',
      lastUpdated: '2025-05-25T05:30:00.000Z',
      events: [
        {
          id: 'update-001',
          timestamp: '2025-05-25T03:55:00.000Z',
          author: {
            id: 'user-001',
            first_name: 'Robert',
            last_name: 'Wolfkisser',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-1.png',
            role: 'DevOps Engineer',
          },
          type: 'status_change',
          content: 'Incident created - API Gateway outage detected',
          visibility: 'internal',
        },
        {
          id: 'update-002',
          timestamp: '2025-05-25T04:15:00.000Z',
          author: {
            id: 'user-002',
            first_name: 'Sarah',
            last_name: 'Johnson',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-4.png',
            role: 'Senior Developer',
          },
          type: 'comment',
          content:
            'Investigating load balancer configuration. Initial checks show high CPU usage on gateway instances.',
          visibility: 'internal',
        },
      ],
    },
  },
  {
    id: '22222222-2222-2222-2222-222222222222',
    incident_number: 'INC-002',
    title: 'Database Connection Failure',
    summary:
      'Primary database cluster experiencing connection timeouts and performance degradation affecting all application services.',
    incident_type: INCIDENT_TYPE.OUTAGE,
    severity: 'critical',
    priority: 'p0',
    status: 'active',
    reported_at: '2025-05-25T04:10:00.000Z',
    reporter: {
      id: 'user-004',
      first_name: 'Jill',
      last_name: 'Jailbreaker',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-7.png',
      role: 'Database Administrator',
    },
    assignedUsers: [
      {
        id: 'user-004',
        first_name: 'Jill',
        last_name: 'Jailbreaker',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-7.png',
        role: 'Database Administrator',
      },
      {
        id: 'user-005',
        first_name: 'David',
        last_name: 'Brown',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-8.png',
        role: 'Infrastructure Engineer',
      },
    ],
    tags: ['database', 'connection-timeout', 'performance', 'critical'],
    affectedServices: [
      {
        id: 'service-003',
        name: 'Primary Database Cluster',
        status: 'Degraded',
        details: [
          'Connection pool exhausted',
          'Query timeouts exceeding 30s',
          'Memory usage at 95%',
        ],
        impact: 'High',
        dependencies: ['Storage Array', 'Network Infrastructure'],
      },
      {
        id: 'service-004',
        name: 'User Service',
        status: 'Degraded',
        details: ['Unable to fetch user profiles', 'Authentication delays'],
        impact: 'High',
        dependencies: ['Primary Database Cluster'],
      },
      {
        id: 'service-005',
        name: 'Order Processing',
        status: 'Down',
        details: ['Cannot process new orders', 'Order status updates failing'],
        impact: 'High',
        dependencies: ['Primary Database Cluster'],
      },
    ],
    timeline: {
      created: '2025-05-25T04:10:00.000Z',
      lastUpdated: '2025-05-25T05:45:00.000Z',
      events: [
        {
          id: 'update-003',
          timestamp: '2025-05-25T04:10:00.000Z',
          author: {
            id: 'user-004',
            first_name: 'Jill',
            last_name: 'Jailbreaker',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-7.png',
            role: 'Database Administrator',
          },
          type: 'status_change',
          content:
            'Critical database incident - Connection failures detected across all clusters',
          visibility: 'internal',
        },
        {
          id: 'update-004',
          timestamp: '2025-05-25T04:25:00.000Z',
          author: {
            id: 'user-005',
            first_name: 'David',
            last_name: 'Brown',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-8.png',
            role: 'Infrastructure Engineer',
          },
          type: 'comment',
          content:
            'Scaling up database instances and optimizing connection pool settings. ETA for resolution: 30 minutes.',
          visibility: 'public',
        },
      ],
    },
  },
  {
    id: '33333333-3333-3333-3333-333333333333',
    incident_number: 'INC-003',
    title: 'Authentication Service Degradation',
    summary:
      'Increased latency in auth service affecting login times and user session management across all platforms.',
    incident_type: INCIDENT_TYPE.DEGRADATION,
    severity: 'medium',
    priority: 'p2',
    status: 'active',
    reported_at: '2025-05-24T05:55:00.000Z',
    reporter: {
      id: 'user-006',
      first_name: 'Henry',
      last_name: 'Silkeater',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png',
      role: 'Security Engineer',
    },
    assignedUsers: [
      {
        id: 'user-006',
        first_name: 'Henry',
        last_name: 'Silkeater',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png',
        role: 'Security Engineer',
      },
    ],
    tags: ['authentication', 'latency', 'performance', 'login'],
    affectedServices: [
      {
        id: 'service-006',
        name: 'Authentication Service',
        status: 'Degraded',
        details: [
          'Login response time increased to 5-8 seconds',
          'Token validation slow',
          'Session management delays',
        ],
        impact: 'Medium',
        dependencies: ['Redis Cache', 'User Database'],
      },
      {
        id: 'service-007',
        name: 'Web Application',
        status: 'Degraded',
        details: [
          'Slow page loads after authentication',
          'User experience degraded',
        ],
        impact: 'Low',
        dependencies: ['Authentication Service'],
      },
    ],
    timeline: {
      created: '2025-05-24T05:55:00.000Z',
      lastUpdated: '2025-05-25T02:30:00.000Z',
      events: [
        {
          id: 'update-005',
          timestamp: '2025-05-24T05:55:00.000Z',
          author: {
            id: 'user-006',
            first_name: 'Henry',
            last_name: 'Silkeater',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png',
            role: 'Security Engineer',
          },
          type: 'status_change',
          content: 'Authentication performance degradation detected',
          visibility: 'internal',
        },
        {
          id: 'update-006',
          timestamp: '2025-05-25T02:30:00.000Z',
          author: {
            id: 'user-006',
            first_name: 'Henry',
            last_name: 'Silkeater',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png',
            role: 'Security Engineer',
          },
          type: 'comment',
          content:
            'Optimizing Redis cache configuration and implementing connection pooling improvements.',
          visibility: 'internal',
        },
      ],
    },
  },
  {
    id: '44444444-4444-4444-4444-444444444444',
    incident_number: 'INC-004',
    title: 'UI Rendering Issue - Dashboard',
    summary:
      'Charts not displaying correctly in analytics dashboard, affecting data visualization and reporting capabilities.',
    incident_type: INCIDENT_TYPE.OTHER,
    severity: 'low',
    priority: 'p3',
    status: 'resolved',
    reported_at: '2025-05-23T05:55:00.000Z',
    reporter: {
      id: 'user-007',
      first_name: 'Bill',
      last_name: 'Horsefighter',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-3.png',
      role: 'Frontend Developer',
    },
    assignedUsers: [
      {
        id: 'user-007',
        first_name: 'Bill',
        last_name: 'Horsefighter',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-3.png',
        role: 'Frontend Developer',
      },
    ],
    tags: ['ui', 'dashboard', 'charts', 'frontend'],
    affectedServices: [
      {
        id: 'service-008',
        name: 'Analytics Dashboard',
        status: 'Operational',
        details: ['Chart rendering fixed', 'Data visualization restored'],
        impact: 'Low',
        dependencies: ['Chart Library', 'Data API'],
      },
    ],
    timeline: {
      created: '2025-05-23T05:55:00.000Z',
      lastUpdated: '2025-05-23T08:30:00.000Z',
      events: [
        {
          id: 'update-007',
          timestamp: '2025-05-23T05:55:00.000Z',
          author: {
            id: 'user-007',
            first_name: 'Bill',
            last_name: 'Horsefighter',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-3.png',
            role: 'Frontend Developer',
          },
          type: 'status_change',
          content: 'Dashboard charts not rendering properly',
          visibility: 'internal',
        },
      ],
    },
    resolution: {
      summary: 'Updated chart library dependency and fixed CSS conflicts',
      rootCause:
        'Outdated chart library version causing rendering conflicts with new CSS framework',
      preventiveMeasures: [
        'Regular dependency updates',
        'Enhanced UI testing',
        'Visual regression testing',
      ],
      resolvedAt: '2025-05-23T08:30:00.000Z',
      resolvedBy: {
        id: 'user-007',
        first_name: 'Bill',
        last_name: 'Horsefighter',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-3.png',
        role: 'Frontend Developer',
      },
    },
  },
  {
    id: '55555555-5555-5555-5555-555555555555',
    incident_number: 'INC-005',
    title: 'Memory Leak - Backend Service',
    summary:
      'Gradual memory growth observed in user service causing performance degradation and eventual service crashes.',
    incident_type: INCIDENT_TYPE.PERFORMANCE,
    severity: 'high',
    priority: 'p1',
    status: 'closed',
    reported_at: '2025-05-22T05:55:00.000Z',
    reporter: {
      id: 'user-008',
      first_name: 'Jeremy',
      last_name: 'Footviewer',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-10.png',
      role: 'Backend Developer',
    },
    assignedUsers: [
      {
        id: 'user-008',
        first_name: 'Jeremy',
        last_name: 'Footviewer',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-10.png',
        role: 'Backend Developer',
      },
    ],
    tags: ['memory-leak', 'performance', 'backend', 'user-service'],
    affectedServices: [
      {
        id: 'service-009',
        name: 'User Service',
        status: 'Operational',
        details: [
          'Memory usage optimized',
          'Performance restored to normal levels',
        ],
        impact: 'Low',
        dependencies: ['Database', 'Cache Layer'],
      },
    ],
    timeline: {
      created: '2025-05-22T05:55:00.000Z',
      lastUpdated: '2025-05-22T14:20:00.000Z',
      events: [
        {
          id: 'update-008',
          timestamp: '2025-05-22T05:55:00.000Z',
          author: {
            id: 'user-008',
            first_name: 'Jeremy',
            last_name: 'Footviewer',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-10.png',
            role: 'Backend Developer',
          },
          type: 'status_change',
          content: 'Memory leak detected in user service',
          visibility: 'internal',
        },
      ],
    },
    resolution: {
      summary:
        'Fixed memory leak in user session management and optimized garbage collection',
      rootCause:
        'Improper cleanup of user session objects causing memory accumulation',
      preventiveMeasures: [
        'Memory profiling in CI/CD',
        'Regular performance monitoring',
        'Code review for resource management',
      ],
      resolvedAt: '2025-05-22T14:20:00.000Z',
      resolvedBy: {
        id: 'user-008',
        first_name: 'Jeremy',
        last_name: 'Footviewer',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-10.png',
        role: 'Backend Developer',
      },
    },
  },
  {
    id: '66666666-6666-6666-6666-666666666666',
    incident_number: 'INC-006',
    title: 'SSL Certificate Expiration',
    summary:
      'Production SSL certificate expires in less than 24 hours, threatening secure connections for all services.',
    incident_type: INCIDENT_TYPE.SECURITY,
    severity: 'critical',
    priority: 'p0',
    status: 'open',
    reported_at: '2025-05-25T05:25:00.000Z',
    reporter: {
      id: 'user-002',
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-4.png',
      role: 'Senior Developer',
    },
    assignedUsers: [
      {
        id: 'user-002',
        first_name: 'Sarah',
        last_name: 'Johnson',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-4.png',
        role: 'Senior Developer',
      },
      {
        id: 'user-005',
        first_name: 'David',
        last_name: 'Brown',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-8.png',
        role: 'Infrastructure Engineer',
      },
    ],
    tags: ['ssl', 'certificate', 'security', 'urgent'],
    affectedServices: [
      {
        id: 'service-010',
        name: 'Web Application',
        status: 'Under Maintenance',
        details: [
          'SSL certificate renewal in progress',
          'Secure connections may be affected',
        ],
        impact: 'High',
        dependencies: ['Load Balancer', 'CDN'],
      },
      {
        id: 'service-011',
        name: 'API Services',
        status: 'Under Maintenance',
        details: [
          'Certificate update scheduled',
          'API endpoints may show security warnings',
        ],
        impact: 'High',
        dependencies: ['Certificate Authority', 'DNS'],
      },
    ],
    timeline: {
      created: '2025-05-25T05:25:00.000Z',
      lastUpdated: '2025-05-25T05:25:00.000Z',
      events: [
        {
          id: 'update-009',
          timestamp: '2025-05-25T05:25:00.000Z',
          author: {
            id: 'user-002',
            first_name: 'Sarah',
            last_name: 'Johnson',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-4.png',
            role: 'Senior Developer',
          },
          type: 'status_change',
          content: 'Critical: SSL certificate expiring in less than 24 hours',
          visibility: 'internal',
        },
      ],
    },
    escalation: {
      level: 3,
      escalatedAt: '2025-05-25T05:25:00.000Z',
      escalatedBy: {
        id: 'user-002',
        first_name: 'Sarah',
        last_name: 'Johnson',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-4.png',
        role: 'Senior Developer',
      },
      reason:
        'Critical security incident requiring immediate attention from leadership',
    },
  },
  {
    id: '77777777-7777-7777-7777-777777777777',
    incident_number: 'INC-007',
    title: 'Network Connectivity Issues',
    summary:
      'Intermittent packet loss between data centers causing service disruptions and data synchronization delays.',
    incident_type: INCIDENT_TYPE.OUTAGE,
    severity: 'high',
    priority: 'p1',
    status: 'active',
    reported_at: '2025-05-25T04:55:00.000Z',
    reporter: {
      id: 'user-003',
      first_name: 'Michael',
      last_name: 'Chen',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-5.png',
      role: 'Platform Engineer',
    },
    assignedUsers: [
      {
        id: 'user-003',
        first_name: 'Michael',
        last_name: 'Chen',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-5.png',
        role: 'Platform Engineer',
      },
      {
        id: 'user-005',
        first_name: 'David',
        last_name: 'Brown',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-8.png',
        role: 'Infrastructure Engineer',
      },
    ],
    tags: ['network', 'connectivity', 'packet-loss', 'data-centers'],
    affectedServices: [
      {
        id: 'service-012',
        name: 'Data Replication Service',
        status: 'Degraded',
        details: ['Cross-datacenter sync delayed', 'Backup processes affected'],
        impact: 'Medium',
        dependencies: ['Network Infrastructure', 'Storage Systems'],
      },
      {
        id: 'service-013',
        name: 'Load Balancer',
        status: 'Degraded',
        details: [
          'Traffic routing inconsistent',
          'Failover mechanisms triggered',
        ],
        impact: 'High',
        dependencies: ['Network Infrastructure'],
      },
    ],
    timeline: {
      created: '2025-05-25T04:55:00.000Z',
      lastUpdated: '2025-05-25T05:30:00.000Z',
      events: [
        {
          id: 'update-010',
          timestamp: '2025-05-25T04:55:00.000Z',
          author: {
            id: 'user-003',
            first_name: 'Michael',
            last_name: 'Chen',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-5.png',
            role: 'Platform Engineer',
          },
          type: 'status_change',
          content:
            'Network connectivity issues detected between primary and backup data centers',
          visibility: 'internal',
        },
      ],
    },
  },
  {
    id: '********-8888-8888-8888-********8888',
    incident_number: 'INC-008',
    title: 'Disk Space Exhaustion - Primary Storage',
    summary:
      'Primary storage disk space is 95% full, threatening system stability and data integrity.',
    incident_type: INCIDENT_TYPE.OTHER,
    severity: 'medium',
    priority: 'p2',
    status: 'resolved',
    reported_at: '2025-05-25T03:55:00.000Z',
    reporter: {
      id: 'user-009',
      first_name: 'Emily',
      last_name: 'Davis',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-6.png',
      role: 'Systems Administrator',
    },
    assignedUsers: [
      {
        id: 'user-009',
        first_name: 'Emily',
        last_name: 'Davis',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-6.png',
        role: 'Systems Administrator',
      },
    ],
    tags: ['storage', 'disk-space', 'capacity', 'maintenance'],
    affectedServices: [
      {
        id: 'service-014',
        name: 'Primary Storage Array',
        status: 'Operational',
        details: ['Disk space optimized', 'Storage capacity increased'],
        impact: 'Low',
        dependencies: ['File System', 'Backup Services'],
      },
    ],
    timeline: {
      created: '2025-05-25T03:55:00.000Z',
      lastUpdated: '2025-05-25T05:15:00.000Z',
      events: [
        {
          id: 'update-011',
          timestamp: '2025-05-25T03:55:00.000Z',
          author: {
            id: 'user-009',
            first_name: 'Emily',
            last_name: 'Davis',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-6.png',
            role: 'Systems Administrator',
          },
          type: 'status_change',
          content:
            'Primary storage reaching capacity limits - immediate action required',
          visibility: 'internal',
        },
      ],
    },
    resolution: {
      summary: 'Cleaned up old log files and expanded storage capacity',
      rootCause:
        'Insufficient log rotation and lack of automated cleanup processes',
      preventiveMeasures: [
        'Automated log rotation',
        'Storage monitoring alerts',
        'Regular capacity planning',
      ],
      resolvedAt: '2025-05-25T05:15:00.000Z',
      resolvedBy: {
        id: 'user-009',
        first_name: 'Emily',
        last_name: 'Davis',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-6.png',
        role: 'Systems Administrator',
      },
    },
  },
  {
    id: '99999999-9999-9999-9999-999999999999',
    incident_number: 'INC-009',
    title: 'Database Query Timeout',
    summary:
      'Long running queries causing timeouts in user service, affecting user data retrieval and authentication processes.',
    incident_type: INCIDENT_TYPE.PERFORMANCE,
    severity: 'low',
    priority: 'p3',
    status: 'closed',
    reported_at: '2025-05-21T05:55:00.000Z',
    reporter: {
      id: 'user-005',
      first_name: 'David',
      last_name: 'Brown',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-8.png',
      role: 'Infrastructure Engineer',
    },
    assignedUsers: [
      {
        id: 'user-005',
        first_name: 'David',
        last_name: 'Brown',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-8.png',
        role: 'Infrastructure Engineer',
      },
      {
        id: 'user-004',
        first_name: 'Jill',
        last_name: 'Jailbreaker',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-7.png',
        role: 'Database Administrator',
      },
    ],
    tags: ['database', 'query-timeout', 'performance', 'optimization'],
    affectedServices: [
      {
        id: 'service-015',
        name: 'User Database',
        status: 'Operational',
        details: ['Query performance optimized', 'Indexes updated'],
        impact: 'Low',
        dependencies: ['Database Engine', 'Query Optimizer'],
      },
    ],
    timeline: {
      created: '2025-05-21T05:55:00.000Z',
      lastUpdated: '2025-05-21T12:30:00.000Z',
      events: [
        {
          id: 'update-012',
          timestamp: '2025-05-21T05:55:00.000Z',
          author: {
            id: 'user-005',
            first_name: 'David',
            last_name: 'Brown',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-8.png',
            role: 'Infrastructure Engineer',
          },
          type: 'status_change',
          content: 'Database query timeouts affecting user service performance',
          visibility: 'internal',
        },
      ],
    },
    resolution: {
      summary: 'Optimized database queries and added missing indexes',
      rootCause:
        'Inefficient query patterns and missing database indexes causing slow performance',
      preventiveMeasures: [
        'Query performance monitoring',
        'Regular index optimization',
        'Database performance reviews',
      ],
      resolvedAt: '2025-05-21T12:30:00.000Z',
      resolvedBy: {
        id: 'user-004',
        first_name: 'Jill',
        last_name: 'Jailbreaker',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-7.png',
        role: 'Database Administrator',
      },
    },
  },
  {
    id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
    incident_number: 'INC-010',
    title: 'API Rate Limiting',
    summary:
      'API rate limiting causing 503 errors for clients, impacting external integrations and third-party services.',
    incident_type: INCIDENT_TYPE.DEGRADATION,
    severity: 'medium',
    priority: 'p2',
    status: 'active',
    reported_at: '2025-05-24T05:55:00.000Z',
    reporter: {
      id: 'user-010',
      first_name: 'Jessica',
      last_name: 'White',
      email: '<EMAIL>',
      avatar:
        'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-9.png',
      role: 'API Developer',
    },
    assignedUsers: [
      {
        id: 'user-010',
        first_name: 'Jessica',
        last_name: 'White',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-9.png',
        role: 'API Developer',
      },
      {
        id: 'user-002',
        first_name: 'Sarah',
        last_name: 'Johnson',
        email: '<EMAIL>',
        avatar:
          'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-4.png',
        role: 'Senior Developer',
      },
    ],
    tags: ['api', 'rate-limiting', 'external-integrations', '503-errors'],
    affectedServices: [
      {
        id: 'service-016',
        name: 'API Gateway',
        status: 'Degraded',
        details: [
          'Rate limits being adjusted',
          'Client request patterns analyzed',
        ],
        impact: 'Medium',
        dependencies: ['Rate Limiter', 'Load Balancer'],
      },
      {
        id: 'service-017',
        name: 'External Integrations',
        status: 'Degraded',
        details: [
          'Third-party services experiencing delays',
          'Webhook deliveries affected',
        ],
        impact: 'Medium',
        dependencies: ['API Gateway'],
      },
    ],
    timeline: {
      created: '2025-05-24T05:55:00.000Z',
      lastUpdated: '2025-05-25T03:15:00.000Z',
      events: [
        {
          id: 'update-013',
          timestamp: '2025-05-24T05:55:00.000Z',
          author: {
            id: 'user-010',
            first_name: 'Jessica',
            last_name: 'White',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-9.png',
            role: 'API Developer',
          },
          type: 'status_change',
          content: 'API rate limiting causing 503 errors for external clients',
          visibility: 'public',
        },
        {
          id: 'update-014',
          timestamp: '2025-05-25T03:15:00.000Z',
          author: {
            id: 'user-002',
            first_name: 'Sarah',
            last_name: 'Johnson',
            email: '<EMAIL>',
            avatar:
              'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-4.png',
            role: 'Senior Developer',
          },
          type: 'comment',
          content:
            'Analyzing traffic patterns and adjusting rate limit configurations. ETA: 2 hours.',
          visibility: 'public',
        },
      ],
    },
  },
];

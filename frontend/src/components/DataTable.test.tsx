import { render } from '../utils/test-utils';
import { DataTable } from './DataTable';

describe('DataTable component', () => {
  const mockData = [
    { id: '1', name: '<PERSON>', email: '<EMAIL>' },
    { id: '2', name: '<PERSON>', email: '<EMAIL>' },
    { id: '3', name: '<PERSON>', email: '<EMAIL>' },
  ];

  const columns = [
    { key: 'name', header: 'Name' },
    { key: 'email', header: 'Email' },
  ];

  it('renders without crashing with minimal Mantine context', () => {
    expect(() =>
      render(
        <DataTable
          data={mockData}
          columns={columns}
          keyExtractor={item => item.id}
        />,
      ),
    ).not.toThrow();
  });

  it('renders basic structure correctly', () => {
    expect(() =>
      render(
        <DataTable
          data={mockData}
          columns={columns}
          keyExtractor={item => item.id}
        />,
      ),
    ).not.toThrow();
  });

  it('handles pagination props without error', () => {
    const handlePageChange = jest.fn();

    expect(() =>
      render(
        <DataTable
          data={mockData}
          columns={columns}
          keyExtractor={item => item.id}
          currentPage={1}
          remainingPages={3}
          onPageChange={handlePageChange}
        />,
      ),
    ).not.toThrow();
  });

  it('handles row click handler without error', () => {
    const handleRowClick = jest.fn();

    expect(() =>
      render(
        <DataTable
          data={mockData}
          columns={columns}
          keyExtractor={item => item.id}
          onRowClick={handleRowClick}
        />,
      ),
    ).not.toThrow();
  });

  it('handles different itemsPerPage values', () => {
    const smallData = mockData.slice(0, 1);

    expect(() =>
      render(
        <DataTable
          data={smallData}
          columns={columns}
          keyExtractor={item => item.id}
          itemsPerPage={3}
        />,
      ),
    ).not.toThrow();
  });
});

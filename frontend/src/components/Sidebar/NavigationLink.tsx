import { Link } from 'react-router';

interface NavigationLinkProps {
  to: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  isActive: boolean;
  variant?: 'default' | 'projects' | 'footer';
}

export function NavigationLink({
  to,
  icon: Icon,
  label,
  isActive,
  variant = 'default',
}: NavigationLinkProps) {
  const baseStyles =
    'flex items-center gap-3 text-sm px-3 py-2 rounded-md font-medium transition-colors';

  const variantStyles = {
    projects: {
      container: `${baseStyles} bg-white text-primary`,
      icon: 'w-5 h-5 text-gray-500',
      label: 'text-primary',
    },
    footer: {
      container: `${baseStyles} hover:bg-white/10`,
      icon: 'w-5 h-5 text-white',
      label: 'text-white',
    },
    default: {
      container: isActive
        ? `${baseStyles} bg-white`
        : `${baseStyles} hover:bg-white/10`,
      icon: isActive ? 'w-5 h-5 text-primary' : 'w-5 h-5 text-white',
      label: isActive ? 'text-primary' : 'text-white',
    },
  };

  const styles = variantStyles[variant];

  return (
    <Link to={to} className={styles.container}>
      <Icon className={styles.icon} />
      <span className={styles.label}>{label}</span>
    </Link>
  );
}

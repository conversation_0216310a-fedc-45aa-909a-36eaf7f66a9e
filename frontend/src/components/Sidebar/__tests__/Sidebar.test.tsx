import { render, screen } from '../../../utils/test-utils';
import { Sidebar } from '..';

describe('Sidebar component', () => {
  it('renders navigation links', () => {
    render(<Sidebar />);

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Incidents')).toBeInTheDocument();
    expect(screen.getByText('Logs')).toBeInTheDocument();
    expect(screen.getByText('Users')).toBeInTheDocument();
  });

  it('displays the logo', () => {
    render(<Sidebar />);

    expect(screen.getByAltText('Abilytics')).toBeInTheDocument();
  });
});

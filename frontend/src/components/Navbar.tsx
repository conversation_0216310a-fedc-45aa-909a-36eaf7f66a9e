import { <PERSON><PERSON>, Loader, Menu } from '@mantine/core';
import { BellDot, FolderDot, LogOut, UserIcon } from 'lucide-react';
import { useNavigate } from 'react-router';
import { useAuth } from '../contexts/AuthContext';

export function Navbar() {
  const { logout, user, isLoading } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
  };

  if (isLoading) {
    return (
      <div className="absolute top-0 right-0 w-full h-[70px] flex justify-end items-center px-10 z-10 bg-white border-b border-gray-200">
        <Loader size="sm" data-testid="loading" />
      </div>
    );
  }

  const fullName = user
    ? `${user.first_name} ${user.last_name}`
    : 'Unknown User';

  return (
    <div className="absolute top-0 right-0 w-full h-[70px] flex justify-end items-center px-10 z-10 bg-white border-b border-gray-200">
      <div className="flex items-center gap-4">
        <BellDot size={20} className="cursor-pointer" />
        <div className="h-full w-[1px] bg-gray-300" />
        <Menu shadow="md" width={200} position="bottom-end">
          <Menu.Target>
            <div className="flex items-center gap-2 cursor-pointer">
              <Avatar
                size={26}
                src="https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-9.png"
                radius={26}
              />
              <span>{fullName}</span>
            </div>
          </Menu.Target>

          <Menu.Dropdown>
            <Menu.Label>Account</Menu.Label>
            <Menu.Item
              leftSection={<FolderDot size={14} />}
              onClick={() => navigate('/projects')}
            >
              Manage Projects
            </Menu.Item>
            <Menu.Item
              leftSection={<UserIcon size={14} />}
              onClick={() => navigate('/profile')}
            >
              Profile
            </Menu.Item>
            <Menu.Divider />
            <Menu.Item
              leftSection={<LogOut size={14} />}
              onClick={handleLogout}
              color="red"
            >
              Logout
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </div>
    </div>
  );
}

import { Table, Button, Center, Text } from '@mantine/core';
import { Fragment, ReactNode, useState, useEffect, useRef } from 'react';

export interface Column<T> {
  key: string;
  header: string;
  render?: (item: T) => ReactNode;
  width?: string;
  expandedContent?: (item: T) => ReactNode;
}

interface InfiniteDataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick?: (item: T) => void;
  emptyTableHeight?: number;
  keyExtractor: (item: T) => string;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => void;
  isLoading?: boolean;
}

export function InfiniteDataTable<T>({
  data,
  columns,
  onRowClick,
  emptyTableHeight = 60,
  keyExtractor,
  hasNextPage = false,
  isFetchingNextPage = false,
  fetchNextPage,
  isLoading = false,
}: InfiniteDataTableProps<T>) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const toggleRowExpansion = (rowKey: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(rowKey)) {
      newExpandedRows.delete(rowKey);
    } else {
      newExpandedRows.add(rowKey);
    }
    setExpandedRows(newExpandedRows);
  };

  // Intersection Observer for infinite scrolling
  useEffect(() => {
    const element = loadMoreRef.current;
    if (!element || !hasNextPage || isFetchingNextPage) return;

    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && fetchNextPage) {
          fetchNextPage();
        }
      },
      { threshold: 1.0 },
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const isEmpty = data.length === 0 && !isLoading;

  return (
    <div className="flex flex-col">
      <div className="rounded-lg overflow-hidden">
        <Table
          verticalSpacing="sm"
          className="w-full"
          highlightOnHover
          withTableBorder
          withRowBorders={false}
        >
          <Table.Thead className="bg-primary text-white font-bold">
            <Table.Tr className="h-[60px] text-sm">
              {columns.map(({ key, header, width }) => (
                <Table.Th
                  key={key}
                  className="text-left bg-transparent"
                  style={{ width }}
                >
                  {header}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>

          <Table.Tbody>
            {isEmpty ? (
              <Table.Tr style={{ height: `${emptyTableHeight}px` }}>
                <Table.Td
                  colSpan={columns.length}
                  className="text-center text-gray-500"
                >
                  {isLoading
                    ? 'Loading logs...'
                    : 'No logs found matching your search or filter criteria'}
                </Table.Td>
              </Table.Tr>
            ) : (
              <>
                {data.map(item => {
                  const rowKey = keyExtractor(item);
                  const isExpanded = expandedRows.has(rowKey);
                  const hasExpandableContent = columns.some(
                    col => col.expandedContent,
                  );

                  return (
                    <Fragment key={rowKey}>
                      <Table.Tr
                        className={`h-[60px] cursor-pointer ${isExpanded ? 'bg-gray-50' : ''}`}
                        onClick={e => {
                          if (hasExpandableContent) {
                            toggleRowExpansion(rowKey, e);
                          } else if (onRowClick) {
                            onRowClick(item);
                          }
                        }}
                        tabIndex={onRowClick ? 0 : undefined}
                        style={{ transition: 'background 0.2s' }}
                      >
                        {columns.map(({ key, render, width }) => (
                          <Table.Td
                            key={key}
                            className="font-normal text-left"
                            style={{ width }}
                          >
                            {render
                              ? render(item)
                              : String(item[key as keyof T] ?? '')}
                          </Table.Td>
                        ))}
                      </Table.Tr>
                      {isExpanded && (
                        <Table.Tr>
                          <Table.Td colSpan={columns.length} p="md">
                            <div className="pl-4 border-l-2 border-gray-200">
                              {columns
                                .filter(col => col.expandedContent)
                                .map(col => (
                                  <div key={col.key}>
                                    {col.expandedContent?.(item)}
                                  </div>
                                ))}
                            </div>
                          </Table.Td>
                        </Table.Tr>
                      )}
                    </Fragment>
                  );
                })}
              </>
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Load More Section */}
      {(hasNextPage || isFetchingNextPage) && (
        <div ref={loadMoreRef} className="flex justify-center mt-6 mb-4">
          {isFetchingNextPage ? (
            <Text size="sm" c="dimmed">
              Loading more logs...
            </Text>
          ) : hasNextPage ? (
            <Button variant="subtle" size="sm" onClick={fetchNextPage}>
              Load More
            </Button>
          ) : null}
        </div>
      )}

      {/* End of logs indicator */}
      {!hasNextPage && !isFetchingNextPage && data.length > 0 && (
        <Center mt="md">
          <Text size="sm" c="dimmed">
            No more logs to load
          </Text>
        </Center>
      )}
    </div>
  );
}

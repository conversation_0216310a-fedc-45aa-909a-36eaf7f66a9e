import { Button } from '@mantine/core';
import LogoSvg from '../assets/logos/Logo.svg';
import PlaceholderLogoSvg from '../assets/logos/PlaceholderLogo.svg';

export function ThemeExample() {
  return (
    <div className="p-8 flex flex-col gap-6 items-center">
      <div className="mb-4 flex flex-col items-center">
        <h2 className="text-2xl font-bold mb-4 text-primary">Logo Display</h2>
        <div className="flex flex-col md:flex-row gap-8 items-center">
          <div className="flex flex-col items-center">
            <img src={LogoSvg} alt="Original Logo" className="h-16 mb-2" />
            <p className="text-sm text-gray-600">Original Logo</p>
          </div>
          <div className="flex flex-col items-center">
            <img
              src={PlaceholderLogoSvg}
              alt="Placeholder Logo"
              className="h-16 mb-2"
            />
            <p className="text-sm text-gray-600">Placeholder Logo</p>
          </div>
        </div>
      </div>

      <h1 className="text-4xl font-bold text-primary">Theme Example</h1>

      <div className="flex gap-4 flex-wrap justify-center">
        <div className="w-24 h-24 bg-primary rounded-md flex items-center justify-center text-white">
          Primary
        </div>
        <div className="w-24 h-24 bg-[var(--color-secondary)] rounded-md flex items-center justify-center text-white">
          Secondary
        </div>
        <div className="w-24 h-24 bg-[var(--color-tertiary)] rounded-md flex items-center justify-center text-white">
          Tertiary
        </div>
        <div className="w-24 h-24 bg-black rounded-md flex items-center justify-center text-white">
          Black
        </div>
        <div className="w-24 h-24 bg-white rounded-md border flex items-center justify-center text-black">
          White
        </div>
      </div>

      <div className="flex flex-col gap-2 items-center">
        <p className="text-xl">Font family: Jost</p>
        <p className="font-light">Font weight: Light (300)</p>
        <p className="font-normal">Font weight: Regular (400)</p>
        <p className="font-medium">Font weight: Medium (500)</p>
        <p className="font-semibold">Font weight: Semibold (600)</p>
        <p className="font-bold">Font weight: Bold (700)</p>
      </div>

      <div className="flex gap-4 flex-wrap justify-center">
        <Button style={{ backgroundColor: 'var(--color-primary)' }} size="md">
          Primary Button
        </Button>
        <Button style={{ backgroundColor: 'var(--color-secondary)' }} size="md">
          Secondary Button
        </Button>
        <Button style={{ backgroundColor: 'var(--color-tertiary)' }} size="md">
          Tertiary Button
        </Button>
      </div>
    </div>
  );
}

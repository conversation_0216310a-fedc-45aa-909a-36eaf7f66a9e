import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  Group,
  Modal,
  Select,
  Stack,
  Textarea,
  TextInput,
} from '@mantine/core';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  KnowledgeBaseCreate,
  KnowledgeBaseTypeEnum,
} from '../../types/KnowledgeBaseTypes';

interface CreateKnowledgeBaseModalProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (data: KnowledgeBaseCreate) => Promise<void>;
  projectId: string;
  loading?: boolean;
}

const knowledgeBaseSchema = z.object({
  name: z
    .string()
    .min(1, 'Knowledge base name is required')
    .max(255, 'Name too long'),
  description: z.string().optional(),
  kb_type: z.enum(KnowledgeBaseTypeEnum, {
    error: 'Please select a knowledge base type',
  }),
});

type KnowledgeBaseFormData = z.infer<typeof knowledgeBaseSchema>;

const CreateKnowledgeBaseModal: React.FC<CreateKnowledgeBaseModalProps> = ({
  opened,
  onClose,
  onSubmit,
  projectId,
  loading = false,
}) => {
  const { control, handleSubmit, formState, reset } =
    useForm<KnowledgeBaseFormData>({
      resolver: zodResolver(knowledgeBaseSchema),
      defaultValues: {
        name: '',
        description: '',
        kb_type: undefined,
      },
    });

  const { errors } = formState;

  const handleFormSubmit = async (data: KnowledgeBaseFormData) => {
    try {
      await onSubmit({
        ...data,
        project_id: projectId,
      });
      // Reset form after successful submission
      reset({
        name: '',
        description: '',
        kb_type: undefined,
      });
    } catch (error) {
      // Error handling is done in the parent component
      throw error;
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const kbTypeOptions = [
    {
      value: KnowledgeBaseTypeEnum.PROJECT_DOCUMENTATION,
      label: 'Project Documentation',
    },
    {
      value: KnowledgeBaseTypeEnum.EXTERNAL_DOCUMENTATION,
      label: 'External Documentation',
    },
    {
      value: KnowledgeBaseTypeEnum.SYSTEM_ARCHITECTURE,
      label: 'System Architecture',
    },
    { value: KnowledgeBaseTypeEnum.SERVICE_DETAILS, label: 'Service Details' },
    { value: KnowledgeBaseTypeEnum.RUNBOOKS, label: 'Runbooks' },
    { value: KnowledgeBaseTypeEnum.OTHER, label: 'Other' },
  ];

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Create Knowledge Base"
      size="md"
    >
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <Stack gap="md">
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label="Knowledge Base Name"
                placeholder="Enter knowledge base name"
                required
                error={errors.name?.message}
              />
            )}
          />

          <Controller
            name="kb_type"
            control={control}
            render={({ field }) => (
              <Select
                {...field}
                label="Type"
                placeholder="Select knowledge base type"
                required
                data={kbTypeOptions}
                error={errors.kb_type?.message}
              />
            )}
          />

          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                label="Description"
                placeholder="Enter knowledge base description (optional)"
                rows={4}
                error={errors.description?.message}
              />
            )}
          />

          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button
              type="submit"
              loading={loading}
              style={{ backgroundColor: 'var(--color-primary)' }}
            >
              Create Knowledge Base
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
};

export default CreateKnowledgeBaseModal;

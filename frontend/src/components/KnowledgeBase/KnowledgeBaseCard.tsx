import {
  <PERSON><PERSON><PERSON>,
  Badge,
  Card,
  Group,
  Menu,
  Stack,
  Text,
  Tooltip,
} from '@mantine/core';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {
  BookOpen,
  Clock,
  Edit,
  MoreVertical,
  Trash2,
  Upload,
} from 'lucide-react';
import React from 'react';
import { useNavigate } from 'react-router';
import { KnowledgeBase } from '../../types/KnowledgeBaseTypes';

dayjs.extend(relativeTime);

interface KnowledgeBaseCardProps {
  knowledgeBase: KnowledgeBase;
  onEdit?: (kb: KnowledgeBase) => void;
  onDelete?: (kb: KnowledgeBase) => void;
  onUploadDocument?: (kb: KnowledgeBase) => void;
}

const KnowledgeBaseCard: React.FC<KnowledgeBaseCardProps> = ({
  knowledgeBase,
  onEdit,
  onDelete,
  onUploadDocument,
}) => {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate(`/knowledge-base/${knowledgeBase.id}`);
  };

  const handleMenuAction = (action: string, event: React.MouseEvent) => {
    event.stopPropagation();

    switch (action) {
      case 'edit':
        onEdit?.(knowledgeBase);
        break;
      case 'delete':
        onDelete?.(knowledgeBase);
        break;
      case 'upload':
        onUploadDocument?.(knowledgeBase);
        break;
    }
  };

  const getKbTypeColor = (type: string) => {
    switch (type) {
      case 'PROJECT_DOCUMENTATION':
        return 'blue';
      case 'EXTERNAL_DOCUMENTATION':
        return 'green';
      case 'SYSTEM_ARCHITECTURE':
        return 'purple';
      case 'SERVICE_DETAILS':
        return 'orange';
      case 'RUNBOOKS':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getKbTypeLabel = (type: string) => {
    return type
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      className="cursor-pointer hover:shadow-md transition-shadow"
      onClick={handleCardClick}
    >
      <Stack gap="sm">
        <Group justify="space-between" align="flex-start">
          <Group gap="sm" align="center">
            <BookOpen size={20} className="text-green-600" />
            <Text fw={600} size="lg" lineClamp={1}>
              {knowledgeBase.name}
            </Text>
          </Group>

          <Menu shadow="md" width={200} position="bottom-end">
            <Menu.Target>
              <ActionIcon
                variant="subtle"
                size="sm"
                onClick={e => e.stopPropagation()}
              >
                <MoreVertical size={16} />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item
                leftSection={<Edit size={14} />}
                onClick={e => handleMenuAction('edit', e)}
              >
                Edit Knowledge Base
              </Menu.Item>
              <Menu.Item
                leftSection={<Upload size={14} />}
                onClick={e => handleMenuAction('upload', e)}
              >
                Upload Document
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                leftSection={<Trash2 size={14} />}
                color="red"
                onClick={e => handleMenuAction('delete', e)}
              >
                Delete Knowledge Base
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>

        <Badge
          color={getKbTypeColor(knowledgeBase.kb_type)}
          variant="light"
          size="sm"
        >
          {getKbTypeLabel(knowledgeBase.kb_type)}
        </Badge>

        {knowledgeBase.description && (
          <Text size="sm" c="dimmed" lineClamp={2}>
            {knowledgeBase.description}
          </Text>
        )}

        <Group justify="space-between" align="center">
          <Group gap="xs">
            <Text size="xs" c="dimmed">
              {knowledgeBase.documents.length} document
              {knowledgeBase.documents.length !== 1 ? 's' : ''}
            </Text>
          </Group>

          <Tooltip
            label={dayjs(knowledgeBase.updated_at).format(
              'YYYY-MM-DD HH:mm:ss',
            )}
          >
            <Text size="xs" c="dimmed">
              <Clock size={14} className="inline-block mr-1" />
              Updated {dayjs(knowledgeBase.updated_at).fromNow()}
            </Text>
          </Tooltip>
        </Group>
      </Stack>
    </Card>
  );
};

export default KnowledgeBaseCard;

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  FileInput,
  Group,
  Modal,
  Stack,
  Tabs,
  Text,
  Textarea,
  TextInput,
} from '@mantine/core';
import { FileText, Link, Upload } from 'lucide-react';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { DocumentCreate } from '../../types/KnowledgeBaseTypes';

interface FileUploadModalProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (data: DocumentCreate) => Promise<void>;
  loading?: boolean;
}

const fileUploadSchema = z
  .object({
    name: z.string().optional(),
    description: z.string().optional(),
    file: z.instanceof(File).optional(),
    external_url: z.union([z.url(), z.literal('')]).optional(),
    content: z.string().optional(),
  })
  .refine(
    data =>
      data.file ||
      (data.external_url && data.external_url.trim() !== '') ||
      (data.content && data.content.trim() !== ''),
    {
      message: 'Please provide either a file, URL, or text content',
      path: ['file'],
    },
  );

type FileUploadData = z.infer<typeof fileUploadSchema>;

const FileUploadModal: React.FC<FileUploadModalProps> = ({
  opened,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [activeTab, setActiveTab] = useState<string>('file');

  const { control, handleSubmit, formState, reset, watch } =
    useForm<FileUploadData>({
      resolver: zodResolver(fileUploadSchema),
      defaultValues: {
        name: '',
        description: '',
        file: undefined,
        external_url: '',
        content: '',
      },
    });

  const { errors } = formState;
  const watchedFile = watch('file');

  const handleFormSubmit = async (data: FileUploadData) => {
    const submitData: DocumentCreate = {};

    if (data.name) submitData.name = data.name;
    if (data.description) submitData.description = data.description;

    switch (activeTab) {
      case 'file':
        if (data.file) submitData.file = data.file;
        break;
      case 'url':
        if (data.external_url) submitData.external_url = data.external_url;
        break;
      case 'text':
        if (data.content) submitData.content = data.content;
        break;
    }

    try {
      await onSubmit(submitData);
      // Reset form after successful submission
      reset({
        name: '',
        description: '',
        file: undefined,
        external_url: '',
        content: '',
      });
      setActiveTab('file');
    } catch (error) {
      // Error handling is done in the parent component
      throw error;
    }
  };

  const handleClose = () => {
    reset();
    setActiveTab('file');
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Upload Document"
      size="lg"
    >
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <Stack gap="md">
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label="Document Name"
                placeholder="Enter document name (optional)"
                error={errors.name?.message}
              />
            )}
          />

          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                label="Description"
                placeholder="Enter document description (optional)"
                rows={3}
                error={errors.description?.message}
              />
            )}
          />

          <Tabs
            value={activeTab}
            onChange={value => setActiveTab(value || 'file')}
          >
            <Tabs.List>
              <Tabs.Tab value="file" leftSection={<Upload size={16} />}>
                File Upload
              </Tabs.Tab>
              <Tabs.Tab value="url" leftSection={<Link size={16} />}>
                External URL
              </Tabs.Tab>
              <Tabs.Tab value="text" leftSection={<FileText size={16} />}>
                Text Content
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="file" pt="md">
              <Controller
                name="file"
                control={control}
                render={({ field: { onChange, value, ...field } }) => (
                  <FileInput
                    {...field}
                    value={value || null}
                    onChange={onChange}
                    label="Select File"
                    placeholder="Choose a file to upload"
                    accept=".pdf,.txt,.md,.docx,.doc"
                    error={
                      activeTab === 'file' ? errors.file?.message : undefined
                    }
                  />
                )}
              />
              {watchedFile && (
                <Text size="sm" c="dimmed" mt="xs">
                  Selected: {watchedFile.name} (
                  {(watchedFile.size / 1024 / 1024).toFixed(2)} MB)
                </Text>
              )}
            </Tabs.Panel>

            <Tabs.Panel value="url" pt="md">
              <Controller
                name="external_url"
                control={control}
                render={({ field }) => (
                  <TextInput
                    {...field}
                    label="External URL"
                    placeholder="https://example.com/document"
                    error={
                      activeTab === 'url'
                        ? errors.external_url?.message
                        : undefined
                    }
                  />
                )}
              />
            </Tabs.Panel>

            <Tabs.Panel value="text" pt="md">
              <Controller
                name="content"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    label="Text Content"
                    placeholder="Enter or paste your text content here..."
                    rows={8}
                    error={
                      activeTab === 'text' ? errors.content?.message : undefined
                    }
                  />
                )}
              />
            </Tabs.Panel>
          </Tabs>

          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button
              type="submit"
              loading={loading}
              style={{ backgroundColor: 'var(--color-primary)' }}
            >
              Upload Document
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
};

export default FileUploadModal;

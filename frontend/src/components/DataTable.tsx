import { Pagination, Table } from '@mantine/core';
import { Fragment, ReactNode, useState } from 'react';

export interface Column<T> {
  key: string;
  header: string;
  render?: (item: T) => ReactNode;
  minWidth?: string;
  maxWidth?: string;
  flexGrow?: number;
  flexShrink?: number;
  flexBasis?: string;
  expandedContent?: (item: T) => ReactNode;
  hideOnMobile?: boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  itemsPerPage?: number;
  onRowClick?: (item: T) => void;
  emptyTableHeight?: number;
  keyExtractor: (item: T) => string;
  currentPage?: number;
  remainingPages?: number;
  onPageChange?: (page: number) => void;
}

export function DataTable<T>({
  data,
  columns,
  itemsPerPage = 8,
  onRowClick,
  emptyTableHeight = 60,
  keyExtractor,
  currentPage = 1,
  remainingPages = 1,
  onPageChange,
}: DataTableProps<T>) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const handlePageChange = (page: number) => {
    if (page !== currentPage) {
      onPageChange?.(page);
    }
  };

  const toggleRowExpansion = (rowKey: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(rowKey)) {
      newExpandedRows.delete(rowKey);
    } else {
      newExpandedRows.add(rowKey);
    }
    setExpandedRows(newExpandedRows);
  };

  const isEmpty = data.length === 0;
  const paddingRows = itemsPerPage - data.length;

  // Get column styles for responsive layout
  const getColumnStyle = (column: Column<T>) => {
    const style: React.CSSProperties = {};

    if (column.minWidth) {
      style.minWidth = column.minWidth;
    }

    if (column.maxWidth) {
      style.maxWidth = column.maxWidth;
    }

    if (
      column.flexGrow !== undefined ||
      column.flexShrink !== undefined ||
      column.flexBasis
    ) {
      style.flex = `${column.flexGrow || 0} ${column.flexShrink || 1} ${column.flexBasis || 'auto'}`;
    }

    return style;
  };

  return (
    <div className="flex flex-col">
      <div className="rounded-lg overflow-hidden">
        <Table
          verticalSpacing="sm"
          className="w-full"
          highlightOnHover
          withTableBorder
          withRowBorders={false}
          style={{
            minWidth: '600px', // Ensure minimum width for horizontal scroll on mobile
            tableLayout: 'auto', // Allow table to size dynamically
          }}
        >
          <Table.Thead className="bg-primary text-white font-bold">
            <Table.Tr className="h-[60px] text-sm">
              {columns.map(column => (
                <Table.Th
                  key={column.key}
                  className={`text-left bg-transparent ${column.hideOnMobile ? 'hidden sm:table-cell' : ''}`}
                  style={getColumnStyle(column)}
                >
                  {column.header}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>

          <Table.Tbody>
            {isEmpty ? (
              <Table.Tr style={{ height: `${emptyTableHeight}px` }}>
                <Table.Td
                  colSpan={columns.length}
                  className="text-center text-gray-500"
                >
                  No rows found matching your search or filter criteria
                </Table.Td>
              </Table.Tr>
            ) : (
              <>
                {data.map(item => {
                  const rowKey = keyExtractor(item);
                  const isExpanded = expandedRows.has(rowKey);
                  const hasExpandableContent = columns.some(
                    col => col.expandedContent,
                  );

                  return (
                    <Fragment key={rowKey}>
                      <Table.Tr
                        className={`h-[60px] cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${isExpanded ? 'bg-gray-50' : ''}`}
                        onClick={e => {
                          if (hasExpandableContent) {
                            toggleRowExpansion(rowKey, e);
                          } else if (onRowClick) {
                            onRowClick(item);
                          }
                        }}
                        tabIndex={onRowClick ? 0 : undefined}
                      >
                        {columns.map(column => (
                          <Table.Td
                            key={column.key}
                            className={`font-normal text-left align-top py-3 ${column.hideOnMobile ? 'hidden sm:table-cell' : ''}`}
                            style={getColumnStyle(column)}
                          >
                            <div className="overflow-hidden">
                              {column.render
                                ? column.render(item)
                                : String(item[column.key as keyof T] ?? '')}
                            </div>
                          </Table.Td>
                        ))}
                      </Table.Tr>
                      {isExpanded && (
                        <Table.Tr>
                          <Table.Td colSpan={columns.length} p="md">
                            <div className="pl-4 border-l-2 border-gray-200">
                              {columns
                                .filter(col => col.expandedContent)
                                .map(col => (
                                  <div key={col.key}>
                                    {col.expandedContent?.(item)}
                                  </div>
                                ))}
                            </div>
                          </Table.Td>
                        </Table.Tr>
                      )}
                    </Fragment>
                  );
                })}
                {Array.from({ length: paddingRows }, (_, i) => (
                  <Table.Tr
                    key={`empty-${i}`}
                    style={{ height: `${emptyTableHeight}px` }}
                  >
                    <Table.Td colSpan={columns.length} />
                  </Table.Tr>
                ))}
              </>
            )}
          </Table.Tbody>
        </Table>
      </div>

      <div className="flex justify-center mt-6 mb-4">
        <Pagination
          total={remainingPages}
          value={currentPage}
          onChange={handlePageChange}
          color="var(--color-primary)"
          size="md"
          withEdges
          boundaries={2}
          siblings={1}
        />
      </div>
    </div>
  );
}

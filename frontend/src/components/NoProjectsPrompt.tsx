import { Button, Container, Paper, Stack, Text, Title } from '@mantine/core';
import { FolderPlus } from 'lucide-react';
import React from 'react';
import { useNavigate } from 'react-router';

interface NoProjectsPromptProps {
  isLoading?: boolean;
}

const NoProjectsPrompt: React.FC<NoProjectsPromptProps> = ({
  isLoading = false,
}) => {
  const navigate = useNavigate();

  if (isLoading) {
    return null; // Let the parent component handle loading state
  }

  return (
    <Container size="sm" py="xl">
      <Paper p="xl" radius="md" withBorder style={{ textAlign: 'center' }}>
        <Stack gap="lg" align="center">
          <FolderPlus size={64} className="text-gray-400" />

          <div>
            <Title order={2} c="var(--color-primary)" mb="sm">
              Welcome
            </Title>
            <Text size="lg" c="dimmed" mb="md">
              No projects found
            </Text>
            <Text
              size="sm"
              c="dimmed"
              style={{ maxWidth: 400, margin: '0 auto' }}
            >
              To get started, you'll need to create your first project.
            </Text>
          </div>

          <Button
            size="lg"
            leftSection={<FolderPlus size={20} />}
            onClick={() => navigate('/projects')}
            style={{ backgroundColor: 'var(--color-primary)' }}
          >
            Create Your First Project
          </Button>
        </Stack>
      </Paper>
    </Container>
  );
};

export default NoProjectsPrompt;

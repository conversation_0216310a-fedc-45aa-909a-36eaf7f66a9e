import { ScrollArea } from '@mantine/core';
import React, { ReactNode } from 'react';
import { useLocation } from 'react-router';
import { useProjectContext } from '../contexts/ProjectContext';
import { Navbar } from './Navbar';
import NoProjectsPrompt from './NoProjectsPrompt';
import { Sidebar } from './Sidebar';

interface LayoutProps {
  children: ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const { needsProjectCreation, isLoading } = useProjectContext();

  // Pages that don't require project selection
  const noProjectRequiredPaths = ['/projects'];

  // Show no projects prompt for knowledge base page when no projects exist
  const shouldShowNoProjectsPrompt =
    needsProjectCreation &&
    !noProjectRequiredPaths.includes(location.pathname) &&
    !isLoading;

  return (
    <>
      <Sidebar />
      <div className="h-full w-full flex flex-col p-0 relative">
        <Navbar />
        <ScrollArea>
          <main
            className="w-full h-full pt-[90px] px-4 sm:px-6 md:px-8 lg:px-10 pb-4 sm:pb-6 md:pb-8 lg:pb-10"
            role="main"
            aria-label="Main content"
            style={{
              minHeight: 'calc(100vh - 90px)',
              maxWidth: '100vw',
              overflow: 'hidden',
            }}
          >
            {shouldShowNoProjectsPrompt ? (
              <NoProjectsPrompt isLoading={isLoading} />
            ) : (
              children
            )}
          </main>
        </ScrollArea>
      </div>
    </>
  );
};

import { Badge, Button, Group, Paper, Stack, Tabs, Title } from '@mantine/core';
import { FileText, MessageSquare, Plus } from 'lucide-react';
import { useState } from 'react';
import AttachmentsTab, { attachments } from './AttachmentsTab';
import DiscussionsTab, { discussions } from './DiscussionsTab';
import ReferencesTab, { references } from './ReferencesTab';

const IncidentReferences = () => {
  const [activeTab, setActiveTab] = useState<string | null>('references');

  return (
    <Stack gap="lg">
      <Paper p="lg" radius="md" withBorder>
        <Group justify="space-between" align="center" mb="md">
          <Group align="center" gap="sm">
            <FileText size={24} color="blue" />
            <Title order={2}>References & Resources</Title>
            <Badge color="blue" variant="light">
              {references.length} resources
            </Badge>
          </Group>
          <Button leftSection={<Plus size={16} />} size="sm">
            Add Reference
          </Button>
        </Group>

        <Tabs value={activeTab} onChange={setActiveTab} color="blue">
          <Tabs.List>
            <Tabs.Tab value="references" leftSection={<FileText size={16} />}>
              References ({references.length})
            </Tabs.Tab>
            <Tabs.Tab
              value="attachments"
              leftSection={<MessageSquare size={16} />}
            >
              Attachments ({attachments.length})
            </Tabs.Tab>
            <Tabs.Tab
              value="discussions"
              leftSection={<MessageSquare size={16} />}
            >
              Discussion ({discussions.length})
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="references" pt="md">
            <ReferencesTab />
          </Tabs.Panel>

          <Tabs.Panel value="discussions" pt="md">
            <DiscussionsTab />
          </Tabs.Panel>
          <Tabs.Panel value="attachments" pt="md">
            <AttachmentsTab />
          </Tabs.Panel>
        </Tabs>
      </Paper>
    </Stack>
  );
};

export default IncidentReferences;

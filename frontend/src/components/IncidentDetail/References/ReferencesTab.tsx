import {
  ActionIcon,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  Card,
  Group,
  Stack,
  Text,
  TextInput,
  ThemeIcon,
  Tooltip,
} from '@mantine/core';
import {
  AlertCircle,
  BookOpen,
  ExternalLink,
  FileText,
  Link,
  Search,
  X,
} from 'lucide-react';
import { useState } from 'react';
interface Reference {
  id: string;
  type:
    | 'documentation'
    | 'runbook'
    | 'related_incident'
    | 'knowledge_base'
    | 'external';
  title: string;
  description: string;
  url: string;
  tags: string[];
  addedBy: string;
  addedAt: string;
  relevanceScore: number;
}
export const references: Reference[] = [
  {
    id: '1',
    type: 'documentation',
    title: 'Payment API Documentation',
    description:
      'Complete API documentation for the payment service including parameter validation rules',
    url: 'https://docs.company.com/payment-api',
    tags: ['api', 'payment', 'parameters'],
    addedBy: 'System',
    addedAt: '2025-05-13T06:00:00Z',
    relevanceScore: 95,
  },
  {
    id: '2',
    type: 'runbook',
    title: 'Payment Service Rollback Procedure',
    description:
      'Step-by-step guide for rolling back payment service deployments',
    url: 'https://runbooks.company.com/payment-rollback',
    tags: ['rollback', 'deployment', 'payment'],
    addedBy: 'Alan Biju',
    addedAt: '2025-05-13T06:15:00Z',
    relevanceScore: 88,
  },
  {
    id: '3',
    type: 'related_incident',
    title: 'INC-3821 - Similar Payment API Issue',
    description:
      'Previous incident with parameter validation failure, resolved by updating validation logic',
    url: '/incidents/3821',
    tags: ['payment', 'validation', 'resolved'],
    addedBy: 'System',
    addedAt: '2025-05-13T05:55:00Z',
    relevanceScore: 92,
  },
  {
    id: '4',
    type: 'knowledge_base',
    title: 'Parameter Validation Best Practices',
    description:
      'Guidelines for implementing robust parameter validation in microservices',
    url: 'https://kb.company.com/validation-best-practices',
    tags: ['validation', 'best-practices', 'microservices'],
    addedBy: 'Ron Jose',
    addedAt: '2025-05-13T06:30:00Z',
    relevanceScore: 78,
  },
  {
    id: '5',
    type: 'external',
    title: 'HTTP 400 Bad Request Troubleshooting',
    description:
      'External resource on debugging and fixing HTTP 400 errors in REST APIs',
    url: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400',
    tags: ['http', 'troubleshooting', 'external'],
    addedBy: 'Alan Biju',
    addedAt: '2025-05-13T07:00:00Z',
    relevanceScore: 65,
  },
];

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'documentation':
      return <FileText size={16} />;
    case 'runbook':
      return <BookOpen size={16} />;
    case 'related_incident':
      return <AlertCircle size={16} />;
    case 'knowledge_base':
      return <Search size={16} />;
    case 'external':
      return <ExternalLink size={16} />;
    default:
      return <Link size={16} />;
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'documentation':
      return 'blue';
    case 'runbook':
      return 'green';
    case 'related_incident':
      return 'orange';
    case 'knowledge_base':
      return 'purple';
    case 'external':
      return 'gray';
    default:
      return 'gray';
  }
};

const ReferencesTab = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredReferences = references.filter(
    ref =>
      ref.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ref.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ref.tags.some(tag =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
  );
  return (
    <>
      {/* Search */}
      <TextInput
        placeholder="Search references by title, description, or tags..."
        leftSection={<Search size={16} />}
        rightSection={
          searchQuery && (
            <Tooltip label="Clear search">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                onClick={() => setSearchQuery('')}
              >
                <X size={14} />
              </ActionIcon>
            </Tooltip>
          )
        }
        value={searchQuery}
        onChange={e => setSearchQuery(e.currentTarget.value)}
        mb="md"
      />

      {/* Auto-suggested references */}
      <Alert color="blue" icon={<AlertCircle size={16} />} mb="md">
        <Text size="sm">
          AI has automatically identified{' '}
          {references.filter(r => r.addedBy === 'System').length} relevant
          resources based on incident context and historical data.
        </Text>
      </Alert>

      {/* References list */}
      <Stack gap="md">
        {filteredReferences
          .sort((a, b) => b.relevanceScore - a.relevanceScore)
          .map(reference => (
            <Card key={reference.id} padding="md" radius="sm" withBorder>
              <Group justify="space-between" align="flex-start" mb="xs">
                <Group align="center" gap="sm">
                  <ThemeIcon
                    size="md"
                    color={getTypeColor(reference.type)}
                    variant="light"
                  >
                    {getTypeIcon(reference.type)}
                  </ThemeIcon>
                  <div style={{ flex: 1 }}>
                    <Group align="center" gap="xs" mb="xs">
                      <Anchor href={reference.url} target="_blank" fw={600}>
                        {reference.title}
                      </Anchor>
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        component="a"
                        href={reference.url}
                        target="_blank"
                      >
                        <ExternalLink size={14} />
                      </ActionIcon>
                    </Group>
                    <Text size="sm" c="dimmed" mb="xs">
                      {reference.description}
                    </Text>
                    <Group gap="xs">
                      {reference.tags.map(tag => (
                        <Badge key={tag} size="xs" variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </Group>
                  </div>
                </Group>
                <Stack align="flex-end" gap="xs">
                  <Badge
                    size="sm"
                    color={
                      reference.relevanceScore > 85
                        ? 'green'
                        : reference.relevanceScore > 70
                          ? 'orange'
                          : 'gray'
                    }
                    variant="light"
                  >
                    {reference.relevanceScore}% relevant
                  </Badge>
                  <Group gap="xs" align="center">
                    <Text size="xs" c="dimmed">
                      by {reference.addedBy}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {new Date(reference.addedAt).toLocaleTimeString()}
                    </Text>
                  </Group>
                </Stack>
              </Group>
            </Card>
          ))}
      </Stack>
    </>
  );
};

export default ReferencesTab;

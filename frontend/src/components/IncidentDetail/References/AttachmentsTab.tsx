import {
  ActionIcon,
  Badge,
  Card,
  Group,
  Stack,
  Text,
  ThemeIcon,
} from '@mantine/core';
import { ExternalLink, FileText } from 'lucide-react';

interface Attachment {
  id: string;
  name: string;
  type: string;
  size: string;
  addedBy: string;
  addedAt: string;
}
export const attachments: Attachment[] = [
  {
    id: '1',
    name: 'Payment API Logs',
    type: 'log',
    size: '2.5MB',
    addedBy: 'Alan Biju',
    addedAt: '2025-05-13T06:15:00Z',
  },
  {
    id: '2',
    name: 'Payment API Traces',
    type: 'trace',
    size: '1.2MB',
    addedBy: '<PERSON>',
    addedAt: '2025-05-13T06:30:00Z',
  },
];
const AttachmentsTab = () => {
  return (
    <Stack gap="md">
      {attachments.map(attachment => (
        <Card key={attachment.id} padding="md" radius="sm" withBorder>
          <Group justify="space-between" align="flex-start">
            <Group align="center" gap="sm">
              <ThemeIcon size="lg" color="blue" variant="light">
                <FileText size={20} />
              </ThemeIcon>
              <div>
                <Group gap="xs" align="center">
                  <Text size="sm" mb="xs">
                    {attachment.name}
                  </Text>
                </Group>
                <Group gap="xs" align="center">
                  <Badge size="xs" variant="outline">
                    {attachment.type}
                  </Badge>
                  <Text size="xs" c="dimmed">
                    {attachment.size}
                  </Text>
                </Group>
                <Group gap="xs" align="center" mt="xs">
                  <Text size="xs" c="dimmed">
                    Added by {attachment.addedBy}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {new Date(attachment.addedAt).toLocaleString()}
                  </Text>
                </Group>
              </div>
            </Group>
            <Group gap="xs">
              <ActionIcon size="sm" variant="subtle">
                <ExternalLink size={14} />
              </ActionIcon>
            </Group>
          </Group>
        </Card>
      ))}
    </Stack>
  );
};

export default AttachmentsTab;

import { <PERSON><PERSON>, <PERSON><PERSON>, Container, Loader, Stack, Text } from '@mantine/core';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import React from 'react';
import { useIncident } from '../../hooks/useIncident';
import IncidentHeader from './IncidentHeader';

interface IncidentDetailLayoutProps {
  incidentId: string;
  children: React.ReactNode;
}

/**
 * Layout wrapper for incident detail pages that handles:
 * - Loading state
 * - Error handling
 * - Incident header rendering
 * - Provides incident data to children
 */
const IncidentDetailLayout: React.FC<IncidentDetailLayoutProps> = ({
  incidentId,
  children,
}) => {
  const { incident, loading, error, refetch } = useIncident(incidentId);

  if (loading) {
    return (
      <Container
        fluid
        p="md"
        style={{ position: 'relative', minHeight: '400px' }}
      >
        <div className="flex justify-center items-center h-96">
          <Loader color="var(--color-primary)" size="md" />
        </div>
      </Container>
    );
  }

  if (error || !incident) {
    return (
      <Container fluid p="md">
        <Alert
          icon={<AlertTriangle size={16} />}
          title="Error loading incident"
          color="red"
          variant="light"
        >
          <Text mb="md">{error || 'Incident not found'}</Text>
          <Button
            leftSection={<RefreshCw size={16} />}
            onClick={refetch}
            variant="outline"
          >
            Retry
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid p="md">
      <Stack gap="md">
        <IncidentHeader incident={incident} />
        {children}
      </Stack>
    </Container>
  );
};

export default IncidentDetailLayout;

import React, { useState, use<PERSON><PERSON>back, ReactNode } from 'react';
import {
  Card,
  Group,
  ActionIcon,
  Button,
  Text,
  Code,
  Collapse,
  Textarea,
} from '@mantine/core';
import {
  ChevronDown,
  ChevronUp,
  CheckCircle,
  XCircle,
  CircleDashed,
  CircleCheck,
  CircleX,
  Trash,
} from 'lucide-react';
import {
  queryRunbookSteps,
  useUpdateRunbookStep,
  useDeleteRunbookStep,
} from '../../../hooks/useApi';
import { useQueryClient } from '@tanstack/react-query';
import { STEP_STATUS, StepStatus } from '../../../constants/types';

const STATUS_ICONS: Record<
  (typeof STEP_STATUS)[keyof typeof STEP_STATUS],
  ReactNode
> = {
  [STEP_STATUS.PENDING]: <CircleDashed size={24} color="gray" />,
  [STEP_STATUS.SUCCESSFUL]: <CircleCheck size={24} color="green" />,
  [STEP_STATUS.FAILED]: <CircleX size={24} color="red" />,
};

interface RunbookStepsProps {
  runbookId: string;
  incidentId: string;
}

const RunbookSteps: React.FC<RunbookStepsProps> = ({
  runbookId,
  incidentId,
}) => {
  const [expandedSteps, setExpandedSteps] = useState<string[]>([]);
  const [stepNotes, setStepNotes] = useState<Record<string, string>>({});
  const queryClient = useQueryClient();
  const { data: runbookSteps = [] } = queryRunbookSteps(incidentId, runbookId);
  const updateStepMutation = useUpdateRunbookStep();
  const deleteStepMutation = useDeleteRunbookStep();
  const toggleStep = useCallback((stepId: string) => {
    setExpandedSteps(prev =>
      prev.includes(stepId)
        ? prev.filter(id => id !== stepId)
        : [...prev, stepId],
    );
  }, []);

  const handleUpdate = useCallback(
    async (stepId: string, status: StepStatus) => {
      const notes = stepNotes[stepId] || '';
      try {
        await updateStepMutation.mutateAsync({
          incidentId,
          runbookId,
          stepId,
          data: { status, notes },
        });

        queryClient.invalidateQueries({
          queryKey: ['runbookSteps', incidentId, runbookId],
        });

        setStepNotes(prev => ({ ...prev, [stepId]: '' }));
      } catch (error) {
        console.error('Failed to update step:', error);
      }
    },
    [incidentId, runbookId, queryClient, stepNotes, updateStepMutation],
  );

  const handleNotesChange = useCallback((stepId: string, value: string) => {
    setStepNotes(prev => ({ ...prev, [stepId]: value }));
  }, []);

  const deleteStep = useCallback(
    async (stepId: string) => {
      try {
        await deleteStepMutation.mutateAsync({
          incidentId,
          runbookId,
          stepId,
        });
        queryClient.invalidateQueries({
          queryKey: ['runbookSteps', incidentId, runbookId],
        });
      } catch (error) {
        console.error('Failed to delete step:', error);
      }
    },
    [incidentId, runbookId, queryClient, deleteStepMutation],
  );

  return (
    <div className="flex flex-col gap-4">
      {runbookSteps.map(step => {
        const isExpanded = expandedSteps.includes(step.id);
        const isCompleted = step.status !== STEP_STATUS.PENDING;
        const isDeleting = deleteStepMutation.isPending;
        return (
          <Card
            key={step.id}
            p="md"
            radius="md"
            withBorder
            style={{
              opacity: isDeleting ? 0.6 : 1,
              pointerEvents: isDeleting ? 'none' : 'auto',
            }}
          >
            <div className="flex justify-between">
              <div>
                <Group align="center">
                  <Text ta="left" size="sm" fw={700}>
                    Step {step.step_order}:
                  </Text>
                  <Text>{step.title}</Text>
                </Group>
                <Text ta="left" size="sm" c="dimmed">
                  {step.description}
                </Text>
              </div>
              <Group>
                <ActionIcon
                  variant="transparent"
                  c="dimmed"
                  onClick={() => deleteStep(step.id)}
                  disabled={isDeleting}
                  loading={isDeleting}
                >
                  <Trash size={16} />
                </ActionIcon>
                {STATUS_ICONS[step.status]}
                <ActionIcon
                  variant="transparent"
                  c="dimmed"
                  onClick={() => toggleStep(step.id)}
                  disabled={isDeleting}
                >
                  {isExpanded ? <ChevronUp /> : <ChevronDown />}
                </ActionIcon>
              </Group>
            </div>

            <Collapse in={isExpanded}>
              <div className="mt-4 space-y-4">
                {step.details && (
                  <div className="bg-gray-50 p-4 rounded-md">
                    <Text ta="left" size="sm" fw={600} mb={2}>
                      Details:
                    </Text>
                    <Code
                      block
                      style={{
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word',
                      }}
                    >
                      {step.details}
                    </Code>
                  </div>
                )}

                {step.expected_result && (
                  <div className="bg-gray-50 p-4 rounded-md">
                    <Text ta="left" size="sm" fw={600} mb={2}>
                      Expected Result:
                    </Text>
                    <Text size="sm">{step.expected_result}</Text>
                  </div>
                )}

                {step.notes && step.executed_by_user && (
                  <div className="p-4 rounded-md">
                    <Text ta="left" size="sm" fw={600} mb={10}>
                      Notes:
                    </Text>
                    <Group align="flex-start" mt={2}>
                      <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                        <Text size="sm" c="white" fw={700}>
                          {step.executed_by_user.first_name[0]}
                          {step.executed_by_user.last_name[0]}
                        </Text>
                      </div>
                      <div className="flex-1">
                        <Group gap="xs">
                          <Text size="sm" fw={600}>
                            {step.executed_by_user.first_name}{' '}
                            {step.executed_by_user.last_name}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {new Date(step.executed_at || '').toLocaleString()}
                          </Text>
                        </Group>
                        <Text
                          ta="left"
                          size="sm"
                          style={{ whiteSpace: 'pre-wrap' }}
                        >
                          {step.notes}
                        </Text>
                      </div>
                    </Group>
                  </div>
                )}

                {step.status === STEP_STATUS.PENDING && (
                  <Textarea
                    placeholder="Add notes about what you did or observed..."
                    autosize
                    minRows={2}
                    maxRows={4}
                    value={stepNotes[step.id] || ''}
                    onChange={e =>
                      handleNotesChange(step.id, e.currentTarget.value)
                    }
                  />
                )}

                <Group justify="flex-end" mt="md">
                  <Button
                    variant="outline"
                    color="red"
                    disabled={
                      isCompleted || updateStepMutation.isPending || isDeleting
                    }
                    leftSection={<XCircle size={16} />}
                    loading={updateStepMutation.isPending}
                    onClick={() => handleUpdate(step.id, STEP_STATUS.FAILED)}
                  >
                    Didn't Work
                  </Button>
                  <Button
                    variant="outline"
                    color="green"
                    disabled={
                      isCompleted || updateStepMutation.isPending || isDeleting
                    }
                    leftSection={<CheckCircle size={16} />}
                    loading={updateStepMutation.isPending}
                    onClick={() =>
                      handleUpdate(step.id, STEP_STATUS.SUCCESSFUL)
                    }
                  >
                    Mark as Done
                  </Button>
                </Group>
              </div>
            </Collapse>
          </Card>
        );
      })}
    </div>
  );
};

export default RunbookSteps;

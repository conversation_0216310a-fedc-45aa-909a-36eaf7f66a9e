import React from 'react';
import {
  <PERSON><PERSON>,
  Box,
  Card,
  Group,
  ThemeIcon,
  Title,
  Badge,
  Text,
  ActionIcon,
  Loader,
} from '@mantine/core';
import { BookOpen, Play, X, Eye, ExternalLink } from 'lucide-react';
import {
  queryRunbookSteps,
  useGenerateRunbookSteps,
} from '../../../hooks/useApi';
import { useQueryClient } from '@tanstack/react-query';
import RunbookSteps from './RunbookSteps';
import { Runbook } from '../../../types/RunbookType';

interface RunbookCardProps {
  runbook: Runbook;
  incidentId: string;
  getCategoryColor: (type: string) => string;
  isActive: boolean;
  setActive: (runbookId: string | null) => void;
}

const RunbookCard: React.FC<RunbookCardProps> = ({
  runbook,
  incidentId,
  getCategoryColor,
  isActive,
  setActive,
}) => {
  const queryClient = useQueryClient();
  const generateStepsMutation = useGenerateRunbookSteps();

  // Only query steps when runbook is active
  const { isLoading: stepsLoading } = queryRunbookSteps(
    incidentId,
    runbook.id,
    {
      enabled: isActive,
    },
  );

  const handleToggleActive = () => {
    const newActiveId = isActive ? null : runbook.id;
    setActive(newActiveId);
  };

  const handleGenerateMoreSteps = () => {
    generateStepsMutation.mutate(
      { incidentId, runbookId: runbook.id },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ['runbookSteps', incidentId, runbook.id],
          });
        },
        onError: error => {
          console.error('Failed to generate steps:', error);
        },
      },
    );
  };

  return (
    <Card
      padding="lg"
      radius="sm"
      withBorder
      style={{ width: '100%', overflow: 'hidden' }}
    >
      <Group justify="space-between" align="flex-start" mb="md">
        <div style={{ flex: 1 }}>
          <Group align="center" gap="sm" mb="xs">
            <ThemeIcon
              size="md"
              color={getCategoryColor(runbook.type)}
              variant="light"
            >
              <BookOpen size={16} />
            </ThemeIcon>
            <Title order={4}>{runbook.title}</Title>
            <Badge
              size="sm"
              color={getCategoryColor(runbook.type)}
              variant="light"
            >
              {runbook.type}
            </Badge>
          </Group>
          <Text ta="left" size="sm" c="dimmed" mb="sm">
            {runbook.purpose}
          </Text>
          <Group gap="sm" align="center">
            <Text size="xs" c="dimmed">
              Created {new Date(runbook.created_at).toLocaleDateString()}
            </Text>
          </Group>
        </div>
        <Group gap="xs">
          <Button
            size="sm"
            color="green"
            leftSection={<Play size={16} />}
            onClick={handleToggleActive}
          >
            Execute
          </Button>
          <Button
            size="sm"
            color="blue"
            variant="light"
            leftSection={isActive ? <X size={16} /> : <Eye size={16} />}
            onClick={handleToggleActive}
          >
            {isActive ? 'Close' : 'View'}
          </Button>
          <ActionIcon variant="subtle" size="sm">
            <ExternalLink size={16} />
          </ActionIcon>
        </Group>
      </Group>

      {isActive && (
        <Box style={{ width: '100%', overflowX: 'auto' }}>
          <div style={{ minWidth: 'fit-content', maxWidth: '100%' }}>
            <RunbookSteps incidentId={incidentId} runbookId={runbook.id} />
          </div>
          <div className="flex justify-center mt-4 mb-4">
            {stepsLoading ? (
              <Group gap="sm" align="center">
                <Loader size="sm" />
                <Text size="sm" c="dimmed">
                  Loading steps, please wait...
                </Text>
              </Group>
            ) : (
              <Button
                variant="outline"
                color="green"
                loading={generateStepsMutation.isPending}
                disabled={generateStepsMutation.isPending}
                onClick={handleGenerateMoreSteps}
              >
                {runbook.steps.length > 0
                  ? 'Generate More Steps'
                  : 'Generate Steps'}
              </Button>
            )}
          </div>
        </Box>
      )}
    </Card>
  );
};

export default RunbookCard;

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Group,
  Paper,
  Stack,
  Text,
  ThemeIcon,
  Title,
  Loader,
} from '@mantine/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Brain,
  Lightbulb,
  Search,
  TrendingUp,
} from 'lucide-react';
import { useAiAnalysis } from '../../../hooks/useApi';
import { AiAnalysisResponse } from '../../../types/AiAnalysisType';
import { useParams } from 'react-router';

interface AIInsight {
  id: string;
  type: 'analysis' | 'recommendation' | 'prediction' | 'warning';
  title: string;
  content: string;
}
// Transform API response to component format
const transformAnalysisToInsights = (
  analysis: AiAnalysisResponse,
): AIInsight[] => {
  const insights: AIInsight[] = [];

  // Root cause analysis
  if (analysis.root_cause) {
    insights.push({
      id: '1',
      type: 'analysis',
      title: 'Root Cause Analysis',
      content: analysis.root_cause,
    });
  }

  // Immediate action
  if (analysis.immediate_action) {
    insights.push({
      id: '2',
      type: 'recommendation',
      title: 'Immediate Action',
      content: analysis.immediate_action,
    });
  }

  // Impact forecast
  if (analysis.impact_forecast) {
    insights.push({
      id: '3',
      type: 'prediction',
      title: 'Impact Forecast',
      content: analysis.impact_forecast,
    });
  }

  // Cascading risks
  if (analysis.cascading_risks) {
    insights.push({
      id: '4',
      type: 'warning',
      title: 'Cascading Risks',
      content: analysis.cascading_risks,
    });
  }

  return insights;
};

const getInsightIcon = (type: string) => {
  switch (type) {
    case 'analysis':
      return <Search size={16} />;
    case 'recommendation':
      return <Lightbulb size={16} />;
    case 'prediction':
      return <TrendingUp size={16} />;
    case 'warning':
      return <AlertTriangle size={16} />;
    default:
      return <Brain size={16} />;
  }
};

const getInsightColor = (type: string) => {
  switch (type) {
    case 'analysis':
      return '#021a5a';
    case 'recommendation':
      return '#0573ea';
    case 'prediction':
      return '#00b15c';
    case 'warning':
      return '#d32f2f';
    default:
      return '#636e85';
  }
};

const AnalysisSummary = () => {
  const { incidentId } = useParams();
  if (!incidentId) {
    return null;
  }
  const { data: analysis, isLoading, error } = useAiAnalysis(incidentId);

  if (isLoading) {
    return (
      <Paper p="lg" radius="md" withBorder>
        <Group align="center" gap="sm" mb="md">
          <Bot size={24} color="blue" />
          <Title order={2}>AI Analysis & Recommendations</Title>
        </Group>
        <Group justify="center" p="xl">
          <Loader size="md" />
          <Text>Generating AI analysis...</Text>
        </Group>
      </Paper>
    );
  }

  if (error || !analysis) {
    return (
      <Paper p="lg" radius="md" withBorder>
        <Group align="center" gap="sm" mb="md">
          <Bot size={24} color="blue" />
          <Title order={2}>AI Analysis & Recommendations</Title>
        </Group>
        <Alert color="red" icon={<AlertTriangle size={16} />}>
          <Text size="sm">
            Failed to generate AI analysis. Please try again later.
          </Text>
        </Alert>
      </Paper>
    );
  }

  const aiInsights = transformAnalysisToInsights(analysis);

  return (
    <Paper p="lg" radius="md" withBorder>
      <Group align="center" gap="sm" mb="md">
        <Bot size={20} />
        <Title order={3}>AI Analysis & Recommendations</Title>
        <Badge color="blue" variant="light" size="sm">
          {aiInsights.length} insights
        </Badge>
      </Group>

      <Stack gap="sm">
        {aiInsights.map((insight: AIInsight) => (
          <Card key={insight.id} padding="md" radius="md" withBorder>
            <Group align="center" gap="sm" mb="xs">
              <ThemeIcon
                size="sm"
                color={getInsightColor(insight.type)}
                variant="light"
              >
                {getInsightIcon(insight.type)}
              </ThemeIcon>
              <Text size="sm" fw={600}>
                {insight.title}
              </Text>
              <Badge
                size="xs"
                variant="light"
                color={getInsightColor(insight.type)}
              >
                {insight.type}
              </Badge>
            </Group>
            <Text ta="left" size="sm" c="dimmed" pl="xl">
              {insight.content}
            </Text>
          </Card>
        ))}
      </Stack>
    </Paper>
  );
};

export default AnalysisSummary;

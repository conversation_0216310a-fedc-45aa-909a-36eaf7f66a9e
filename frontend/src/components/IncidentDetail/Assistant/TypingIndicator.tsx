import React, { useEffect, useState } from 'react';
import { Box, Text } from '@mantine/core';

const TypingIndicator: React.FC = () => {
  const [dots, setDots] = useState('.');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '.';
        return prev + '.';
      });
    }, 300); // Faster animation (300ms instead of 500ms)

    return () => clearInterval(interval);
  }, []);

  return (
    <Box>
      <Text span fw={500}>
        Typing{dots}
      </Text>
    </Box>
  );
};

export default TypingIndicator;

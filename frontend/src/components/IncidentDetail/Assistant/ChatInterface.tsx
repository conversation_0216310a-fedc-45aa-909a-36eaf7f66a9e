import {
  Avatar,
  Button,
  Card,
  Flex,
  Group,
  Paper,
  ScrollArea,
  Stack,
  Text,
  Textarea,
  Box,
  Title,
} from '@mantine/core';
import { Bot, RefreshCw, Send, User, Zap } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { useAiChat } from '../../../hooks/useApi';
import TypingIndicator from './TypingIndicator';
import { useParams } from 'react-router';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}
const ChatInterface = () => {
  const { incidentId } = useParams();
  const [userInput, setUserInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const viewportRef = useRef<HTMLDivElement>(null);
  const { mutate: generateAIResponse } = useAiChat();
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  // Auto-scroll to bottom
  useEffect(() => {
    if (viewportRef.current) {
      viewportRef.current.scrollTo({
        top: viewportRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, [chatMessages, isTyping]);

  const handleRefreshAnalysis = () => {
    setChatMessages([]);
  };

  const handleSendMessage = async () => {
    if (!userInput.trim()) return;

    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: userInput,
      timestamp: new Date(),
    };

    // Add a temporary typing message
    const typingMessage: ChatMessage = {
      id: 'typing-temp',
      type: 'ai',
      content: 'typing...',
      timestamp: new Date(),
    };

    setChatMessages(prev => [...prev, newUserMessage, typingMessage]);
    setUserInput('');
    setIsLoading(true);
    setIsTyping(true);

    generateAIResponse(
      {
        message: `${userInput}. If not provided earlier, the incident id is ${incidentId}`,
      },
      {
        onSuccess: (data: any) => {
          const aiResponse: ChatMessage = {
            id: (Date.now() + 1).toString(),
            type: 'ai',
            content: data,
            timestamp: new Date(),
          };

          setChatMessages(prev => [...prev.slice(0, -1), aiResponse]);
          setIsLoading(false);
          setIsTyping(false);
        },
        onError: (error: any) => {
          console.error('Error generating AI response:', error);
          setChatMessages(prev => prev.slice(0, -1));
          setIsLoading(false);
          setIsTyping(false);
        },
      },
    );
  };

  return (
    <Paper p="lg" radius="md" withBorder>
      <Group align="center" gap="sm" mb="md">
        <Zap size={20} color="purple" />
        <Title order={3}>Ask AI Assistant</Title>
        <Button
          size="xs"
          variant="subtle"
          leftSection={<RefreshCw size={14} />}
          onClick={handleRefreshAnalysis}
        >
          Refresh Analysis
        </Button>
      </Group>

      <Card withBorder mb="md">
        <ScrollArea h={350} p="md" viewportRef={viewportRef}>
          <Stack gap="md">
            {chatMessages.map(message => (
              <Box
                key={message.id}
                className={`w-full mb-4  flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <Flex gap="sm" align="flex-start" className={`max-w-[80%]`}>
                  {/* AI Avatar (left side) */}
                  {message.type === 'ai' && (
                    <Avatar size="md" color="cyan" radius="xl">
                      <Bot size={20} />
                    </Avatar>
                  )}
                  <Box className="flex max-w-2xl flex-col">
                    <Paper
                      p="md"
                      radius="lg"
                      ta={message.type === 'user' ? 'right' : 'left'}
                      bg={message.type === 'user' ? '#021A5A' : '#BEE6FF'}
                      className={`text-sm ${
                        message.type === 'user' ? 'text-white' : 'text-black'
                      } break-words`}
                      style={{
                        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.2)',
                      }}
                    >
                      {message.id === 'typing-temp' ? (
                        <TypingIndicator />
                      ) : (
                        <Paper p="xs" radius="sm" bg="transparent">
                          {message.content}
                        </Paper>
                      )}
                    </Paper>
                    <Text
                      size="xs"
                      color="dimmed"
                      className={`mt-1 ${message.type === 'user' ? 'text-right' : 'text-left'}`}
                    >
                      {message.timestamp.toLocaleTimeString()}
                    </Text>
                  </Box>
                  {/* User Avatar (right side) */}
                  {message.type === 'user' && (
                    <Avatar size="md" color="blue" radius="xl">
                      <User size={20} />
                    </Avatar>
                  )}
                </Flex>
              </Box>
            ))}
          </Stack>
        </ScrollArea>
      </Card>

      <Group align="flex-end" gap="sm">
        <Textarea
          placeholder="Ask about root cause, similar incidents, resolution steps..."
          value={userInput}
          onChange={e => setUserInput(e.currentTarget.value)}
          style={{ flex: 1 }}
          onKeyDown={e => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleSendMessage();
            }
          }}
        />
        <Button
          onClick={handleSendMessage}
          disabled={!userInput.trim() || isLoading}
          leftSection={<Send size={16} />}
        >
          Send
        </Button>
      </Group>
    </Paper>
  );
};

export default ChatInterface;

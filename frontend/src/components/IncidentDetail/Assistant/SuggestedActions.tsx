import { Button, Divider, Group, Paper, Text, Title } from '@mantine/core';
import { Target } from 'lucide-react';

const SuggestedActions = () => {
  return (
    <Paper p="lg" radius="md" withBorder>
      <Group align="center" gap="sm" mb="md">
        <Target size={20} color="green" />
        <Title order={3}>Suggested Actions</Title>
      </Group>

      <Text size="sm" c="dimmed">
        No suggested actions available.
      </Text>

      <Divider my="md" />

      <Group justify="space-between">
        <Text size="sm" c="dimmed">
          Last updated: {new Date().toLocaleTimeString()}
        </Text>
        <Group gap="xs">
          <Button size="xs" variant="light" color="blue">
            Export Analysis
          </Button>
          <Button size="xs" variant="light" color="green">
            Apply Recommendations
          </Button>
        </Group>
      </Group>
    </Paper>
  );
};

export default SuggestedActions;

import {
  Badge,
  Button,
  Group,
  Loader,
  Paper,
  Stack,
  Text,
  Textarea,
  Timeline,
  Title,
} from '@mantine/core';
import { Activity, Eye, Send } from 'lucide-react';
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import {
  queryIncidentEvents,
  useCreateIncidentEvent,
} from '../../../hooks/useApi';
import { IncidentEvent } from '../../../types/IncidentTimelineType';

const TimelinePreview = () => {
  const navigate = useNavigate();
  const { incidentId } = useParams<{ incidentId: string }>();
  const { data: timelineEvents, isLoading } = queryIncidentEvents(
    incidentId || '',
    { skip: 0, limit: 3, sort: 'desc' },
  );
  const { mutate: createIncidentEvent, isPending } = useCreateIncidentEvent(
    incidentId || '',
  );
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitComment = async () => {
    if (!comment.trim()) return;

    setIsSubmitting(true);
    createIncidentEvent({
      incidentId: incidentId || '',
      event_name: 'Comment',
      event_type: 'COMMENT_ADDED',
      event_details: { description: comment },
    });
    setComment('');
    setIsSubmitting(false);
  };

  return (
    <>
      <Paper p="lg" radius="md" withBorder>
        <Group justify="space-between" align="center" mb="md">
          <Group align="center" gap="sm">
            <Activity size={20} />
            <Title order={3}>Recent Updates</Title>
          </Group>
          <Button
            size="sm"
            variant="light"
            leftSection={<Eye size={16} />}
            onClick={() => {
              navigate(`/incident/${incidentId}/timeline`);
            }}
          >
            View Full Timeline
          </Button>
        </Group>
        {isLoading ? (
          <Loader color="var(--color-primary)" size="md" />
        ) : timelineEvents?.items && timelineEvents.items.length > 0 ? (
          <Timeline
            active={(timelineEvents.items.length || 1) - 1}
            bulletSize={18}
          >
            {timelineEvents.items
              .slice()
              .reverse()
              .map((event: IncidentEvent) => (
                <Timeline.Item key={event.id} title={event.event_name}>
                  <Group gap="xs" align="center">
                    <Text size="xs" c="dimmed">
                      {new Date(event.event_datetime).toLocaleString()}
                    </Text>
                    <Badge size="xs" variant="outline">
                      {event.event_type}
                    </Badge>
                  </Group>
                </Timeline.Item>
              ))}
          </Timeline>
        ) : (
          <Text c="dimmed" ta="center" py="xl">
            No recent updates found. Be the first to add an update!
          </Text>
        )}
      </Paper>

      <Paper p="lg" radius="md" withBorder>
        <Title order={3} mb="md">
          Add Update
        </Title>
        <Stack gap="md">
          <Textarea
            placeholder="Add a comment, status update, or share findings..."
            value={comment}
            onChange={e => setComment(e.currentTarget.value)}
            rows={3}
            resize="vertical"
          />
          <Group justify="flex-end">
            <Button
              leftSection={<Send size={16} />}
              onClick={handleSubmitComment}
              disabled={!comment.trim()}
              loading={isSubmitting || isPending}
            >
              Post Update
            </Button>
          </Group>
        </Stack>
      </Paper>
    </>
  );
};

export default TimelinePreview;

import { Group, Paper, Spoiler, Title } from '@mantine/core';
import { ReceiptText } from 'lucide-react';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import ReactMarkdown from 'react-markdown';

const Details = ({ details }: { details: string }) => {
  // Ensure details is a string and provide fallback
  const safeDetails =
    typeof details === 'string' && details.trim()
      ? details.trim()
      : 'No details available';

  return (
    <Paper p="lg" radius="md" withBorder>
      <Group align="center" gap="sm" mb="md">
        <ReceiptText size={20} />
        <Title order={3}>Details</Title>
      </Group>
      <Spoiler
        maxHeight={40}
        maw={800}
        showLabel="Show more"
        hideLabel="Show less"
      >
        <div
          style={{
            fontSize: 'var(--mantine-font-size-sm)',
            textAlign: 'left',
            lineHeight: 'var(--mantine-line-height-sm)',
          }}
        >
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkBreaks]}
            rehypePlugins={[rehypeRaw, rehypeSanitize, rehypeHighlight]}
            components={{
              p: ({ children }) => (
                <div style={{ marginBottom: '1em' }}>{children}</div>
              ),
            }}
          >
            {safeDetails}
          </ReactMarkdown>
        </div>
      </Spoiler>
    </Paper>
  );
};

export default Details;

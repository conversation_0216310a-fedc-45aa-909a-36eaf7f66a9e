import { Badge, Group, Paper, Stack, Text, Title } from '@mantine/core';

const StatusHistory = () => {
  return (
    <Paper p="lg" radius="md" withBorder>
      <Title order={3} mb="md">
        Status Progress
      </Title>
      <Stack gap="xs">
        <Group justify="space-between">
          <Text size="sm">Incident Created</Text>
          <Badge size="xs" color="gray">
            Completed
          </Badge>
        </Group>
        <Group justify="space-between">
          <Text size="sm">Team Assigned</Text>
          <Badge size="xs" color="blue">
            Current
          </Badge>
        </Group>
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Investigation
          </Text>
          <Badge size="xs" variant="outline">
            Pending
          </Badge>
        </Group>
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Root Cause Analysis
          </Text>
          <Badge size="xs" variant="outline">
            Pending
          </Badge>
        </Group>
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Identify Solutions
          </Text>
          <Badge size="xs" variant="outline">
            Pending
          </Badge>
        </Group>
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Resolution
          </Text>
          <Badge size="xs" variant="outline">
            Pending
          </Badge>
        </Group>
        <Group justify="space-between">
          <Text size="sm" c="dimmed">
            Post-Incident Review
          </Text>
          <Badge size="xs" variant="outline">
            Pending
          </Badge>
        </Group>
      </Stack>
    </Paper>
  );
};

export default StatusHistory;

import { Button, Paper, Stack, Title } from '@mantine/core';

const QuickActions = () => {
  return (
    <Paper p="lg" radius="md" withBorder>
      <Title order={3} mb="md">
        Quick Actions
      </Title>
      <Stack gap="sm">
        <Button fullWidth variant="light" color="blue">
          Escalate Incident
        </Button>
        <Button fullWidth variant="light" color="green">
          Mark as Resolved
        </Button>
        <Button fullWidth variant="light" color="orange">
          Request Support
        </Button>
      </Stack>
    </Paper>
  );
};

export default QuickActions;

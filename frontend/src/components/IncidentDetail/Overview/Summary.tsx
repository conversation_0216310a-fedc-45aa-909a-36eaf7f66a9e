import {
  Group,
  Paper,
  Text,
  Title,
  Button,
  LoadingOverlay,
} from '@mantine/core';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { useParams } from 'react-router';
import { toast } from 'react-toastify';
import { useRegenerateIncidentSummary } from '../../../hooks/useApi';

const Summary = ({ summary }: { summary: string }) => {
  const { incidentId } = useParams<{ incidentId: string }>();
  const regenerateSummaryMutation = useRegenerateIncidentSummary();

  const handleRegenerateSummary = async () => {
    if (!incidentId) return;

    try {
      await regenerateSummaryMutation.mutateAsync(incidentId);
      toast.success('Summary regenerated successfully');
    } catch (error) {
      toast.error('Failed to regenerate summary');
    }
  };

  return (
    <Paper p="lg" radius="md" withBorder style={{ position: 'relative' }}>
      <LoadingOverlay visible={regenerateSummaryMutation.isPending} />
      <Group align="center" justify="space-between" mb="md">
        <Group align="center" gap="sm">
          <AlertCircle size={20} />
          <Title order={3}>Summary</Title>
        </Group>
        <Button
          variant="outline"
          size="xs"
          leftSection={<RefreshCw size={14} />}
          onClick={handleRegenerateSummary}
          loading={regenerateSummaryMutation.isPending}
        >
          Regenerate
        </Button>
      </Group>
      <Text size="sm" ta="left">
        {summary}
      </Text>
    </Paper>
  );
};

export default Summary;

import { Avatar, Group, Paper, Stack, Text, Title } from '@mantine/core';
import { Users } from 'lucide-react';
import { User } from '../../../types/IncidentType';

const AssignedUsers = ({ users }: { users: User[] }) => {
  return (
    <Paper p="lg" radius="md" withBorder>
      <Group align="center" gap="sm" mb="md">
        <Users size={20} />
        <Title order={3}>Assigned Team</Title>
      </Group>
      <Stack gap="sm">
        {users.map(user => (
          <Group key={user.id} align="center">
            <Avatar name={`${user.first_name} ${user.last_name}`} size="md" />
            <div style={{ flex: 1 }}>
              <Text size="sm" fw={500}>
                {`${user.first_name} ${user.last_name}`}
              </Text>
              <Text size="xs" c="dimmed">
                {user.role}
              </Text>
            </div>
          </Group>
        ))}
      </Stack>
    </Paper>
  );
};

export default AssignedUsers;

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Group,
  Paper,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { Server } from 'lucide-react';
import { AffectedService } from '../../../types/IncidentType';
import { SERVICE_STATUS, IMPACT } from '../../../constants/types';
import {
  SERVICE_STATUS_COLORS,
  IMPACT_COLORS,
  UI_COLORS,
} from '../../../constants/colors';

const getServiceStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case SERVICE_STATUS.OPERATIONAL.toLowerCase():
      return SERVICE_STATUS_COLORS.OPERATIONAL.FILLED;
    case SERVICE_STATUS.DEGRADED.toLowerCase():
      return SERVICE_STATUS_COLORS.DEGRADED.FILLED;
    case SERVICE_STATUS.DOWN.toLowerCase():
      return SERVICE_STATUS_COLORS.DOWN.FILLED;
    case SERVICE_STATUS.MAINTENANCE.toLowerCase():
      return SERVICE_STATUS_COLORS.MAINTENANCE.FILLED;
    default:
      return UI_COLORS.MUTED;
  }
};

const getImpactColor = (impact: string) => {
  switch (impact.toLowerCase()) {
    case IMPACT.HIGH.toLowerCase():
      return IMPACT_COLORS.HIGH.FILLED;
    case IMPACT.MEDIUM.toLowerCase():
      return IMPACT_COLORS.MEDIUM.FILLED;
    case IMPACT.LOW.toLowerCase():
      return IMPACT_COLORS.LOW.FILLED;
    default:
      return UI_COLORS.MUTED;
  }
};

const AffectedServices = ({ services }: { services: AffectedService[] }) => {
  return (
    <Paper p="lg" radius="md" withBorder>
      <Group align="center" gap="sm" mb="md">
        <Server size={20} />
        <Title order={3}>Affected Services</Title>
        <Badge size="sm" variant="light" color={UI_COLORS.INFO}>
          {services.length} services
        </Badge>
      </Group>
      <Stack gap="sm">
        {services.map(service => (
          <Card key={service.id} padding="md" radius="sm" withBorder>
            <Group justify="space-between" align="center">
              <Group align="center">
                <Badge
                  color={getServiceStatusColor(service.status)}
                  variant="dot"
                >
                  {service.status}
                </Badge>
                <Text>{service.name}</Text>
                <Badge
                  size="xs"
                  color={getImpactColor(service.impact)}
                  variant="light"
                >
                  {service.impact} impact
                </Badge>
              </Group>
              <Button size="xs" variant="subtle" color={UI_COLORS.PRIMARY}>
                View Details
              </Button>
            </Group>
            {service.details.length > 0 && (
              <Text size="sm" c={UI_COLORS.MUTED} mt="xs">
                {service.details.join(', ')}
              </Text>
            )}
          </Card>
        ))}
      </Stack>
    </Paper>
  );
};

export default AffectedServices;

import {
  Badge,
  Card,
  Group,
  Paper,
  SimpleGrid,
  Text,
  Title,
  ThemeIcon,
  ActionIcon,
  Tooltip,
  Stack,
  Button,
} from '@mantine/core';
import {
  TrendingUp,
  Calendar,
  Clock,
  Copy,
  Eye,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router';
import dayjs from 'dayjs';
import { querySimilarIncidents } from '../../../hooks/useApi';

interface SimilarIncidentsProps {
  incidentId: string;
}

const SimilarIncidents = ({ incidentId }: SimilarIncidentsProps) => {
  const [showAll, setShowAll] = useState(false);
  const navigate = useNavigate();

  const { data: similarIncidentsResponse, isLoading } = querySimilarIncidents(
    incidentId,
    8,
  );
  const allSimilarIncidents = similarIncidentsResponse?.similar_incidents || [];
  const displayedSimilarIncidents = showAll
    ? allSimilarIncidents
    : allSimilarIncidents.slice(0, 4);
  const hasMoreIncidents = allSimilarIncidents.length > 4;

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'closed':
        return 'gray';
      case 'resolved':
        return 'green';
      case 'active':
      case 'investigating':
        return 'blue';
      case 'open':
        return 'red';
      default:
        return 'gray';
    }
  };

  const handleIncidentClick = (incidentId: string) => {
    // Navigate to incident details using React Router
    navigate(`/incident/${incidentId}`);
  };

  const handleCopyId = async (incidentId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(incidentId);
      // You can add a notification here
    } catch (err) {
      console.error('Failed to copy incident ID:', err);
    }
  };

  if (isLoading) {
    return (
      <Paper p="lg" radius="md" withBorder>
        <Group align="center" gap="sm" mb="md">
          <ThemeIcon size="md" color="blue" variant="light">
            <TrendingUp size={16} />
          </ThemeIcon>
          <Title order={3}>Similar Incidents</Title>
        </Group>
        <Text size="sm" c="dimmed">
          Finding similar incidents...
        </Text>
      </Paper>
    );
  }

  if (!similarIncidentsResponse || allSimilarIncidents.length === 0) {
    return (
      <Paper p="lg" radius="md" withBorder>
        <Group align="center" gap="sm" mb="md">
          <ThemeIcon size="md" color="blue" variant="light">
            <TrendingUp size={16} />
          </ThemeIcon>
          <Title order={3}>Similar Incidents</Title>
          <Badge color="gray" variant="light">
            0 found
          </Badge>
        </Group>
        <Text size="sm" c="dimmed">
          No similar incidents found for this incident.
        </Text>
      </Paper>
    );
  }

  return (
    <Paper p="lg" radius="md" withBorder>
      <Group align="center" gap="sm" mb="md">
        <ThemeIcon size="md" color="blue" variant="light">
          <TrendingUp size={16} />
        </ThemeIcon>
        <Title order={3}>Similar Incidents</Title>
        <Badge color="blue" variant="light">
          {similarIncidentsResponse?.total_found || 0} found
        </Badge>
      </Group>

      <Stack gap="md">
        <SimpleGrid cols={2} spacing="md">
          {displayedSimilarIncidents.map(similarIncident => (
            <Card
              key={similarIncident.incident.id}
              padding="lg"
              radius="md"
              withBorder
              style={{
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
              }}
              onClick={() => handleIncidentClick(similarIncident.incident.id)}
            >
              <Stack gap="sm" align="flex-start" style={{ height: '100%' }}>
                {/* Header with Incident Number, Title, Status and Copy Action */}
                <Group justify="space-between" align="center" w="100%">
                  <Text
                    fw={600}
                    size="md"
                    lineClamp={2}
                    style={{ flex: 1, textAlign: 'left' }}
                  >
                    #{similarIncident.incident.incident_number} -{' '}
                    {similarIncident.incident.title}
                  </Text>
                  <Badge
                    size="sm"
                    color={getStatusColor(similarIncident.incident.status)}
                    variant="light"
                  >
                    {similarIncident.incident.status.toUpperCase()}
                  </Badge>
                  <Tooltip label="Copy incident ID">
                    <ActionIcon
                      variant="subtle"
                      color="gray"
                      size="sm"
                      onClick={e =>
                        handleCopyId(similarIncident.incident.id, e)
                      }
                    >
                      <Copy size={14} />
                    </ActionIcon>
                  </Tooltip>
                </Group>

                {/* Similarity Score */}
                <Group gap="xs" align="center">
                  <Badge size="xs" color="blue" variant="outline">
                    {Math.round(similarIncident.similarity_score * 100)}%
                    similar
                  </Badge>
                </Group>

                {/* Summary */}
                <Text
                  size="sm"
                  c="dimmed"
                  lineClamp={3}
                  style={{ textAlign: 'left' }}
                >
                  {similarIncident.incident.summary}
                </Text>

                {/* Reported DateTime */}
                <Group gap="xs" align="center">
                  <Calendar size={14} color="var(--mantine-color-gray-6)" />
                  <Text size="xs" c="dimmed" style={{ textAlign: 'left' }}>
                    <strong>Reported:</strong>{' '}
                    {dayjs(similarIncident.incident.reported_at).format(
                      'MMM D, YYYY h:mm A',
                    )}
                  </Text>
                </Group>

                {/* Closed DateTime - only show if status is closed */}
                {similarIncident.incident.status.toLowerCase() === 'closed' && (
                  <Group gap="xs" align="center">
                    <Clock size={14} color="var(--mantine-color-gray-6)" />
                    <Text size="xs" c="dimmed" style={{ textAlign: 'left' }}>
                      <strong>Closed:</strong>{' '}
                      {dayjs(similarIncident.incident.reported_at).format(
                        'MMM D, YYYY h:mm A',
                      )}{' '}
                      {/* Note: API doesn't provide closed_at */}
                    </Text>
                  </Group>
                )}

                {/* Spacer to push button to bottom */}
                <div style={{ flex: 1 }} />

                {/* View Details Button - Bottom Right */}
                <Group justify="flex-end" w="100%">
                  <Button
                    size="xs"
                    variant="light"
                    color="blue"
                    leftSection={<Eye size={14} />}
                    onClick={e => {
                      e.stopPropagation();
                      handleIncidentClick(similarIncident.incident.id);
                    }}
                  >
                    View Details
                  </Button>
                </Group>
              </Stack>
            </Card>
          ))}
        </SimpleGrid>

        {/* View More/Less Button */}
        {hasMoreIncidents && (
          <Group justify="center" mt="md">
            <Button
              variant="light"
              color="blue"
              size="sm"
              leftSection={
                showAll ? <ChevronUp size={16} /> : <ChevronDown size={16} />
              }
              onClick={() => setShowAll(!showAll)}
            >
              {showAll
                ? 'View Less'
                : `View More (${allSimilarIncidents.length - 4} more)`}
            </Button>
          </Group>
        )}
      </Stack>
    </Paper>
  );
};

export default SimilarIncidents;

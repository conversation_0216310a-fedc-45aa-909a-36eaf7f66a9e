import { Grid, Stack } from '@mantine/core';
import { useParams } from 'react-router';
import { queryIncidentById, queryIncidentDetails } from '../../../hooks/useApi';
import AffectedServices from './AffectedServices';
import AssignedUsers from './AssignedUsers';
import Summary from './Summary';
import QuickActions from './QuickActions';
import StatusHistory from './StatusHistory';
import TimelinePreview from './TimelinePreview';
import SimilarIncidents from './SimilarIncidents';
import Details from './Details';

const IncidentOverview = () => {
  const { incidentId } = useParams<{ incidentId: string }>();
  if (!incidentId) {
    return null;
  }
  const { data: incident } = queryIncidentById(incidentId);
  const { data: incidentDetails } = queryIncidentDetails(incidentId);

  if (!incident) return null;

  return (
    <Grid>
      <Grid.Col span={12}>
        <Stack gap="lg">
          <Grid>
            {/* Left Column */}
            <Grid.Col span={{ base: 12, md: 8 }}>
              <Stack gap="md">
                <Summary summary={incident.summary || 'No summary available'} />
                <Details
                  details={
                    incidentDetails?.incident_details || 'No details available'
                  }
                />
                <TimelinePreview />
                <SimilarIncidents incidentId={incidentId} />
              </Stack>
            </Grid.Col>

            {/* Right Column*/}
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Stack gap="md">
                <QuickActions />
                <StatusHistory />
                <AffectedServices
                  services={
                    incidentDetails?.affected_services?.map(serviceName => ({
                      id: serviceName,
                      name: serviceName,
                      status: 'Operational' as const,
                      details: [],
                      impact: 'Medium' as const,
                      dependencies: [],
                    })) || []
                  }
                />
                <AssignedUsers users={incident.assignedUsers} />
              </Stack>
            </Grid.Col>
          </Grid>
        </Stack>
      </Grid.Col>
    </Grid>
  );
};

export default IncidentOverview;

import {
  ActionIcon,
  Avatar,
  Badge,
  Flex,
  Group,
  Paper,
  Stack,
  Text,
  Title,
  Tooltip,
} from '@mantine/core';
import { AlertTriangle, Clock, Copy, ExternalLink, User } from 'lucide-react';
import React from 'react';
import { useLocation } from 'react-router';
import BreadcrumbNavigation from '../BreadcrumbNavigation';
import { Incident } from '../../types/IncidentType';
import { SEVERITY, STATUS } from '../../constants/types';
import {
  SEVERITY_COLORS,
  STATUS_COLORS,
  UI_COLORS,
} from '../../constants/colors';
import { generateIncidentBreadcrumbs } from '../../utils/breadcrumbUtils';

interface IncidentHeaderProps {
  incident: Incident;
}

const IncidentHeader: React.FC<IncidentHeaderProps> = ({ incident }) => {
  const location = useLocation();

  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case SEVERITY.CRITICAL:
        return SEVERITY_COLORS.CRITICAL.FILLED;
      case SEVERITY.HIGH:
        return SEVERITY_COLORS.HIGH.FILLED;
      case SEVERITY.MEDIUM:
        return SEVERITY_COLORS.MEDIUM.FILLED;
      case SEVERITY.LOW:
        return SEVERITY_COLORS.LOW.FILLED;
      default:
        return UI_COLORS.MUTED;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case STATUS.OPEN:
        return STATUS_COLORS.OPEN.FILLED;
      case STATUS.ACKNOWLEDGED:
        return STATUS_COLORS.ACKNOWLEDGED.FILLED;
      case STATUS.ACTIVE:
        return STATUS_COLORS.ACTIVE.FILLED;
      case STATUS.RESOLVED:
        return STATUS_COLORS.RESOLVED.FILLED;
      case STATUS.CLOSED:
        return STATUS_COLORS.CLOSED.FILLED;
      default:
        return UI_COLORS.MUTED;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const copyIncidentId = () => {
    navigator.clipboard.writeText(incident.id);
  };

  const getReporterDisplay = () => {
    return (
      `${incident.reporter?.first_name} ${incident.reporter?.last_name}` ||
      'Unknown'
    );
  };

  const breadcrumbItems = generateIncidentBreadcrumbs(
    incident,
    location.pathname,
  );

  return (
    <Paper p="lg" radius="md" withBorder shadow="sm">
      <Stack gap="md">
        {/* Breadcrumbs */}
        <BreadcrumbNavigation items={breadcrumbItems} />

        {/* Title and ID */}
        <Group align="center" gap="md">
          <Title ta="left" order={1} size="h2" c={UI_COLORS.PRIMARY}>
            {incident.title}
          </Title>
          <Group gap="xs">
            <Tooltip label="Copy incident ID">
              <ActionIcon variant="subtle" size="sm" onClick={copyIncidentId}>
                <Copy size={14} />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Open in new tab">
              <ActionIcon variant="subtle" size="sm">
                <ExternalLink size={14} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>

        <Flex justify="space-between" align="flex-start" wrap="wrap" gap="md">
          {/* Left side - Meta information */}
          <Stack gap="sm">
            <Group gap="sm" align="center">
              <Clock size={16} color="gray" />
              <Text size="sm" c={UI_COLORS.MUTED}>
                Reported
              </Text>
              <Text size="sm" fw={500}>
                {formatDate(incident.reported_at)}
              </Text>
              <Text size="sm" c="dimmed">
                by
              </Text>
              <Group gap="xs">
                {typeof incident.reporter !== 'string' && (
                  <Avatar
                    size="xs"
                    name={`${incident.reporter.first_name} ${incident.reporter.last_name}`}
                  />
                )}
                <Text size="sm" fw={500}>
                  {getReporterDisplay()}
                </Text>
              </Group>
            </Group>

            {/* Priority and Severity */}
            <Group gap="sm">
              <Group gap="xs">
                <AlertTriangle size={16} color="gray" />
                <Text size="sm" c="dimmed">
                  Priority:
                </Text>
                <Badge color={UI_COLORS.ERROR} variant="filled" size="sm">
                  {incident.priority}
                </Badge>
              </Group>
              <Group gap="xs">
                <Text size="sm" c="dimmed">
                  Severity:
                </Text>
                <Badge
                  color={getSeverityColor(incident.severity)}
                  variant="filled"
                  size="sm"
                >
                  {incident.severity}
                </Badge>
              </Group>
            </Group>

            {/* Assigned Users */}
            {incident.assignedUsers.length > 0 && (
              <Group gap="xs" align="center">
                <User size={16} color="gray" />
                <Text size="sm" c="dimmed">
                  Assigned to:
                </Text>
                <Avatar.Group spacing="xs">
                  {incident.assignedUsers.slice(0, 3).map(user => (
                    <Tooltip
                      key={user.id}
                      label={`${user.first_name} ${user.last_name} (${user.role})`}
                    >
                      <Avatar
                        size="sm"
                        name={`${user.first_name} ${user.last_name}`}
                      />
                    </Tooltip>
                  ))}
                  {incident.assignedUsers.length > 3 && (
                    <Avatar size="sm">
                      +{incident.assignedUsers.length - 3}
                    </Avatar>
                  )}
                </Avatar.Group>
              </Group>
            )}
          </Stack>

          {/* Right side - Status and Tags */}
          <Stack align="flex-end" gap="sm">
            {/* Status */}
            <Group gap="xs">
              <Text size="sm" c="dimmed">
                Status:
              </Text>
              <Badge
                color={getStatusColor(incident.status)}
                variant="light"
                size="lg"
                styles={{
                  root: {
                    textTransform: 'none',
                    fontWeight: 600,
                  },
                }}
              >
                {incident.status.toUpperCase()}
              </Badge>
            </Group>

            {/* Tags */}
            {incident.tags.length > 0 && (
              <Group gap="xs" justify="flex-end">
                {incident.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" size="sm" color="blue">
                    {tag}
                  </Badge>
                ))}
              </Group>
            )}
          </Stack>
        </Flex>
      </Stack>
    </Paper>
  );
};

export default IncidentHeader;

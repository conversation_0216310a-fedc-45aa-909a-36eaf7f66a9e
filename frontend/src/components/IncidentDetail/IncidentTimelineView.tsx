import {
  Box,
  Card,
  Group,
  Loader,
  Paper,
  Stack,
  Text,
  Timeline,
  Badge,
  Divider,
  Avatar,
  ScrollArea,
} from '@mantine/core';
import { AlertTriangle, CheckCircle, Clock, Search } from 'lucide-react';
import React, { useState } from 'react';
import { useParams } from 'react-router';
import { IncidentEvent, EventType } from '../../types/IncidentTimelineType';
import { queryIncidentEvents } from '../../hooks/useApi';
import IncidentDetailLayout from './IncidentDetailLayout';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { getAvatar } from '../../constants/avatars';

dayjs.extend(relativeTime);

// Helper functions
const getEventIcon = (type: EventType) => {
  switch (type) {
    case 'CREATED':
      return <AlertTriangle size={16} color="orange" />;
    case 'UPDATED':
      return <Clock size={16} color="blue" />;
    case 'ANALYSIS':
      return <Search size={16} color="purple" />;
    case 'RESOLVED':
      return <CheckCircle size={16} color="green" />;
    default:
      return <Clock size={16} />;
  }
};

const getEventColor = (type: EventType) => {
  switch (type) {
    case 'CREATED':
      return 'orange';
    case 'UPDATED':
      return 'blue';
    case 'ANALYSIS':
      return 'purple';
    case 'RESOLVED':
      return 'green';
    default:
      return 'gray';
  }
};

interface TimelineItemProps {
  event: IncidentEvent;
  isSelected: boolean;
  onSelect: () => void;
}

const TimelineItem: React.FC<TimelineItemProps> = ({
  event,
  isSelected,
  onSelect,
}) => {
  return (
    <Timeline.Item
      bullet={getEventIcon(event.event_type)}
      title={
        <Group gap="xs">
          <Text fw={500} size="sm">
            {event.event_name}
          </Text>
          <Badge
            color={getEventColor(event.event_type)}
            variant="light"
            size="xs"
          >
            {event.event_type}
          </Badge>
        </Group>
      }
    >
      <Card
        shadow="sm"
        padding="md"
        radius="md"
        withBorder
        style={{
          cursor: 'pointer',
          backgroundColor: isSelected
            ? 'var(--mantine-color-blue-0)'
            : undefined,
          borderColor: isSelected ? 'var(--mantine-color-blue-3)' : undefined,
        }}
        onClick={onSelect}
      >
        <Text size="sm" c="dimmed" mb="xs">
          {event.event_details.description}
        </Text>

        <Group justify="space-between" mt="md">
          <Group gap="xs">
            <Avatar
              size={28}
              src={getAvatar(
                `${event.user?.first_name}+${event.user?.last_name}`,
              )}
              radius="xl"
            />
            <Text size="xs" c="dimmed">
              {event.user.first_name} {event.user.last_name}
            </Text>
          </Group>
          <Text size="xs" c="dimmed">
            {dayjs(event.event_datetime).fromNow()}
          </Text>
        </Group>
      </Card>
    </Timeline.Item>
  );
};

const EventDetailsPanel = ({ event }: { event: IncidentEvent }) => {
  return (
    <Stack gap="md">
      <Group align="center" gap="md">
        {getEventIcon(event.event_type)}
        <div>
          <Text fw={700} size="lg">
            {event.event_name}
          </Text>
          <Text size="sm" c="dimmed">
            {dayjs(event.event_datetime).format('MMM DD, YYYY HH:mm')}
          </Text>
        </div>
      </Group>

      <Divider />

      <Box>
        <Text fw={600} mb="sm">
          Description
        </Text>
        <Text size="sm">{event.event_details.description}</Text>
      </Box>

      {event.event_details.alert_details && (
        <Box>
          <Text fw={600} mb="sm">
            Alert Details
          </Text>
          <Stack gap="xs">
            {Object.entries(event.event_details.alert_details).map(
              ([key, value]) =>
                value ? (
                  <Group key={key} gap="xs">
                    <Text size="sm" fw={500} style={{ minWidth: 80 }}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}:
                    </Text>
                    <Text size="sm">{value}</Text>
                  </Group>
                ) : null,
            )}
          </Stack>
        </Box>
      )}

      {event.event_details.analysis_details && (
        <Box>
          <Text fw={600} mb="sm">
            Analysis Details
          </Text>
          <Stack gap="xs">
            {Object.entries(event.event_details.analysis_details).map(
              ([key, value]) =>
                value && typeof value === 'string' ? (
                  <Group key={key} gap="xs">
                    <Text size="sm" fw={500} style={{ minWidth: 80 }}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}:
                    </Text>
                    <Text size="sm">{value}</Text>
                  </Group>
                ) : Array.isArray(value) ? (
                  <Box key={key}>
                    <Text size="sm" fw={500} mb="xs">
                      {key.charAt(0).toUpperCase() + key.slice(1)}:
                    </Text>
                    <Stack gap="xs" ml="md">
                      {value.map((item, idx) => (
                        <Text key={idx} size="sm">
                          • {item}
                        </Text>
                      ))}
                    </Stack>
                  </Box>
                ) : null,
            )}
          </Stack>
        </Box>
      )}
    </Stack>
  );
};

const IncidentTimelineView = () => {
  const { incidentId } = useParams<{ incidentId: string }>();
  if (!incidentId) {
    return <div>Incident ID not found</div>;
  }
  const [selectedEventIndex, setSelectedEventIndex] = useState<number | null>(
    null,
  );

  const { data: timelineEvents, isLoading } = queryIncidentEvents(
    incidentId || '',
    { skip: 0, limit: 100, sort: 'asc' },
  );

  const events = timelineEvents?.items ?? [];

  const selectEvent = (index: number) => {
    setSelectedEventIndex(index === selectedEventIndex ? null : index);
  };

  const selectedEvent =
    selectedEventIndex !== null ? events[selectedEventIndex] : null;

  if (isLoading) {
    return (
      <IncidentDetailLayout incidentId={incidentId}>
        <Group justify="center" p="xl">
          <Loader size="lg" />
          <Text>Loading timeline...</Text>
        </Group>
      </IncidentDetailLayout>
    );
  }

  return (
    <IncidentDetailLayout incidentId={incidentId}>
      <div className="flex gap-4 h-[calc(100vh-200px)]">
        {/* Timeline Panel */}
        <Paper p="md" radius="md" className="w-1/2 overflow-hidden" withBorder>
          <Group justify="space-between" mb="md">
            <Text fw={600} size="lg">
              Timeline Events
            </Text>
            <Badge variant="light" size="sm">
              {timelineEvents?.total || 0} total
            </Badge>
          </Group>

          <ScrollArea h="100%" offsetScrollbars>
            <Timeline active={-1} bulletSize={24} lineWidth={2}>
              {events.map((event: IncidentEvent, index: number) => (
                <TimelineItem
                  key={event.id}
                  event={event}
                  isSelected={selectedEventIndex === index}
                  onSelect={() => selectEvent(index)}
                />
              ))}
            </Timeline>
            <div style={{ height: '50px' }}></div>
          </ScrollArea>
        </Paper>

        {/* Details Panel */}
        <Paper p="xl" radius="md" className="w-1/2" withBorder>
          {selectedEvent ? (
            <EventDetailsPanel event={selectedEvent} />
          ) : (
            <div className="h-full flex flex-col items-center justify-center text-center">
              <Search size={48} color="var(--mantine-color-gray-5)" />
              <Text fw={600} size="lg" mt="md" c="dimmed">
                No Event Selected
              </Text>
              <Text c="dimmed" size="sm" mt="xs">
                Select an event from the timeline to view details
              </Text>
            </div>
          )}
        </Paper>
      </div>
    </IncidentDetailLayout>
  );
};

export default IncidentTimelineView;

import { Badge, Card, Group, Stack, Text, Title } from '@mantine/core';

const ActionItems = () => {
  return (
    <Card padding="lg" withBorder>
      <Title order={4} mb="md">
        Action Items
      </Title>
      <Stack gap="sm">
        <Group justify="space-between" align="center">
          <Text size="sm">
            Implement comprehensive parameter validation tests
          </Text>
          <Badge color="red" size="sm">
            High Priority
          </Badge>
        </Group>
        <Group justify="space-between" align="center">
          <Text size="sm">Update CI/CD pipeline with edge case testing</Text>
          <Badge color="orange" size="sm">
            Medium Priority
          </Badge>
        </Group>
        <Group justify="space-between" align="center">
          <Text size="sm">Review and update incident response procedures</Text>
          <Badge color="blue" size="sm">
            Low Priority
          </Badge>
        </Group>
      </Stack>
    </Card>
  );
};

export default ActionItems;

import { Card, Text, ThemeIcon, Timeline, Title } from '@mantine/core';
import { Activity } from 'lucide-react';
import { TimelineItem } from './types';

interface IncidentTimelineProps {
  timelineData: TimelineItem[];
}

const IncidentTimeline = ({ timelineData }: IncidentTimelineProps) => {
  return (
    <Card padding="lg" withBorder>
      <Title order={4} mb="md">
        Incident Timeline
      </Title>
      <Timeline active={timelineData.length} bulletSize={24}>
        {timelineData.map((item, index) => (
          <Timeline.Item
            key={index}
            bullet={
              <ThemeIcon size={24} radius="xl" color="blue">
                <Activity size={14} />
              </ThemeIcon>
            }
            title={item.event}
          >
            <Text size="sm" c="dimmed">
              {item.time} UTC
            </Text>
          </Timeline.Item>
        ))}
      </Timeline>
    </Card>
  );
};

export default IncidentTimeline;

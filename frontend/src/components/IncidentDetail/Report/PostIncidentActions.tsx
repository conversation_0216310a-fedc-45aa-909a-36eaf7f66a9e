import { But<PERSON>, Card, Group, Text } from '@mantine/core';

const PostIncidentActions = () => {
  return (
    <Card padding="lg" withBorder>
      <Group justify="space-between" align="center">
        <div>
          <Text size="sm" fw={500}>
            Post-Incident Actions
          </Text>
          <Text size="sm" c="dimmed">
            Recommended follow-up actions based on incident analysis
          </Text>
        </div>
        <Group gap="sm">
          <Button size="sm" variant="light" color="blue">
            Schedule Post-Mortem
          </Button>
          <Button size="sm" variant="light" color="green">
            Update Runbooks
          </Button>
          <Button size="sm" variant="light" color="orange">
            Create Action Items
          </Button>
        </Group>
      </Group>
    </Card>
  );
};

export default PostIncidentActions;

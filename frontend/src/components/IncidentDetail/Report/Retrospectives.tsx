import { Card, Stack, Text, Title } from '@mantine/core';

const Retrospectives = () => {
  return (
    <Card padding="lg" withBorder>
      <Title order={4} mb="md">
        Retrospectives
      </Title>
      <Stack gap="sm">
        <div>
          <Text size="sm" fw={500} mb="xs" ta={'left'}>
            What went well:
          </Text>
          <Text size="sm" ta={'left'}>
            • Rapid detection through automated monitoring
          </Text>
          <Text size="sm" ta={'left'}>
            • Quick team notification and response
          </Text>
          <Text size="sm" ta={'left'}>
            • Effective rollback procedure
          </Text>
        </div>
        <div>
          <Text size="sm" fw={500} mb="xs" ta={'left'}>
            Areas for improvement:
          </Text>
          <Text size="sm" ta={'left'}>
            • Parameter validation testing coverage
          </Text>
          <Text size="sm" ta={'left'}>
            • Deployment validation procedures
          </Text>
          <Text size="sm" ta={'left'}>
            • Customer communication timing
          </Text>
        </div>
      </Stack>
    </Card>
  );
};

export default Retrospectives;

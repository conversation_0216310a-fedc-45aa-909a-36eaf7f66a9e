import { Clock, DollarSign, TrendingUp, Users } from 'lucide-react';
import { MetricCard } from './types';

export const getImpactMetrics = (): MetricCard[] => [
  {
    title: 'Total Downtime',
    value: `40 min`,
    change: -23,
    trend: 'down',
    color: 'red',
    icon: <Clock size={20} />,
  },
  {
    title: 'Affected Users',
    value: '0',
    change: -45,
    trend: 'down',
    color: 'blue',
    icon: <Users size={20} />,
  },
  {
    title: 'Error Rate Peak',
    value: `32%`,
    change: -67,
    trend: 'down',
    color: 'orange',
    icon: <TrendingUp size={20} />,
  },
  {
    title: 'Financial Impact',
    value: `1000`,
    change: 10,
    trend: 'stable',
    color: 'green',
    icon: <DollarSign size={20} />,
  },
];

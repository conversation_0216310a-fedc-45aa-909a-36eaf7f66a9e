import { <PERSON><PERSON>, Card, Stack, Text, Title } from '@mantine/core';
import { AlertTriangle } from 'lucide-react';

const RootCauseAnalysis = () => {
  return (
    <Card padding="lg" withBorder>
      <Title order={4} mb="md">
        Root Cause Analysis
      </Title>
      <Stack gap="sm">
        <Alert color="red" icon={<AlertTriangle size={16} />}>
          <Text size="sm">
            <strong>Primary Cause:</strong> Parameter validation logic
            introduced in paymentservice v3.2.0 was incorrectly rejecting valid
            requests missing optional txn_id parameter.
          </Text>
        </Alert>
        <Alert color="orange" icon={<AlertTriangle size={16} />}>
          <Text size="sm">
            <strong>Contributing Factor:</strong> Insufficient testing of
            parameter validation edge cases in the CI/CD pipeline.
          </Text>
        </Alert>
      </Stack>
    </Card>
  );
};

export default RootCauseAnalysis;

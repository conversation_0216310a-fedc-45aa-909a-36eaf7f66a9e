import { Grid, Paper, Stack, Tabs } from '@mantine/core';
import { BarChart3, Calendar, Eye, Zap } from 'lucide-react';
import { useState } from 'react';
import { useParams } from 'react-router';
import { queryIncidentMetrics } from '../../../hooks/useApi';
import ActionItems from './ActionItems';
import ChartsPlaceholder from './ChartsPlaceholder';
import ExecutiveSummary from './ExecutiveSummary';
import ImpactMetrics from './ImpactMetrics';
import IncidentTimeline from './IncidentTimeline';
import { timelineData } from './mockData';
import PostIncidentActions from './PostIncidentActions';
import ReportHeader from './ReportHeader';
import ResponseMetricsTable from './ResponseMetricsTable';
import Retrospectives from './Retrospectives';
import RootCauseAnalysis from './RootCauseAnalysis';
import ServiceHealthChart from './ServiceHealthChart';
import { getImpactMetrics } from './utils';

const IncidentReport = () => {
  const { incidentId } = useParams<{ incidentId: string }>();
  const [activeTab, setActiveTab] = useState<string | null>('overview');

  // Handle missing incidentId
  if (!incidentId) {
    return (
      <Paper p="lg" radius="md" withBorder>
        <Stack align="center" gap="md">
          <div>Error: Incident ID is missing</div>
          <div>Please select a valid incident.</div>
        </Stack>
      </Paper>
    );
  }

  // Fetch incident metrics from API
  const { data: metric } = queryIncidentMetrics(incidentId);

  const generateReport = () => {
    // Mock report generation
    console.log('Generating comprehensive incident report...');
  };

  return (
    <Stack gap="lg">
      <Paper p="lg" radius="md" withBorder>
        <ReportHeader
          incidentId={incidentId}
          onGenerateReport={generateReport}
        />

        <Tabs value={activeTab} onChange={setActiveTab} color="purple">
          <Tabs.List>
            <Tabs.Tab value="overview" leftSection={<Eye size={16} />}>
              Overview
            </Tabs.Tab>
            <Tabs.Tab value="metrics" leftSection={<BarChart3 size={16} />}>
              Metrics
            </Tabs.Tab>
            <Tabs.Tab value="timeline" leftSection={<Calendar size={16} />}>
              Timeline
            </Tabs.Tab>
            <Tabs.Tab value="analysis" leftSection={<Zap size={16} />}>
              Analysis
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="overview" pt="md">
            <Stack gap="md">
              <ExecutiveSummary />
              <ImpactMetrics metrics={getImpactMetrics()} />
              <PostIncidentActions />
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="metrics" pt="md">
            <Grid>
              <Grid.Col span={6}>
                <ResponseMetricsTable metric={metric || null} />
              </Grid.Col>
              <Grid.Col span={6}>
                <ServiceHealthChart />
              </Grid.Col>
              <Grid.Col span={12}>
                <ChartsPlaceholder />
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="timeline" pt="md">
            <IncidentTimeline timelineData={timelineData} />
          </Tabs.Panel>

          <Tabs.Panel value="analysis" pt="md">
            <Stack gap="md">
              <RootCauseAnalysis />
              <Retrospectives />
              <ActionItems />
            </Stack>
          </Tabs.Panel>
        </Tabs>
      </Paper>
    </Stack>
  );
};

export default IncidentReport;

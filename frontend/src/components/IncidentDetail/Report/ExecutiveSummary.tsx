import { Card, Grid, List, Stack, Text, Title } from '@mantine/core';

const ExecutiveSummary = () => {
  return (
    <Card padding="lg" withBorder>
      <Title order={3} mb="md">
        Executive Summary
      </Title>
      <Text mb="md" ta="left">
        On May 13, 2025, at 05:55 UTC, the Payment API experienced a critical
        failure due to parameter validation issues introduced in deployment
        v3.2.0. The incident affected 1,250 users and resulted in 35 minutes of
        downtime with an estimated financial impact of $15,000.
      </Text>
      <Grid>
        <Grid.Col span={6}>
          <Stack gap="xs">
            <Text size="sm" fw={500}>
              Key Metrics:
            </Text>
            <List size="sm">
              <List.Item>Duration: 35 minutes</List.Item>
              <List.Item>Users Affected: 1,250</List.Item>
              <List.Item>Peak Error Rate: 85.6%</List.Item>
              <List.Item>Financial Impact: $15,000</List.Item>
            </List>
          </Stack>
        </Grid.Col>
        <Grid.Col span={6}>
          <Stack gap="xs">
            <Text size="sm" fw={500}>
              Resolution:
            </Text>
            <List>
              <List.Item>Root Cause: Parameter validation bug</List.Item>
              <List.Item>Resolution: Service rollback</List.Item>
              <List.Item>Status: Fully resolved</List.Item>
              <List.Item>Prevention: Updated CI/CD checks</List.Item>
            </List>
          </Stack>
        </Grid.Col>
      </Grid>
    </Card>
  );
};

export default ExecutiveSummary;

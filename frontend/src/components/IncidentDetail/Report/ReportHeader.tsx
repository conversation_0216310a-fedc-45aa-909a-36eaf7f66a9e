import { Badge, Button, Group, Title } from '@mantine/core';
import { BarChart3, Download, FileText, Share } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { generateAndDownloadPdfReport } from '../../../api/reportApi';

interface ReportHeaderProps {
  incidentId: string;
  onGenerateReport: () => void;
}

const ReportHeader = ({ incidentId, onGenerateReport }: ReportHeaderProps) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleExportPdf = async () => {
    setIsDownloading(true);
    try {
      await generateAndDownloadPdfReport(incidentId);
      toast.success('Incident report PDF has been downloaded successfully');
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      toast.error('Failed to generate PDF report. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Group justify="space-between" align="center" mb="md">
      <Group align="center" gap="sm">
        <BarChart3 size={24} color="purple" />
        <Title order={2}>Incident Report & Analysis</Title>
        <Badge color="purple" variant="light">
          Auto-generated with AI
        </Badge>
      </Group>
      <Group gap="sm">
        <Button
          leftSection={<Download size={16} />}
          size="sm"
          variant="light"
          onClick={handleExportPdf}
          loading={isDownloading}
          disabled={isDownloading}
        >
          {isDownloading ? 'Generating...' : 'Export PDF'}
        </Button>
        <Button leftSection={<Share size={16} />} size="sm" variant="light">
          Share Report
        </Button>
        <Button
          leftSection={<FileText size={16} />}
          size="sm"
          onClick={onGenerateReport}
        >
          Regenerate Report
        </Button>
      </Group>
    </Group>
  );
};

export default ReportHeader;

import { Card, Grid, Group, Text, ThemeIcon } from '@mantine/core';
import { Activity, TrendingDown, TrendingUp } from 'lucide-react';
import { MetricCard } from './types';

interface ImpactMetricsProps {
  metrics: MetricCard[];
}

const ImpactMetrics = ({ metrics }: ImpactMetricsProps) => {
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={16} color="red" />;
      case 'down':
        return <TrendingDown size={16} color="green" />;
      default:
        return <Activity size={16} color="gray" />;
    }
  };

  return (
    <Grid>
      {metrics.map(metric => (
        <Grid.Col key={metric.title} span={{ base: 12, sm: 6, md: 3 }}>
          <Card padding="lg" withBorder>
            <Group justify="space-between" align="flex-start">
              <div>
                <Text size="sm" c="dimmed">
                  {metric.title}
                </Text>
                <Text size="xl" fw={700} c={metric.color}>
                  {metric.value}
                </Text>
                {metric.change !== undefined && (
                  <Group gap="xs" align="center">
                    {getTrendIcon(metric.trend!)}
                    <Text
                      size="sm"
                      c={metric.trend === 'down' ? 'green' : 'red'}
                    >
                      {Math.abs(metric.change)}%
                    </Text>
                  </Group>
                )}
              </div>
              <ThemeIcon size="lg" color={metric.color} variant="light">
                {metric.icon}
              </ThemeIcon>
            </Group>
          </Card>
        </Grid.Col>
      ))}
    </Grid>
  );
};

export default ImpactMetrics;

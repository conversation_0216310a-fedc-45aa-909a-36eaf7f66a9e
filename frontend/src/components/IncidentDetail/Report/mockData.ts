import { TimelineItem } from './types';

export const timelineData: TimelineItem[] = [
  { time: '05:55', event: 'Incident Detected', type: 'detection' },
  { time: '06:05', event: 'Team Notified', type: 'notification' },
  { time: '06:15', event: 'Investigation Started', type: 'response' },
  { time: '06:30', event: 'Root Cause Identified', type: 'analysis' },
  { time: '06:45', event: 'Fix Deployed', type: 'resolution' },
  { time: '07:00', event: 'Service Restored', type: 'recovery' },
];

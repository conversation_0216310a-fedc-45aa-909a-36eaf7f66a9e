import { Card, Center, RingProgress, Text, Title } from '@mantine/core';

const ServiceHealthChart = () => {
  return (
    <Card padding="lg" withBorder>
      <Title order={4} mb="md">
        Service Health
      </Title>
      <Center>
        <RingProgress
          size={150}
          thickness={12}
          sections={[
            {
              value: 15,
              color: 'red',
              tooltip: 'Critical services',
            },
            {
              value: 25,
              color: 'yellow',
              tooltip: 'Degraded services',
            },
            {
              value: 60,
              color: 'green',
              tooltip: 'Healthy services',
            },
          ]}
          label={
            <div style={{ textAlign: 'center' }}>
              <Text size="xs" c="dimmed">
                Overall
              </Text>
              <Text fw={700}>Health</Text>
            </div>
          }
        />
      </Center>
    </Card>
  );
};

export default ServiceHealthChart;

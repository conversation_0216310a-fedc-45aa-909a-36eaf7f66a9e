import { Card, Text, Title } from '@mantine/core';

const ChartsPlaceholder = () => {
  return (
    <Card padding="lg" withBorder>
      <Title order={4} mb="md">
        Charts
      </Title>
      <Text size="sm" c="dimmed" mb="md">
        Charts goes here
      </Text>

      <div
        style={{
          height: 200,
          backgroundColor: '#f8f9fa',
          borderRadius: 8,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Text c="dimmed"> Chart Placeholder</Text>
      </div>
    </Card>
  );
};

export default ChartsPlaceholder;

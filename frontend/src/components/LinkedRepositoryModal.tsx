import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Button,
  Group,
  Modal,
  Stack,
  Text,
  Title,
  Card,
  Tooltip,
  Loader,
} from '@mantine/core';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Github,
  RefreshCw,
  Unlink,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { UI_COLORS } from '../constants/colors';
import { useSyncFromGithub, useJobStatus } from '../hooks/useApi';
import { getRunningJobs } from '../api/jobsApi';
import { Job, JobStatusEnum } from '../types/JobType';

interface LinkedRepositoryModalProps {
  opened: boolean;
  onClose: () => void;
  repoUrl: string;
  onUnlink: () => void;
}

const isJobFinished = (status: JobStatusEnum): boolean => {
  return [JobStatusEnum.SUCCESS, JobStatusEnum.FAILURE].includes(status);
};

const getJobStatusColor = (status: JobStatusEnum): string => {
  const statusMap: Record<JobStatusEnum, string> = {
    [JobStatusEnum.SUCCESS]: 'green',
    [JobStatusEnum.FAILURE]: 'red',
    [JobStatusEnum.PENDING]: 'blue',
    [JobStatusEnum.STARTED]: 'blue',
    [JobStatusEnum.RETRY]: 'yellow',
    [JobStatusEnum.REVOKED]: 'gray',
    [JobStatusEnum.IGNORED]: 'gray',
  };
  return statusMap[status] || 'gray';
};

const getJobStatusIcon = (status: JobStatusEnum): React.ReactNode => {
  const statusIconMap: Record<JobStatusEnum, React.ReactNode> = {
    [JobStatusEnum.SUCCESS]: <CheckCircle size={16} />,
    [JobStatusEnum.FAILURE]: <AlertCircle size={16} />,
    [JobStatusEnum.PENDING]: <Clock size={16} />,
    [JobStatusEnum.STARTED]: <RefreshCw size={16} className="animate-spin" />,
    [JobStatusEnum.RETRY]: <RefreshCw size={16} />,
    [JobStatusEnum.REVOKED]: <AlertCircle size={16} />,
    [JobStatusEnum.IGNORED]: <Clock size={16} />,
  };
  return statusIconMap[status] || <Clock size={16} />;
};

const LinkedRepositoryModal = ({
  opened,
  onClose,
  repoUrl,
  onUnlink,
}: LinkedRepositoryModalProps) => {
  const [syncJob, setSyncJob] = useState<Job | null>(null);
  const [lastSyncTime, setLastSyncTime] = useState<string | null>(null);
  const [currentRunningJob, setCurrentRunningJob] = useState<Job | null>(null);

  const queryClient = useQueryClient();
  const syncMutation = useSyncFromGithub();

  const shouldPollSyncJobStatus = !!syncJob && !isJobFinished(syncJob.status);
  const syncJobStatusQuery = useJobStatus(
    syncJob?.job_id || '',
    shouldPollSyncJobStatus,
  );

  const shouldPollRunningJobStatus =
    !!currentRunningJob && !isJobFinished(currentRunningJob.status);
  const runningJobStatusQuery = useJobStatus(
    currentRunningJob?.job_id || '',
    shouldPollRunningJobStatus,
  );

  // Update sync job status when polling receives updates
  useEffect(() => {
    if (!syncJobStatusQuery.data || !syncJob) return;

    const jobStatus = syncJobStatusQuery.data;
    if (
      syncJob.status !== jobStatus.status ||
      syncJob.message !== jobStatus.message
    ) {
      const updatedJob: Job = {
        ...syncJob,
        status: jobStatus.status,
        message: jobStatus.message,
      };

      setSyncJob(updatedJob);

      if (isJobFinished(jobStatus.status)) {
        if (jobStatus.status === JobStatusEnum.SUCCESS) {
          setLastSyncTime(new Date().toISOString());

          // Invalidate incidents query to refresh the list
          queryClient.invalidateQueries({ queryKey: ['incidents'] });
        }

        // Clear sync job after showing result
        setTimeout(() => {
          setSyncJob(null);
        }, 3000);
      }
    }
  }, [syncJobStatusQuery.data, syncJob, queryClient]);

  // Update running job status when polling receives updates
  useEffect(() => {
    if (!runningJobStatusQuery.data || !currentRunningJob) return;

    const jobStatus = runningJobStatusQuery.data;
    if (
      currentRunningJob.status !== jobStatus.status ||
      currentRunningJob.message !== jobStatus.message
    ) {
      const updatedJob: Job = {
        ...currentRunningJob,
        status: jobStatus.status,
        message: jobStatus.message,
      };

      setCurrentRunningJob(updatedJob);

      if (isJobFinished(jobStatus.status)) {
        if (jobStatus.status === JobStatusEnum.SUCCESS) {
          setLastSyncTime(new Date().toISOString());
          // Store last sync time in localStorage
          localStorage.setItem(`lastSync_${repoUrl}`, new Date().toISOString());

          // Invalidate incidents query to refresh the list
          queryClient.invalidateQueries({ queryKey: ['incidents'] });
        }

        // Clear running job after showing result
        setTimeout(() => {
          setCurrentRunningJob(null);
        }, 3000);
      }
    }
  }, [runningJobStatusQuery.data, currentRunningJob, repoUrl, queryClient]);

  // Check for running jobs when modal opens
  useEffect(() => {
    if (opened) {
      const fetchRunningJobs = async () => {
        try {
          const response = await getRunningJobs();
          if (response.items?.length > 0) {
            const githubJob = response.items.find(
              (job: Job) =>
                job.job_type === 'github_import' ||
                job.job_type === 'github_sync',
            );
            if (githubJob) {
              setCurrentRunningJob(githubJob);
            }
          }
        } catch (error) {
          console.error('Failed to fetch running jobs:', error);
        }
      };

      fetchRunningJobs();
    }
  }, [opened]);

  // Reset state when modal closes
  useEffect(() => {
    if (!opened) {
      setSyncJob(null);
      setCurrentRunningJob(null);
    }
  }, [opened]);

  // Load last sync time from localStorage
  useEffect(() => {
    if (opened && repoUrl) {
      const lastSync = localStorage.getItem(`lastSync_${repoUrl}`);
      if (lastSync) {
        setLastSyncTime(lastSync);
      }
    }
  }, [opened, repoUrl]);

  const handleSync = useCallback(async () => {
    try {
      const job = await syncMutation.mutateAsync({ githubRepo: repoUrl });
      setSyncJob(job);
    } catch (error) {
      console.error('Sync failed:', error);
    }
  }, [syncMutation, repoUrl]);

  const handleUnlink = useCallback(() => {
    // Remove from localStorage
    localStorage.removeItem(`linkedRepo`);
    localStorage.removeItem(`lastSync_${repoUrl}`);
    onUnlink();
    onClose();
  }, [repoUrl, onUnlink, onClose]);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Title order={3} fw={600}>
          Linked Repository
        </Title>
      }
      size="md"
      centered
    >
      <Stack gap="lg">
        {/* Repository Info */}
        <Card withBorder padding="md" radius="md">
          <Group justify="space-between" align="center">
            <Group gap="sm">
              <Github size={24} color={UI_COLORS.PRIMARY} />
              <Stack gap={2}>
                <Text fw={600} size="sm">
                  GitHub Repository
                </Text>
                <Text size="sm" c="dimmed">
                  {repoUrl}
                </Text>
              </Stack>
            </Group>
            <Badge variant="light" color="green" size="sm">
              Connected
            </Badge>
          </Group>
        </Card>

        {/* Last Sync Info */}
        {lastSyncTime && (
          <Group justify="space-between">
            <Text size="sm" fw={500}>
              Last Synced:
            </Text>
            <Text size="sm" c="dimmed">
              {new Date(lastSyncTime).toLocaleString()}
            </Text>
          </Group>
        )}

        {/* Job Status */}
        {(currentRunningJob || syncJob) && (
          <Alert
            icon={getJobStatusIcon((currentRunningJob || syncJob)!.status)}
            color={getJobStatusColor((currentRunningJob || syncJob)!.status)}
            variant="light"
          >
            <Group justify="space-between">
              <Stack gap={2}>
                <Text size="sm" fw={500}>
                  {(currentRunningJob || syncJob)!.job_type === 'github_import'
                    ? 'Import'
                    : 'Sync'}{' '}
                  Status:{' '}
                  {(currentRunningJob || syncJob)!.status
                    .charAt(0)
                    .toUpperCase() +
                    (currentRunningJob || syncJob)!.status.slice(1)}
                </Text>
                {(currentRunningJob || syncJob)!.message && (
                  <Text size="xs" c="dimmed">
                    {(currentRunningJob || syncJob)!.message}
                  </Text>
                )}
              </Stack>
              {!isJobFinished((currentRunningJob || syncJob)!.status) && (
                <Loader size="sm" />
              )}
            </Group>
          </Alert>
        )}

        {/* Actions */}
        <Group justify="space-between" mt="md">
          <Tooltip label="Disconnect this repository">
            <Button
              leftSection={<Unlink size={16} />}
              variant="subtle"
              color="red"
              onClick={handleUnlink}
            >
              Unlink Repository
            </Button>
          </Tooltip>

          <Button
            leftSection={<RefreshCw size={16} />}
            onClick={handleSync}
            disabled={
              syncMutation.isPending ||
              (currentRunningJob
                ? !isJobFinished(currentRunningJob.status)
                : false) ||
              (syncJob ? !isJobFinished(syncJob.status) : false)
            }
            color={UI_COLORS.PRIMARY}
          >
            {syncMutation.isPending ||
            (currentRunningJob
              ? !isJobFinished(currentRunningJob.status)
              : false) ||
            (syncJob ? !isJobFinished(syncJob.status) : false)
              ? currentRunningJob?.job_type === 'github_import'
                ? 'Processing Import...'
                : 'Processing Sync...'
              : 'Sync Now'}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

export default LinkedRepositoryModal;

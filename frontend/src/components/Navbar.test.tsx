import { render, screen } from '../utils/test-utils';
import { Navbar } from './Navbar';

// Mock the entire AuthContext module
jest.mock('../contexts/AuthContext', () => ({
  useAuth: jest.fn(() => ({
    logout: jest.fn(),
    isLoading: false,
    user: {
      id: '1',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
    },
  })),
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}));

describe('Navbar', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('renders loading state', () => {
    const { useAuth } = require('../contexts/AuthContext');
    useAuth.mockReturnValue({
      logout: jest.fn(),
      isLoading: true,
      user: null,
    });

    render(<Navbar />);
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  it('renders user information when data is loaded', () => {
    const { useAuth } = require('../contexts/AuthContext');
    useAuth.mockReturnValue({
      logout: jest.fn(),
      isLoading: false,
      user: {
        id: '1',
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
      },
    });

    render(<Navbar />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});

import {
  ActionIcon,
  Card,
  Group,
  Menu,
  Stack,
  Text,
  Tooltip,
} from '@mantine/core';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {
  Check,
  Clock,
  Edit,
  FolderOpen,
  MoreVertical,
  Plus,
  Trash2,
} from 'lucide-react';
import React from 'react';
import { ProjectListItem } from '../../types/KnowledgeBaseTypes';

interface ProjectCardProps {
  project: ProjectListItem;
  onEdit?: (project: ProjectListItem) => void;
  onDelete?: (project: ProjectListItem) => void;
  onViewKnowledgeBase?: (project: ProjectListItem) => void;
  isSelected?: boolean;
  onSelect?: (project: ProjectListItem) => void;
}

dayjs.extend(relativeTime);

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onEdit,
  onDelete,
  onViewKnowledgeBase,
  isSelected = false,
  onSelect,
}) => {
  const handleMenuAction = (action: string, event: React.MouseEvent) => {
    event.stopPropagation();

    switch (action) {
      case 'select':
        onSelect?.(project);
        break;
      case 'edit':
        onEdit?.(project);
        break;
      case 'delete':
        onDelete?.(project);
        break;
      case 'view-kb':
        onViewKnowledgeBase?.(project);
        break;
    }
  };

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      className="cursor-pointer hover:shadow-md transition-shadow"
    >
      <Stack gap="sm">
        <Group justify="space-between" align="flex-start">
          <Group gap="sm" align="center">
            <FolderOpen size={20} className="text-blue-600" />
            <Text fw={600} size="lg" lineClamp={1}>
              {project.name}
            </Text>
          </Group>

          <Menu shadow="md" width={200} position="bottom-end">
            <Menu.Target>
              <ActionIcon
                variant="subtle"
                size="sm"
                onClick={e => e.stopPropagation()}
              >
                <MoreVertical size={16} />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              {!isSelected && (
                <Menu.Item
                  leftSection={<Check size={14} />}
                  onClick={e => handleMenuAction('select', e)}
                >
                  Select Project
                </Menu.Item>
              )}
              <Menu.Item
                leftSection={<Edit size={14} />}
                onClick={e => handleMenuAction('edit', e)}
              >
                Edit Project
              </Menu.Item>
              <Menu.Item
                leftSection={<Plus size={14} />}
                onClick={e => handleMenuAction('view-kb', e)}
              >
                View Knowledge Base
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                leftSection={<Trash2 size={14} />}
                color="red"
                onClick={e => handleMenuAction('delete', e)}
              >
                Delete Project
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>

        <Text size="sm" c="dimmed" lineClamp={2}>
          {project.description}
        </Text>

        <Group justify="space-between" align="center">
          <Group gap="xs">
            <Text size="xs" c="dimmed">
              {project.knowledge_base_count} knowledge base
              {project.knowledge_base_count !== 1 ? 's' : ''}
            </Text>
          </Group>
          {isSelected && (
            <Text size="sm" c="green" fw={500} mt="xs">
              Active Project
            </Text>
          )}
        </Group>
        <Tooltip
          label={dayjs(project.updated_at).format('YYYY-MM-DD HH:mm:ss')}
        >
          <Text size="xs" c="dimmed">
            <Clock size={14} className="inline-block mr-1" />
            Updated {dayjs(project.updated_at).fromNow()}
          </Text>
        </Tooltip>
      </Stack>
    </Card>
  );
};

export default ProjectCard;

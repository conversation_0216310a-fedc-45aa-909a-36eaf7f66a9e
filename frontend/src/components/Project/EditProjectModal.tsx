import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import {
  Button,
  Group,
  Modal,
  Stack,
  Textarea,
  TextInput,
} from '@mantine/core';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { ProjectListItem, ProjectUpdate } from '../../types/KnowledgeBaseTypes';

interface EditProjectModalProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (data: ProjectUpdate) => Promise<void>;
  project: ProjectListItem | null;
  loading?: boolean;
}

const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(255, 'Name too long'),
  description: z.string().min(1, 'Project description is required'),
});

type ProjectFormData = z.infer<typeof projectSchema>;

const EditProjectModal: React.FC<EditProjectModalProps> = ({
  opened,
  onClose,
  onSubmit,
  project,
  loading = false,
}) => {
  const { control, handleSubmit, formState, reset } = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  const { errors } = formState;

  // Reset form when project changes
  useEffect(() => {
    if (project) {
      reset({
        name: project.name,
        description: project.description,
      });
    }
  }, [project, reset]);

  const handleFormSubmit = async (data: ProjectFormData) => {
    try {
      await onSubmit(data);
      // Reset form after successful submission
      reset({
        name: '',
        description: '',
      });
    } catch (error) {
      // Error handling is done in the parent component
      throw error;
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal opened={opened} onClose={handleClose} title="Edit Project" size="md">
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <Stack gap="md">
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label="Project Name"
                placeholder="Enter project name"
                required
                error={errors.name?.message}
              />
            )}
          />

          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                label="Description"
                placeholder="Enter project description"
                required
                rows={4}
                error={errors.description?.message}
              />
            )}
          />

          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button
              type="submit"
              loading={loading}
              style={{ backgroundColor: 'var(--color-primary)' }}
            >
              Update Project
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
};

export default EditProjectModal;

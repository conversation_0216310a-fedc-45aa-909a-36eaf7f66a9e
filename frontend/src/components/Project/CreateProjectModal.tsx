import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import {
  Button,
  Group,
  Modal,
  Stack,
  Textarea,
  TextInput,
} from '@mantine/core';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { ProjectCreate } from '../../types/KnowledgeBaseTypes';

interface CreateProjectModalProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (data: ProjectCreate) => Promise<void>;
  loading?: boolean;
}

const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(255, 'Name too long'),
  description: z.string().min(1, 'Project description is required'),
});

type ProjectFormData = z.infer<typeof projectSchema>;

const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  opened,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const { control, handleSubmit, formState, reset } = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  const { errors } = formState;

  const handleFormSubmit = async (data: ProjectFormData) => {
    try {
      await onSubmit(data);
      // Reset form after successful submission
      reset({
        name: '',
        description: '',
      });
    } catch (error) {
      // Error handling is done in the parent component
      throw error;
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Create New Project"
      size="md"
    >
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <Stack gap="md">
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label="Project Name"
                placeholder="Enter project name"
                required
                error={errors.name?.message}
              />
            )}
          />

          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                label="Description"
                placeholder="Enter project description"
                required
                rows={4}
                error={errors.description?.message}
              />
            )}
          />

          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button
              type="submit"
              loading={loading}
              style={{ backgroundColor: 'var(--color-primary)' }}
            >
              Create Project
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
};

export default CreateProjectModal;

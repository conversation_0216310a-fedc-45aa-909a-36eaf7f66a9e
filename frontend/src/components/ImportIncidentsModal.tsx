import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Modal,
  SimpleGrid,
  Stack,
  Text,
  TextInput,
  Title,
} from '@mantine/core';
import { AlertCircle, Github, GitPullRequest, Settings } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { UI_COLORS } from '../constants/colors';
import { useImportFromGithub } from '../hooks/useApi';
import { Job } from '../types/JobType';

interface ImportIncidentsModalProps {
  opened: boolean;
  onClose: () => void;
  onImportStarted?: (job: Job, repoUrl: string) => void;
}

interface ConnectorCardProps {
  icon: React.ReactNode;
  name: string;
  description: string;
  isEnabled: boolean;
  onClick: () => void;
}

type ImportStep = 'select' | 'configure';

interface ImportState {
  step: ImportStep;
  selectedConnector: string | null;
  githubRepo: string;
  error: string | null;
}

interface Connector {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  isEnabled: boolean;
}

const ConnectorCard = ({
  icon,
  name,
  description,
  isEnabled,
  onClick,
}: ConnectorCardProps) => {
  return (
    <Card
      shadow="sm"
      padding="xl"
      radius="md"
      withBorder
      style={{
        cursor: isEnabled ? 'pointer' : 'not-allowed',
        opacity: isEnabled ? 1 : 0.6,
        transition: 'all 0.2s ease',
        borderColor: isEnabled
          ? 'var(--mantine-color-gray-3)'
          : 'var(--mantine-color-gray-2)',
      }}
      onClick={isEnabled ? onClick : undefined}
      bg={
        isEnabled ? 'var(--mantine-color-white)' : 'var(--mantine-color-gray-0)'
      }
      className={isEnabled ? 'hover:shadow-lg hover:border-blue-300' : ''}
    >
      <Stack gap="md" align="center" ta="center">
        <div
          style={{
            color: isEnabled ? UI_COLORS.PRIMARY : UI_COLORS.MUTED,
            padding: '8px',
            borderRadius: '50%',
            backgroundColor: isEnabled
              ? 'var(--mantine-color-blue-0)'
              : 'var(--mantine-color-gray-1)',
          }}
        >
          {icon}
        </div>
        <Stack gap="xs" align="center">
          <Stack gap="xs" align="center">
            <Text ta="center" fw={600} size="lg">
              {name}
            </Text>
            {!isEnabled && (
              <Badge variant="light" color={UI_COLORS.WARNING} size="sm">
                Coming Soon
              </Badge>
            )}
          </Stack>
          <Text size="sm" c="dimmed" ta="center" maw={250}>
            {description}
          </Text>
        </Stack>
      </Stack>
    </Card>
  );
};

const GITHUB_REPO_PATTERN = /^[a-zA-Z0-9._-]+\/[a-zA-Z0-9._-]+$/;

const CONNECTORS: Connector[] = [
  {
    id: 'github',
    name: 'GitHub',
    description: 'Import issues from GitHub repositories',
    icon: <Github size={32} />,
    isEnabled: true,
  },
  {
    id: 'jira',
    name: 'Jira',
    description: 'Import issues from Jira projects',
    icon: <GitPullRequest size={32} />,
    isEnabled: false,
  },
  {
    id: 'servicenow',
    name: 'ServiceNow',
    description: 'Import incidents from ServiceNow',
    icon: <Settings size={32} />,
    isEnabled: false,
  },
];

const isValidGithubRepo = (repo: string): boolean => {
  return GITHUB_REPO_PATTERN.test(repo);
};

const ImportIncidentsModal = ({
  opened,
  onClose,
  onImportStarted,
}: ImportIncidentsModalProps) => {
  const [state, setState] = useState<ImportState>({
    step: 'select',
    selectedConnector: null,
    githubRepo: '',
    error: null,
  });

  const importMutation = useImportFromGithub();

  const getErrorMessage = (error: unknown): string | null => {
    if (!error) return null;
    if (error instanceof Error) return error.message;
    return String(error);
  };

  const currentError = state.error || getErrorMessage(importMutation.error);

  useEffect(() => {
    if (!opened) {
      setState({
        step: 'select',
        selectedConnector: null,
        githubRepo: '',
        error: null,
      });
      importMutation.reset();
    }
  }, [opened]);

  const handleConnectorSelect = useCallback((connectorId: string) => {
    setState(prev => ({
      ...prev,
      step: 'configure',
      selectedConnector: connectorId,
      githubRepo: '',
      error: null,
    }));
  }, []);

  const handleBack = useCallback(() => {
    setState(prev => ({
      ...prev,
      step: 'select',
      selectedConnector: null,
      githubRepo: '',
      error: null,
    }));
  }, []);

  const handleGithubRepoChange = useCallback((value: string) => {
    setState(prev => ({ ...prev, githubRepo: value }));
  }, []);

  const handleImport = useCallback(async () => {
    if (!state.githubRepo || !isValidGithubRepo(state.githubRepo)) {
      return;
    }

    try {
      setState(prev => ({ ...prev, error: null }));
      const job = await importMutation.mutateAsync({
        githubRepo: state.githubRepo,
      });

      // Notify parent component and close modal immediately
      onImportStarted?.(job, state.githubRepo);
      onClose();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Import failed';
      setState(prev => ({ ...prev, error: errorMessage }));
    }
  }, [state.githubRepo, importMutation, onImportStarted, onClose]);

  const renderContent = () => {
    switch (state.step) {
      case 'select':
        return (
          <Stack gap="lg">
            <Text c="dimmed">
              Select a connector to import incidents from external sources
            </Text>

            <SimpleGrid cols={{ base: 1, sm: 3 }} spacing="lg">
              {CONNECTORS.map(connector => (
                <ConnectorCard
                  key={connector.id}
                  icon={connector.icon}
                  name={connector.name}
                  description={connector.description}
                  isEnabled={connector.isEnabled}
                  onClick={() => handleConnectorSelect(connector.id)}
                />
              ))}
            </SimpleGrid>
          </Stack>
        );

      case 'configure':
        return (
          <Stack gap="lg">
            {state.selectedConnector === 'github' && (
              <>
                <Group align="center" gap="sm">
                  <Github size={24} color={UI_COLORS.PRIMARY} />
                  <Text fw={600} size="lg">
                    GitHub Repository
                  </Text>
                </Group>

                <Text c="dimmed" size="sm">
                  Enter the GitHub repository in the format "owner/repository"
                  to import issues as incidents.
                </Text>

                {currentError && (
                  <Alert
                    icon={<AlertCircle size={16} />}
                    title="Error"
                    color="red"
                    variant="light"
                  >
                    {currentError}
                  </Alert>
                )}

                <TextInput
                  label="Repository"
                  placeholder="e.g., facebook/react"
                  value={state.githubRepo}
                  onChange={e => handleGithubRepoChange(e.target.value)}
                  error={
                    state.githubRepo && !isValidGithubRepo(state.githubRepo)
                      ? 'Please enter a valid repository format (owner/repo)'
                      : null
                  }
                  data-autofocus
                />

                <Group justify="space-between" mt="md">
                  <Button variant="subtle" onClick={handleBack}>
                    Back
                  </Button>
                  <Button
                    onClick={handleImport}
                    disabled={
                      !state.githubRepo ||
                      !isValidGithubRepo(state.githubRepo) ||
                      importMutation.isPending
                    }
                    loading={importMutation.isPending}
                    color={UI_COLORS.PRIMARY}
                  >
                    {importMutation.isPending
                      ? 'Starting Import...'
                      : 'Import Issues'}
                  </Button>
                </Group>
              </>
            )}
          </Stack>
        );

      default:
        return null;
    }
  };

  const getModalTitle = (): string => {
    const titleMap: Record<ImportStep, string> = {
      select: 'Import Incidents',
      configure: 'Configure Import',
    };
    return titleMap[state.step];
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Title order={3} fw={600}>
          {getModalTitle()}
        </Title>
      }
      size="lg"
      centered
    >
      {renderContent()}
    </Modal>
  );
};

export default ImportIncidentsModal;

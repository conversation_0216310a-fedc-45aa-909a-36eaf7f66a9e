import { Anchor, Breadcrumbs, Text } from '@mantine/core';
import React from 'react';
import { Link } from 'react-router';
import { BreadcrumbItem } from '../utils/breadcrumbUtils';

interface BreadcrumbNavigationProps {
  items: BreadcrumbItem[];
  separator?: string;
}

const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  items,
  separator = '>',
}) => {
  const breadcrumbElements = items.map((item, index) => {
    if (item.to) {
      return (
        <Anchor
          key={index}
          component={Link}
          to={item.to}
          size="sm"
          c="blue"
          style={{
            textDecoration: 'none',
            transition: 'all 0.2s ease',
          }}
          className="hover:underline hover:brightness-110"
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              // React Router Link will handle navigation
              e.currentTarget.click();
            }
          }}
          tabIndex={0}
        >
          {item.title}
        </Anchor>
      );
    } else {
      // Current page - not clickable
      return (
        <Text
          key={index}
          size="sm"
          c="dimmed"
          fw={500}
          style={{ userSelect: 'none' }}
        >
          {item.title}
        </Text>
      );
    }
  });

  return (
    <Breadcrumbs
      separator={separator}
      separatorMargin="xs"
      style={{ fontSize: '14px' }}
    >
      {breadcrumbElements}
    </Breadcrumbs>
  );
};

export default BreadcrumbNavigation;

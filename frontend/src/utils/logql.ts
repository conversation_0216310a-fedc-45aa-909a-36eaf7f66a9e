export const buildLokiQuery = ({
  job,
  selectedHostnames,
  selectedServices,
  filename,
  extraLabels = {},
  searchExpression,
  labelFormat = {},
}: {
  job?: string;
  selectedHostnames?: string[];
  selectedServices?: string[];
  filename?: string;
  extraLabels?: Record<string, string | string[]>;
  searchExpression?: string;
  labelFormat?: Record<string, string>;
}): string => {
  const conditions: string[] = [];

  if (job) {
    conditions.push(`job="${job}"`);
  }

  if (selectedHostnames?.length) {
    conditions.push(`hostname=~"${selectedHostnames.join('|')}"`);
  }

  if (selectedServices?.length) {
    conditions.push(`service_name=~"${selectedServices.join('|')}"`);
  }

  if (filename) {
    conditions.push(`filename="${filename}"`);
  }

  // Add extra label selectors
  for (const [key, value] of Object.entries(extraLabels)) {
    if (Array.isArray(value) && value.length > 0) {
      conditions.push(`${key}=~"${value.join('|')}"`);
    } else if (typeof value === 'string' && value.trim() !== '') {
      conditions.push(`${key}="${value}"`);
    }
  }

  // Fallback to match something
  if (conditions.length === 0) {
    conditions.push('hostname=~".+"');
  }

  const labelSelector = `{${conditions.join(',')}}`;
  const searchExprPart = searchExpression ? ` |= \`${searchExpression}\`` : '';
  const labelFormatPart = Object.entries(labelFormat).length
    ? ` | label_format ${Object.entries(labelFormat)
        .map(([k, v]) => `${k}=${v}`)
        .join(', ')}`
    : '';

  return `${labelSelector}${searchExprPart}${labelFormatPart}`;
};

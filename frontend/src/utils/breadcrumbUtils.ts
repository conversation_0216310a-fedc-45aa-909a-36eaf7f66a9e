import { Incident } from '../types/IncidentType';
import { Document, KnowledgeBase, Project } from '../types/KnowledgeBaseTypes';

export interface BreadcrumbItem {
  title: string;
  to: string | null; // null means current page (not clickable)
  icon?: React.ComponentType<{ size?: number; className?: string }>;
}

export const getIncidentViewFromPath = (path: string): string | null => {
  if (path.includes('/timeline')) return 'Timeline';
  if (path.includes('/edit')) return 'Edit';
  if (path.includes('/reports')) return 'Reports';
  if (path.includes('/logs')) return 'Logs';
  if (path.includes('/metrics')) return 'Metrics';
  return null; // Default incident detail view
};

export const generateIncidentBreadcrumbs = (
  incident: Incident,
  currentPath: string,
): BreadcrumbItem[] => {
  const baseBreadcrumbs: BreadcrumbItem[] = [
    { title: 'Incidents', to: '/incidents' },
  ];

  // Add incident detail breadcrumb
  const incidentTitle = `Incident #${incident.incident_number}`;
  const currentView = getIncidentViewFromPath(currentPath);

  if (currentView) {
    // If we're on a specific view, incident detail is clickable
    baseBreadcrumbs.push({
      title: incidentTitle,
      to: `/incident/${incident.id}`,
    });
    baseBreadcrumbs.push({
      title: currentView,
      to: null, // Current page
    });
  } else {
    // If we're on main incident detail, it's not clickable
    baseBreadcrumbs.push({
      title: incidentTitle,
      to: null, // Current page
    });
  }

  return baseBreadcrumbs;
};

export const generateCreateIncidentBreadcrumbs = (): BreadcrumbItem[] => {
  return [
    { title: 'Incidents', to: '/incidents' },
    { title: 'Create Incident', to: null }, // Current page
  ];
};

// Utility function to get the last segment of a path for automatic breadcrumb generation
export const getPathSegments = (pathname: string): string[] => {
  return pathname.split('/').filter(segment => segment.length > 0);
};

// Knowledge Base breadcrumb generators
export const generateKnowledgeBaseBreadcrumbs = (
  currentProject?: Project,
  currentKnowledgeBase?: KnowledgeBase,
  currentDocument?: Document,
): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Knowledge Base', to: '/knowledge-base' },
  ];

  if (currentProject) {
    const isProjectCurrent = !currentKnowledgeBase && !currentDocument;
    breadcrumbs.push({
      title: currentProject.name,
      to: isProjectCurrent ? null : `/projects/${currentProject.id}`,
    });
  }

  if (currentKnowledgeBase) {
    const isKbCurrent = !currentDocument;
    breadcrumbs.push({
      title: currentKnowledgeBase.name,
      to: isKbCurrent ? null : `/knowledge-base/${currentKnowledgeBase.id}`,
    });
  }

  if (currentDocument) {
    breadcrumbs.push({
      title: currentDocument.name,
      to: null, // Current page
    });
  }

  return breadcrumbs;
};

export const generateProjectsBreadcrumbs = (): BreadcrumbItem[] => {
  return [
    { title: 'Knowledge Base', to: '/knowledge-base' },
    { title: 'Projects', to: null }, // Current page
  ];
};

export const generateCreateProjectBreadcrumbs = (): BreadcrumbItem[] => {
  return [
    { title: 'Knowledge Base', to: '/knowledge-base' },
    { title: 'Projects', to: '/knowledge-base' },
    { title: 'Create Project', to: null }, // Current page
  ];
};

export const generateCreateKnowledgeBaseBreadcrumbs = (
  project: Project,
): BreadcrumbItem[] => {
  return [
    { title: 'Knowledge Base', to: '/knowledge-base' },
    { title: project.name, to: `/knowledge-base/projects/${project.id}` },
    { title: 'Create Knowledge Base', to: null }, // Current page
  ];
};

export const generateDocumentDetailBreadcrumbs = (
  project: Project,
  knowledgeBase: KnowledgeBase,
  document: Document,
): BreadcrumbItem[] => {
  return [
    { title: 'Knowledge Base', to: '/knowledge-base' },
    { title: project.name, to: `/projects/${project.id}` },
    {
      title: knowledgeBase.name,
      to: `/knowledge-base/${knowledgeBase.id}`,
    },
    { title: document.name, to: null }, // Current page
  ];
};

// Auto-generate breadcrumbs based on current route (fallback option)
export const generateAutoBreadcrumbs = (pathname: string): BreadcrumbItem[] => {
  const segments = getPathSegments(pathname);
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', to: '/dashboard' },
  ];

  let currentPath = '';
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === segments.length - 1;

    // Convert segment to readable title
    const title = segment.charAt(0).toUpperCase() + segment.slice(1);
    if (
      title === 'Incident' &&
      segments[index + 1] &&
      !isNaN(Number(segments[index + 1]))
    ) {
      // Skip the "incident" segment when followed by an ID
      return;
    }

    breadcrumbs.push({
      title,
      to: isLast ? null : currentPath,
    });
  });

  return breadcrumbs;
};

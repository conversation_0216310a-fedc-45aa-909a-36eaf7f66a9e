// Centralized configuration service
const getApiUrl = (): string => {
  const apiUrl = window.CONFIG?.API_URL;

  // Validate URL format
  try {
    if (!apiUrl) throw new Error('API URL is not defined');
    new URL(apiUrl);
    return apiUrl;
  } catch (e) {
    console.error('Invalid API URL in configuration', e);
    // Fallback to a safe default if URL is invalid
    return '/api';
  }
};

// Export a read-only configuration object
export const config = Object.freeze({
  getApiUrl,
});

import {
  Incident,
  IncidentSeverity,
  IncidentStatus,
} from '../types/IncidentType';
import { SEVERITY, STATUS } from '../constants/types';

export interface DashboardMetrics {
  mtbf: number; // Mean Time Between Failures (hours)
  mttd: number; // Mean Time To Detect (minutes)
  mtta: number; // Mean Time To Acknowledge (minutes)
  mttr: number; // Mean Time To Resolve (hours)
  totalIncidents: number;
  openIncidents: number;
  resolvedIncidents: number;
  criticalIncidents: number;
}

export interface IncidentTimeData {
  date: string;
  incidents: number;
  resolved: number;
  critical: number;
}

export function calculateMetrics(incidents: Incident[]): DashboardMetrics {
  if (incidents.length === 0) {
    return {
      mtbf: 0,
      mttd: 0,
      mtta: 0,
      mttr: 0,
      totalIncidents: 0,
      openIncidents: 0,
      resolvedIncidents: 0,
      criticalIncidents: 0,
    };
  }

  const totalIncidents = incidents.length;
  const openIncidents = incidents.filter(
    i => i.status === STATUS.OPEN || i.status === STATUS.ACTIVE,
  ).length;
  const resolvedIncidents = incidents.filter(
    i => i.status === STATUS.RESOLVED || i.status === STATUS.CLOSED,
  ).length;
  const criticalIncidents = incidents.filter(
    i => i.severity === SEVERITY.CRITICAL,
  ).length;

  // Calculate MTBF (Mean Time Between Failures)
  // This is the average time between incident occurrences
  const sortedIncidents = incidents.sort(
    (a, b) =>
      new Date(a.reported_at).getTime() - new Date(b.reported_at).getTime(),
  );

  let totalTimeBetweenFailures = 0;
  for (let i = 1; i < sortedIncidents.length; i++) {
    const prevIncident = new Date(sortedIncidents[i - 1].reported_at);
    const currentIncident = new Date(sortedIncidents[i].reported_at);
    totalTimeBetweenFailures +=
      currentIncident.getTime() - prevIncident.getTime();
  }
  const mtbf =
    sortedIncidents.length > 1
      ? totalTimeBetweenFailures /
        (sortedIncidents.length - 1) /
        (1000 * 60 * 60)
      : 0;

  // Calculate MTTD (Mean Time To Detect)
  // For simplicity, assuming detection time is the time from incident creation to first update
  let totalDetectionTime = 0;
  let detectionCount = 0;
  incidents.forEach(incident => {
    if (incident.timeline.events.length > 0) {
      const createdTime = new Date(incident.timeline.created);
      const firstEventTime = new Date(incident.timeline.events[0].timestamp);
      totalDetectionTime += firstEventTime.getTime() - createdTime.getTime();
      detectionCount++;
    }
  });
  const mttd =
    detectionCount > 0 ? totalDetectionTime / detectionCount / (1000 * 60) : 0;

  // Calculate MTTA (Mean Time To Acknowledge)
  // Time from incident creation to first acknowledgment
  let totalAcknowledgmentTime = 0;
  let acknowledgmentCount = 0;
  incidents.forEach(incident => {
    const acknowledgedEvent = incident.timeline.events.find(
      () =>
        incident.status === STATUS.ACKNOWLEDGED ||
        incident.status === STATUS.ACTIVE,
    );
    if (acknowledgedEvent) {
      const createdTime = new Date(incident.timeline.created);
      const acknowledgedTime = new Date(acknowledgedEvent.timestamp);
      totalAcknowledgmentTime +=
        acknowledgedTime.getTime() - createdTime.getTime();
      acknowledgmentCount++;
    }
  });
  const mtta =
    acknowledgmentCount > 0
      ? totalAcknowledgmentTime / acknowledgmentCount / (1000 * 60)
      : 0;

  // Calculate MTTR (Mean Time To Resolve)
  // Time from incident creation to resolution
  let totalResolutionTime = 0;
  let resolutionCount = 0;
  incidents.forEach(incident => {
    if (incident.resolution && incident.resolution.resolvedAt) {
      const createdTime = new Date(incident.timeline.created);
      const resolvedTime = new Date(incident.resolution.resolvedAt);
      totalResolutionTime += resolvedTime.getTime() - createdTime.getTime();
      resolutionCount++;
    }
  });
  const mttr =
    resolutionCount > 0
      ? totalResolutionTime / resolutionCount / (1000 * 60 * 60)
      : 0;

  return {
    mtbf: Math.round(mtbf * 100) / 100,
    mttd: Math.round(mttd * 100) / 100,
    mtta: Math.round(mtta * 100) / 100,
    mttr: Math.round(mttr * 100) / 100,
    totalIncidents,
    openIncidents,
    resolvedIncidents,
    criticalIncidents,
  };
}

export function generateIncidentTimeData(
  incidents: Incident[],
): IncidentTimeData[] {
  // Group incidents by date
  const incidentsByDate = new Map<string, Incident[]>();

  incidents.forEach(incident => {
    const date = new Date(incident.reported_at).toISOString().split('T')[0];
    if (!incidentsByDate.has(date)) {
      incidentsByDate.set(date, []);
    }
    incidentsByDate.get(date)?.push(incident);
  });

  // Generate data for the last 30 days
  const data: IncidentTimeData[] = [];
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 29);

  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    const dateStr = d.toISOString().split('T')[0];
    const dayIncidents = incidentsByDate.get(dateStr) || [];

    data.push({
      date: d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      incidents: dayIncidents.length,
      resolved: dayIncidents.filter(
        i => i.status === STATUS.RESOLVED || i.status === STATUS.CLOSED,
      ).length,
      critical: dayIncidents.filter(i => i.severity === SEVERITY.CRITICAL)
        .length,
    });
  }

  return data;
}

// Mock data generator for demo purposes
export function generateMockIncidents(): Incident[] {
  const mockIncidents: Incident[] = [];
  const severities: IncidentSeverity[] = [
    SEVERITY.CRITICAL,
    SEVERITY.HIGH,
    SEVERITY.MEDIUM,
    SEVERITY.LOW,
  ] as IncidentSeverity[];

  const statuses: IncidentStatus[] = [
    STATUS.OPEN,
    STATUS.ACKNOWLEDGED,
    STATUS.ACTIVE,
    STATUS.RESOLVED,
    STATUS.CLOSED,
  ] as IncidentStatus[];

  for (let i = 0; i < 50; i++) {
    const reportedDate = new Date();
    reportedDate.setDate(
      reportedDate.getDate() - Math.floor(Math.random() * 30),
    );

    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const severity = severities[Math.floor(Math.random() * severities.length)];

    const acknowledgedTime = new Date(reportedDate);
    acknowledgedTime.setMinutes(
      acknowledgedTime.getMinutes() + Math.floor(Math.random() * 60),
    );

    const resolvedTime =
      status === STATUS.RESOLVED || status === STATUS.CLOSED
        ? new Date(acknowledgedTime)
        : undefined;
    if (resolvedTime) {
      resolvedTime.setHours(
        resolvedTime.getHours() + Math.floor(Math.random() * 24),
      );
    }

    mockIncidents.push({
      id: `${i.toString().padStart(3, '0')}`,
      incident_number: `INC-${i.toString().padStart(3, '0')}`,
      title: `Incident ${i + 1} - ${severity} Issue`,
      summary: `Summary of incident ${i + 1}`,
      incident_type: 'outage',
      severity,
      priority: 'p1',
      status,
      reported_at: reportedDate.toISOString(),
      reporter: {
        id: '1',

        first_name: 'System',
        last_name: 'Monitor',
        email: '<EMAIL>',
        avatar: '',
      },
      assignedUsers: [],
      tags: ['backend', 'api'],
      affectedServices: [],
      timeline: {
        created: reportedDate.toISOString(),
        events: [
          {
            id: '1',
            timestamp: acknowledgedTime.toISOString(),
            author: {
              id: '1',

              first_name: 'John',
              last_name: 'Doe',
              email: '<EMAIL>',
              avatar: '',
            },
            type: 'status_change',
            content: 'Incident acknowledged',
            visibility: 'internal',
          },
        ],
        lastUpdated: acknowledgedTime.toISOString(),
      },
      resolution: resolvedTime
        ? {
            summary: 'Issue resolved',
            rootCause: 'System error',
            preventiveMeasures: [],
            resolvedAt: resolvedTime.toISOString(),
            resolvedBy: {
              id: '1',
              first_name: 'Jane',
              last_name: 'Smith',
              email: '<EMAIL>',
              avatar: '',
            },
          }
        : undefined,
    });
  }

  return mockIncidents;
}

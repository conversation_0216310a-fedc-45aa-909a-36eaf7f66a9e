import { config } from './configService';
import {
  getAccessToken,
  getRefreshToken,
  setTokens,
  clearTokens,
} from './authHelper';

export interface ApiOptions extends RequestInit {
  requireAuth?: boolean;
  skipRefresh?: boolean; // Flag to prevent infinite refresh loops
}

export class ApiError extends Error {
  constructor(
    public status: number,
    message: string,
    public data?: any,
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Centralized API utility with automatic token refresh and authentication handling
 */
export class ApiClient {
  private baseUrl: string;
  private refreshPromise: Promise<any> | null = null;

  constructor() {
    this.baseUrl = config.getApiUrl();
  }

  /**
   * Refresh access token using refresh token
   */
  private async refreshAccessToken(): Promise<void> {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();
    try {
      await this.refreshPromise;
    } finally {
      this.refreshPromise = null;
    }
  }

  private async performTokenRefresh(): Promise<void> {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch(`${this.baseUrl}/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });

      if (!response.ok) {
        throw new ApiError(response.status, 'Token refresh failed');
      }

      const data = await response.json();
      setTokens(data.access_token, data.refresh_token);
    } catch (error) {
      clearTokens();
      throw error;
    }
  }

  /**
   * Create headers with automatic auth token inclusion
   */
  private createHeaders(options: ApiOptions = {}): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add auth token if required (default: true)
    const requireAuth = options.requireAuth !== false;
    if (requireAuth) {
      const token = getAccessToken();
      if (token) {
        (headers as Record<string, string>).Authorization = `Bearer ${token}`;
      }
    }

    return headers;
  }

  /**
   * Handle API response, including error handling and automatic token refresh
   */
  private async handleResponse<T>(
    response: Response,
    originalRequest: () => Promise<Response>,
    options: ApiOptions = {},
  ): Promise<T> {
    // Handle 401 errors with token refresh
    if (response.status === 401 && !options.skipRefresh) {
      try {
        await this.refreshAccessToken();
        // Retry the original request with new token
        const retryResponse = await originalRequest();
        return this.handleResponse(retryResponse, originalRequest, {
          ...options,
          skipRefresh: true,
        });
      } catch (refreshError) {
        // If refresh fails, clear tokens and throw original error
        clearTokens();
        // Dispatch logout event for components to handle
        window.dispatchEvent(new CustomEvent('auth:logout'));
      }
    }

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      let errorData: any = {};

      try {
        errorData = await response.json();
        errorMessage = errorData.detail || errorData.message || errorMessage;
      } catch {
        // If JSON parsing fails, use the default error message
      }

      throw new ApiError(response.status, errorMessage, errorData);
    }

    // Handle empty responses
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return {} as T;
    }

    return response.json();
  }

  /**
   * Make a GET request
   */
  async get<T>(endpoint: string, options: ApiOptions = {}): Promise<T> {
    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'GET',
        ...options,
        headers: this.createHeaders(options),
      });

    const response = await makeRequest();
    return this.handleResponse<T>(response, makeRequest, options);
  }

  /**
   * Make a POST request
   */
  async post<T>(
    endpoint: string,
    data?: any,
    options: ApiOptions = {},
  ): Promise<T> {
    const body = data ? JSON.stringify(data) : undefined;
    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        body,
        ...options,
        headers: this.createHeaders(options),
      });

    const response = await makeRequest();
    return this.handleResponse<T>(response, makeRequest, options);
  }

  /**
   * Make a POST request with form data
   */
  async postForm<T>(
    endpoint: string,
    formData: FormData | URLSearchParams,
    options: ApiOptions = {},
  ): Promise<T> {
    // Don't set Content-Type for FormData, let browser set it with boundary
    const headers = this.createHeaders(options);
    if (formData instanceof FormData) {
      delete (headers as Record<string, string>)['Content-Type'];
    } else {
      (headers as Record<string, string>)['Content-Type'] =
        'application/x-www-form-urlencoded';
    }

    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        body: formData,
        ...options,
        headers,
      });

    const response = await makeRequest();
    return this.handleResponse<T>(response, makeRequest, options);
  }

  /**
   * Make a PUT request
   */
  async put<T>(
    endpoint: string,
    data?: any,
    options: ApiOptions = {},
  ): Promise<T> {
    const body = data ? JSON.stringify(data) : undefined;
    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        body,
        ...options,
        headers: this.createHeaders(options),
      });

    const response = await makeRequest();
    return this.handleResponse<T>(response, makeRequest, options);
  }

  /**
   * Make a PATCH request
   */
  async patch<T>(
    endpoint: string,
    data?: any,
    options: ApiOptions = {},
  ): Promise<T> {
    const body = data ? JSON.stringify(data) : undefined;
    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PATCH',
        body,
        ...options,
        headers: this.createHeaders(options),
      });

    const response = await makeRequest();
    return this.handleResponse<T>(response, makeRequest, options);
  }

  /**
   * Make a DELETE request
   */
  async delete<T>(endpoint: string, options: ApiOptions = {}): Promise<T> {
    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'DELETE',
        ...options,
        headers: this.createHeaders(options),
      });

    const response = await makeRequest();
    return this.handleResponse<T>(response, makeRequest, options);
  }

  /**
   * Make a POST request with form data
   */
  async postFormData<T>(
    endpoint: string,
    formData: FormData,
    options: ApiOptions = {},
  ): Promise<T> {
    // Don't set Content-Type for FormData, let browser set it with boundary
    const headers = this.createHeaders(options);
    delete (headers as Record<string, string>)['Content-Type'];

    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        body: formData,
        ...options,
        headers,
      });

    const response = await makeRequest();
    return this.handleResponse<T>(response, makeRequest, options);
  }

  /**
   * Make a PUT request with form data
   */
  async putFormData<T>(
    endpoint: string,
    formData: FormData,
    options: ApiOptions = {},
  ): Promise<T> {
    // Don't set Content-Type for FormData, let browser set it with boundary
    const headers = this.createHeaders(options);
    delete (headers as Record<string, string>)['Content-Type'];

    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        body: formData,
        ...options,
        headers,
      });

    const response = await makeRequest();
    return this.handleResponse<T>(response, makeRequest, options);
  }

  /**
   * Make a GET request that returns a Blob (for file downloads)
   */
  async getBlob(endpoint: string, options: ApiOptions = {}): Promise<Blob> {
    const makeRequest = () =>
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'GET',
        ...options,
        headers: this.createHeaders(options),
      });

    const response = await makeRequest();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.blob();
  }
}

// Export a singleton instance
export const apiClient = new ApiClient();

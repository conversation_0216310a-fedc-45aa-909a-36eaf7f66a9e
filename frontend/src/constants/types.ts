export const SEVERITY = {
  CRITICAL: 'critical',
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
};

export const STATUS = {
  OPEN: 'open',
  ACKNOWLEDGED: 'acknowledged',
  ACTIVE: 'active',
  RESOLVED: 'resolved',
  CLOSED: 'closed',
};

export const PRIORITY = {
  P0: 'p0',
  P1: 'p1',
  P2: 'p2',
  P3: 'p3',
  P4: 'p4',
};

export const INCIDENT_TYPE = {
  OUTAGE: 'outage',
  DEGRADATION: 'degradation',
  SECURITY: 'security',
  PERFORMANCE: 'performance',
  OTHER: 'other',
};

export const SERVICE_STATUS = {
  OPERATIONAL: 'Operational',
  DEGRADED: 'Degraded',
  DOWN: 'Down',
  MAINTENANCE: 'Under Maintenance',
};

export const IMPACT = {
  HIGH: 'High',
  MEDIUM: 'Medium',
  LOW: 'Low',
};

export const UPDATE_TYPE = {
  STATUS_CHANGE: 'status_change',
  COMMENT: 'comment',
  RESOLUTION: 'resolution',
  ESCALATION: 'escalation',
  ASSIGNMENT: 'assignment',
};

export const VISIBILITY = {
  INTERNAL: 'internal',
  PUBLIC: 'public',
};

export const STEP_STATUS = {
  PENDING: 'pending',
  SUCCESSFUL: 'successful',
  FAILED: 'failed',
} as const;

export type StepStatus = (typeof STEP_STATUS)[keyof typeof STEP_STATUS];

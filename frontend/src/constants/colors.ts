// Severity Colors
export const SEVERITY_COLORS = {
  CRITICAL: {
    FILLED: 'red',
    LIGHT: 'red.7',
    DOT: 'red.6',
  },
  HIGH: {
    FILLED: 'orange',
    LIGHT: 'orange.7',
    DOT: 'orange.6',
  },
  MEDIUM: {
    FILLED: 'yellow',
    LIGHT: 'yellow.7',
    DOT: 'yellow.6',
  },
  LOW: {
    FILLED: 'green',
    LIGHT: 'green.7',
    DOT: 'green.6',
  },
} as const;

// Status Colors
export const STATUS_COLORS = {
  OPEN: {
    FILLED: 'blue.6',
    LIGHT: 'blue.4',
    DOT: 'blue',
  },
  ACKNOWLEDGED: {
    FILLED: 'orange.6',
    LIGHT: 'orange.4',
    DOT: 'orange',
  },
  ACTIVE: {
    FILLED: 'violet.6',
    LIGHT: 'violet.4',
    DOT: 'violet',
  },
  RESOLVED: {
    FILLED: 'teal.6',
    LIGHT: 'teal.4',
    DOT: 'teal',
  },
  CLOSED: {
    FILLED: 'gray.6',
    LIGHT: 'gray.4',
    DOT: 'gray',
  },
} as const;

// Service Status Colors
export const SERVICE_STATUS_COLORS = {
  OPERATIONAL: {
    FILLED: 'green.6',
    LIGHT: 'green.4',
    DOT: 'green',
  },
  DEGRADED: {
    FILLED: 'yellow.6',
    LIGHT: 'yellow.4',
    DOT: 'yellow',
  },
  DOWN: {
    FILLED: 'red.6',
    LIGHT: 'red.4',
    DOT: 'red',
  },
  MAINTENANCE: {
    FILLED: 'blue.6',
    LIGHT: 'blue.4',
    DOT: 'blue',
  },
} as const;

// Impact Colors
export const IMPACT_COLORS = {
  HIGH: {
    FILLED: 'red.6',
    LIGHT: 'red.4',
    DOT: 'red',
  },
  MEDIUM: {
    FILLED: 'orange.6',
    LIGHT: 'orange.4',
    DOT: 'orange',
  },
  LOW: {
    FILLED: 'blue.6',
    LIGHT: 'blue.4',
    DOT: 'blue',
  },
} as const;

// Common UI Colors
export const UI_COLORS = {
  PRIMARY: 'var(--color-primary)',
  ERROR: 'red',
  SUCCESS: 'green',
  WARNING: 'orange',
  INFO: 'blue',
  MUTED: 'gray.6',
  BORDER: 'gray.3',
  HOVER: 'gray.1',
} as const;

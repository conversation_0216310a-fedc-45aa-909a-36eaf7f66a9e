import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate, useLocation } from 'react-router';
import { loginApi, LoginCredentials, logoutApi } from '../api/authApi';
import { getUserDetails } from '../api/userApi';
import { UserDetail } from '../types/UserDetailTypes';
import { clearTokens, getAccessToken } from '../utils/authHelper';

interface AuthState {
  isAuthenticated: boolean;
  user: UserDetail | null;
  token: string | null;
  isLoading: boolean;
}

interface AuthContextValue extends AuthState {
  login: (credentials: LoginCredentials) => void;
  logout: () => void;
  isLoggingIn: boolean;
  loginError: Error | null;
  refetchUser: () => void;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();

  const [authState, setAuthState] = useState<AuthState>(() => {
    const token = getAccessToken();
    return {
      isAuthenticated: !!token,
      user: null,
      token,
      isLoading: !!token, // If we have a token, we're loading user details
    };
  });

  // Listen for logout events from API client
  useEffect(() => {
    const handleLogoutEvent = () => {
      handleLogout();
    };

    window.addEventListener('auth:logout', handleLogoutEvent);
    return () => window.removeEventListener('auth:logout', handleLogoutEvent);
  }, []);

  // Fetch user details when authenticated
  const {
    data: user,
    isLoading: isLoadingUser,
    error: userError,
    refetch: refetchUser,
  } = useQuery({
    queryKey: ['user', 'me'],
    queryFn: getUserDetails,
    enabled: !!authState.token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on 401 errors
      if (error instanceof Error && error.message.includes('401')) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Update auth state when user data changes
  useEffect(() => {
    setAuthState(prev => ({
      ...prev,
      user: user || null,
      isLoading: isLoadingUser,
    }));
  }, [user, isLoadingUser]);

  // Handle user fetch errors (e.g., token expiration)
  useEffect(() => {
    if (userError && authState.token) {
      console.error('Error fetching user details:', userError);
      if (
        userError.message.includes('401') ||
        userError.message.includes('Unauthorized')
      ) {
        handleLogout(); // Don't call API logout on token expiration
      }
    }
  }, [userError, authState.token]);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: (credentials: LoginCredentials) => loginApi(credentials),
    onSuccess: data => {
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: true,
        token: data.access_token,
        isLoading: true, // Will load user details
      }));

      // Navigate to intended page or dashboard
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    },
    onError: error => {
      console.error('Login failed:', error);
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: logoutApi,
    onSuccess: () => {
      handleLogout(); // Don't call API again
    },
    onError: error => {
      console.error('Logout API failed:', error);
      handleLogout(); // Still clear local state
    },
  });

  const handleLogout = () => {
    // Clear auth state
    setAuthState({
      isAuthenticated: false,
      user: null,
      token: null,
      isLoading: false,
    });

    clearTokens();

    // Clear all cached queries
    queryClient.clear();

    // Navigate to login
    navigate('/login', { replace: true });
  };

  const login = (credentials: LoginCredentials) => {
    loginMutation.mutate(credentials);
  };

  const logout = () => {
    if (authState.token) {
      logoutMutation.mutate();
    } else {
      handleLogout();
    }
  };

  const contextValue: AuthContextValue = {
    ...authState,
    login,
    logout,
    isLoggingIn: loginMutation.isPending,
    loginError: loginMutation.error,
    refetchUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useProjects } from '../hooks/useKnowledgeBase';
import { Project } from '../types/KnowledgeBaseTypes';

interface ProjectContextValue {
  selectedProject: Project | null;
  setSelectedProject: (project: Project | null) => void;
  isLoading: boolean;
  projects: Project[];
  hasProjects: boolean;
  needsProjectCreation: boolean;
}

const ProjectContext = createContext<ProjectContextValue | undefined>(
  undefined,
);

interface ProjectProviderProps {
  children: ReactNode;
}

const SELECTED_PROJECT_KEY = 'selectedProject';

export const ProjectProvider: React.FC<ProjectProviderProps> = ({
  children,
}) => {
  const [selectedProject, setSelectedProjectState] = useState<Project | null>(
    null,
  );
  const [isInitialized, setIsInitialized] = useState(false);

  // Fetch projects
  const { data: projectsData, isLoading: isLoadingProjects } = useProjects(
    0,
    100,
  );
  const projects = projectsData?.items || [];
  const hasProjects = projects.length > 0;
  const needsProjectCreation = !isLoadingProjects && !hasProjects;

  // Initialize selected project from localStorage or first available project
  useEffect(() => {
    if (isLoadingProjects || isInitialized) return;

    const savedProjectId = localStorage.getItem(SELECTED_PROJECT_KEY);

    if (savedProjectId && hasProjects) {
      // Try to find the saved project
      const savedProject = projects.find(p => p.id === savedProjectId);
      if (savedProject) {
        setSelectedProjectState(savedProject);
        setIsInitialized(true);
        return;
      }
    }

    // If no saved project or saved project not found, select first available
    if (hasProjects && !selectedProject) {
      setSelectedProjectState(projects[0]);
      localStorage.setItem(SELECTED_PROJECT_KEY, projects[0].id);
    }

    setIsInitialized(true);
  }, [
    projects,
    hasProjects,
    isLoadingProjects,
    isInitialized,
    selectedProject,
  ]);

  // Update localStorage when selected project changes
  const setSelectedProject = (project: Project | null) => {
    setSelectedProjectState(project);
    if (project) {
      localStorage.setItem(SELECTED_PROJECT_KEY, project.id);
    } else {
      localStorage.removeItem(SELECTED_PROJECT_KEY);
    }
  };

  // Update selected project if it gets updated in the projects list
  useEffect(() => {
    if (selectedProject && hasProjects) {
      const updatedProject = projects.find(p => p.id === selectedProject.id);
      if (
        updatedProject &&
        JSON.stringify(updatedProject) !== JSON.stringify(selectedProject)
      ) {
        setSelectedProjectState(updatedProject);
      }
    }
  }, [projects, selectedProject, hasProjects]);

  const contextValue: ProjectContextValue = {
    selectedProject,
    setSelectedProject,
    isLoading: isLoadingProjects || !isInitialized,
    projects,
    hasProjects,
    needsProjectCreation,
  };

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
};

export const useProjectContext = (): ProjectContextValue => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProjectContext must be used within a ProjectProvider');
  }
  return context;
};

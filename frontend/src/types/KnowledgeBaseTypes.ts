// Enums based on backend models
export enum KnowledgeBaseTypeEnum {
  PROJECT_DOCUMENTATION = 'PROJECT_DOCUMENTATION',
  EXTERNAL_DOCUMENTATION = 'EXTERNAL_DOCUMENTATION',
  SYSTEM_ARCHITECTURE = 'SYSTEM_ARCHITECTURE',
  SERVICE_DETAILS = 'SERVICE_DETAILS',
  RUNBOOKS = 'RUNBOOKS',
  OTHER = 'OTHER',
}

export enum DocumentTypeEnum {
  PDF = 'PDF',
  TEXT = 'TEXT',
  URL = 'URL',
  MARKDOWN = 'MARKDOWN',
  DOCX = 'DOCX',
  OTHER = 'OTHER',
}

export enum SyncStatusEnum {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SYNCED = 'SYNCED',
  FAILED = 'FAILED',
}

// Project interfaces
export interface ProjectCreate {
  name: string;
  description: string;
}

export interface ProjectUpdate {
  name?: string;
  description?: string;
}

export interface KnowledgeBaseInfo {
  id: string;
  name: string;
  kb_type: string;
  description?: string;
  document_count: number;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface ProjectListItem {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  knowledge_base_count: number;
}

export interface PaginatedProjectResponse {
  items: ProjectListItem[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// Knowledge Base interfaces
export interface KnowledgeBaseCreate {
  name: string;
  description?: string;
  kb_type: KnowledgeBaseTypeEnum;
  project_id: string;
}

export interface KnowledgeBaseUpdate {
  name?: string;
  description?: string;
}

export interface DocumentInfo {
  id: string;
  name: string;
  document_type: DocumentTypeEnum;
  sync_status?: string;
  created_at: string;
}

export interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  kb_type: string;
  project_id: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  documents: DocumentInfo[];
}

// Document interfaces
export interface DocumentCreate {
  name?: string;
  description?: string;
  file?: File;
  external_url?: string;
  content?: string;
}

export interface DocumentUpdate {
  name?: string;
  description?: string;
  file?: File;
  external_url?: string;
  content?: string;
}

export interface DocumentListItem {
  id: string;
  name: string;
  description?: string;
  document_type: string;
  created_at: string;
  updated_at: string;
  sync_status?: string;
  knowledge_base_id: string;
}

export interface Document {
  id: string;
  name: string;
  description?: string;
  document_type: string;
  content?: string;
  meta_data?: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  last_synced_at?: string;
  sync_status?: string;
  knowledge_base_id: string;
}

export interface DocumentUploadResponse {
  id: string;
  name: string;
  document_type: string;
  content_extracted: boolean;
  message: string;
}

export interface PaginatedDocumentResponse {
  items: DocumentListItem[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// Navigation and UI types
export interface KnowledgeBaseNavigation {
  currentProject?: Project;
  currentKnowledgeBase?: KnowledgeBase;
  currentDocument?: Document;
}

export interface FileUploadData {
  file?: File;
  name?: string;
  description?: string;
  external_url?: string;
  content?: string;
}

// API Error types
export interface ApiError {
  detail: string;
  status_code: number;
}

// Search and filter types
export interface SearchFilters {
  search?: string;
  kb_type?: KnowledgeBaseTypeEnum;
  document_type?: DocumentTypeEnum;
  sync_status?: SyncStatusEnum;
}

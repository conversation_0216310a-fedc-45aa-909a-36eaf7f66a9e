import {
  SEVERITY,
  STAT<PERSON>,
  PR<PERSON><PERSON><PERSON>,
  INCIDENT_TYPE,
  SERVICE_STATUS,
  IMPACT,
  UPDATE_TYPE,
  VISIBILITY,
} from '../constants/types';

export type IncidentSeverity = (typeof SEVERITY)[keyof typeof SEVERITY];
export type IncidentStatus = (typeof STATUS)[keyof typeof STATUS];
export type IncidentPriority = (typeof PRIORITY)[keyof typeof PRIORITY];
export type IncidentType = (typeof INCIDENT_TYPE)[keyof typeof INCIDENT_TYPE];

export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  avatar?: string;
  role?: string;
}

export interface AffectedService {
  id: string;
  name: string;
  status: (typeof SERVICE_STATUS)[keyof typeof SERVICE_STATUS];
  details: string[];
  impact: (typeof IMPACT)[keyof typeof IMPACT];
  dependencies?: string[];
}

export interface IncidentUpdate {
  id: string;
  timestamp: string;
  author: User;
  type: (typeof UPDATE_TYPE)[keyof typeof UPDATE_TYPE];
  content: string;
  attachments?: string[];
  visibility: (typeof VISIBILITY)[keyof typeof VISIBILITY];
}

export interface IncidentBase {
  id: string;
  incident_number: string;
  title: string;
  summary?: string;
  incident_type: IncidentType;
  severity: IncidentSeverity;
  priority: IncidentPriority;
  status: IncidentStatus;
  reported_at: string;
  reporter: User;
}

export interface IncidentDetails {
  incident_id: string;
  affected_services?: string[];
  tags?: string[];
  incident_details?: string;
  attachments?: string[];
}

export interface IncidentList {
  items: IncidentBase[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface Incident extends IncidentBase {
  assignedUsers: User[];
  tags: string[];
  affectedServices: AffectedService[];
  timeline: {
    created: string;
    events: IncidentUpdate[];
    lastUpdated: string;
  };
  resolution?: {
    summary: string;
    rootCause: string;
    preventiveMeasures: string[];
    resolvedAt: string;
    resolvedBy: User;
  };
  escalation?: {
    level: number;
    escalatedAt: string;
    escalatedBy: User;
    reason: string;
  };
}

export interface SimilarIncident {
  incident: IncidentBase;
  similarity_score: number;
}

export interface SimilarIncidentsResponse {
  similar_incidents: SimilarIncident[];
  total_found: number;
}

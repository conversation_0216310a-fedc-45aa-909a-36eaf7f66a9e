export interface RunbookStep {
  step_order: number;
  title: string;
  description: string;
  details: string;
  expected_result: string;
  status: 'successful' | 'failed' | 'pending';
  notes?: string;
  executed_at?: string;
  executed_by_user: executed_by_user;
  id: string;
  runbook_id: string;
  created_at: string;
}

interface executed_by_user {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'engineer' | 'admin' | 'manager';
}

export interface Runbook {
  title: string;
  type: string;
  purpose: string;
  details: string;
  steps: RunbookStep[];
  id: string;
  incident_id: string;
  created_at: string;
}

export enum JobStatusEnum {
  PENDING = 'pending',
  STARTED = 'started',
  RETRY = 'retry',
  SUCCESS = 'success',
  FAILURE = 'failure',
  REVOKED = 'revoked',
  IGNORED = 'ignored',
}

export enum JobTypeEnum {
  GITHUB_IMPORT = 'github_import',
  GITHUB_SYNC = 'github_sync',
  SERVICENOW_IMPORT = 'servicenow_import',
  SERVICENOW_SYNC = 'servicenow_sync',
  JIRA_IMPORT = 'jira_import',
  JIRA_SYNC = 'jira_sync',
}

export interface Job {
  job_id: string;
  job_type: JobTypeEnum;
  status: JobStatusEnum;
  message?: string;
  created_at: string;
  updated_at: string;
  created_by_user_id: string;
}

export interface JobStatusResponse {
  status: JobStatusEnum;
  message?: string;
}

export interface PaginatedJobResponse {
  items: Job[];
  total: number;
  offset: number;
  limit: number;
}

export interface LogLabels {
  __stream_shard__?: string;
  detected_level: string;
  filename: string;
  hostname: string;
  job: string;
  service_name: string;
}

export interface LogEntry {
  timestamp: number;
  line: string;
  labels: LogLabels;
}

export interface LogResponse {
  status_code: number;
  message: string;
  data: LogEntry[];
  next_cursor?: string;
  has_more: boolean;
}

export interface LogQueryParams {
  query: string;
  start: string;
  end: string;
  limit?: number;
  direction?: 'backward' | 'forward';
  cursor?: string;
}

import { User } from './IncidentType';

export type EventType =
  | 'CREATED'
  | 'UPDATED'
  | 'ANALYSIS'
  | 'RESOLVED'
  | 'CLOSED'
  | 'COMMENT_ADDED'
  | 'ASSIGNED'
  | 'PRIORITY_CHANGED'
  | 'SEVERITY_CHANGED';

export interface AlertDetails {
  domain?: string;
  expiry?: string;
  impact?: string;
  source?: string;
  severity?: string;
  action?: string;
  status?: string;
  provider?: string;
  validity?: string;
}

export interface AnalysisDetails {
  resolution?: string;
  verification?: {
    expiry?: string;
    domains?: string;
    ssl_check?: string;
  };
  risks?: string[];
  current_state?: string;
  recommendation?: string;
  long_term_action?: string;
  team?: string;
  priority?: string;
  timeline?: string;
  initial_steps?: string[];
}

export interface EventDetails {
  description: string;
  alert_details?: AlertDetails;
  analysis_details?: AnalysisDetails;
}

export interface IncidentEvent {
  id: string;
  event_name: string;
  event_type: EventType;
  event_details: EventDetails;
  incident_id: string;
  event_datetime: string;
  created_at: string;
  user: User;
}

export interface IncidentEventsResponse {
  items: IncidentEvent[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface PaginationParams {
  skip?: number;
  limit?: number;
  sort?: string;
}

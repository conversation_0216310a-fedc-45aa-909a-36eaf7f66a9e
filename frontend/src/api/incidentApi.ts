import {
  mockAffectedService,
  mockIncident,
  mockUser,
} from '../mocks/mockIncident';
import {
  AffectedService,
  Incident,
  IncidentList,
  IncidentDetails,
  SimilarIncidentsResponse,
} from '../types/IncidentType';
import { apiClient } from '../utils/apiClient';

export const getIncidents = async (
  offset: number = 0,
  limit: number = 10,
): Promise<IncidentList> => {
  try {
    return await apiClient.get<IncidentList>(
      `/incidents/?offset=${offset}&limit=${limit}`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export const getIncidentById = async (id: string): Promise<Incident> => {
  try {
    const rawData = await apiClient.get<any>(`/incidents/${id}`);

    // Map the response to match our Incident type
    const data: Incident = {
      id: rawData.id,
      incident_number: rawData.incident_number,
      title: rawData.title,
      summary: rawData.summary,
      incident_type: rawData.incident_type,
      priority: rawData.priority,
      severity: rawData.severity,
      status: rawData.status,
      reported_at: rawData.reported_at,
      reporter: rawData.reporter
        ? {
            id: rawData.reporter.id,
            first_name: rawData.reporter.first_name,
            last_name: rawData.reporter.last_name,
            email: rawData.reporter.email,
            avatar: rawData.reporter.avatar || mockUser.avatar,
            role: rawData.reporter.role || mockUser.role,
          }
        : mockUser,
      assignedUsers:
        rawData.assignedUsers?.map((user: any) => ({
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          avatar: user.avatar || mockUser.avatar,
          role: user.role || mockUser.role,
        })) || mockIncident.assignedUsers,
      tags: rawData.tags || mockIncident.tags,
      affectedServices:
        rawData.affected_services?.map(
          (serviceName: string): AffectedService => ({
            id: serviceName,
            name: serviceName,
            status: mockAffectedService.status,
            details: mockAffectedService.details,
            impact: mockAffectedService.impact,
            dependencies: mockAffectedService.dependencies,
          }),
        ) || mockIncident.affectedServices,
      timeline: {
        created: rawData.timeline?.created || mockIncident.timeline.created,
        events: rawData.timeline?.events || mockIncident.timeline.events,
        lastUpdated:
          rawData.timeline?.lastUpdated || mockIncident.timeline.lastUpdated,
      },
    };

    return data;
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export interface CreateIncidentData {
  title: string;
  priority: string;
  severity: string;
  incident_type: string;
  affected_services?: string[];
  tags?: string[];
  incident_details?: string;
  attachments?: string[];
}

export const createIncident = async (
  incidentData: CreateIncidentData,
): Promise<Incident> => {
  try {
    // Map frontend priority values to backend values
    const priorityMap: { [key: string]: string } = {
      Critical: 'p0',
      High: 'p1',
      Medium: 'p2',
      Low: 'p3',
    };

    // Map frontend severity values to backend values
    const severityMap: { [key: string]: string } = {
      Critical: 'critical',
      High: 'high',
      Medium: 'medium',
      Low: 'low',
    };

    // Map frontend incident type to backend values
    const typeMap: { [key: string]: string } = {
      Outage: 'outage',
      Degradation: 'degradation',
      Security: 'security',
      Performance: 'performance',
      Other: 'other',
    };

    // Transform frontend data to backend format
    const backendData = {
      title: incidentData.title,
      priority:
        priorityMap[incidentData.priority] ||
        incidentData.priority.toLowerCase(),
      severity:
        severityMap[incidentData.priority] ||
        incidentData.priority.toLowerCase(), // TODO: Fix this
      incident_type:
        typeMap[incidentData.incident_type] ||
        incidentData.incident_type.toLowerCase(),
      affected_services: incidentData.affected_services || [],
      tags: incidentData.tags || [],
      incident_details: incidentData.incident_details || '',
      attachments: incidentData.attachments || [],
    };

    const rawData = await apiClient.post<any>('/incidents/create', backendData);

    // Transform response to match Incident type
    const data: Incident = {
      id: rawData.id,
      incident_number: rawData.incident_number,
      title: rawData.title,
      summary: rawData.summary,
      incident_type: rawData.incident_type,
      priority: rawData.priority,
      severity: rawData.severity,
      status: rawData.status,
      reported_at: rawData.reported_at,
      reporter: rawData.reporter || mockUser,
      assignedUsers: [],
      tags: rawData.tags || [],
      affectedServices:
        rawData.affected_services?.map((serviceName: string) => ({
          id: serviceName,
          name: serviceName,
          status: mockAffectedService.status,
          details: mockAffectedService.details,
          impact: mockAffectedService.impact,
          dependencies: mockAffectedService.dependencies,
        })) || [],
      timeline: {
        created: rawData.reported_at,
        events: [],
        lastUpdated: rawData.reported_at,
      },
    };

    return data;
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export const getIncidentDetails = async (
  id: string,
): Promise<IncidentDetails> => {
  try {
    return await apiClient.get<IncidentDetails>(`/incidents/${id}/details`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export const regenerateIncidentSummary = async (
  incidentId: string,
): Promise<Incident> => {
  try {
    const rawData = await apiClient.post<any>(
      `/incidents/${incidentId}/regenerate-summary`,
    );

    // Map the response to match our Incident type
    const data: Incident = {
      id: rawData.id,
      incident_number: rawData.incident_number,
      title: rawData.title,
      summary: rawData.summary,
      incident_type: rawData.incident_type,
      priority: rawData.priority,
      severity: rawData.severity,
      status: rawData.status,
      reported_at: rawData.reported_at,
      reporter: rawData.reporter
        ? {
            id: rawData.reporter.id,
            first_name: rawData.reporter.first_name,
            last_name: rawData.reporter.last_name,
            email: rawData.reporter.email,
            avatar: rawData.reporter.avatar,
            role: rawData.reporter.role,
          }
        : mockUser,
      assignedUsers:
        rawData.assignedUsers?.map((user: any) => ({
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          avatar: user.avatar || mockUser.avatar,
          role: user.role || mockUser.role,
        })) || mockIncident.assignedUsers,
      tags: rawData.tags || mockIncident.tags,
      affectedServices:
        rawData.affected_services?.map(
          (serviceName: string): AffectedService => ({
            id: serviceName,
            name: serviceName,
            status: mockAffectedService.status,
            details: mockAffectedService.details,
            impact: mockAffectedService.impact,
            dependencies: mockAffectedService.dependencies,
          }),
        ) || mockIncident.affectedServices,
      timeline: {
        created: rawData.timeline?.created || mockIncident.timeline.created,
        events: rawData.timeline?.events || mockIncident.timeline.events,
        lastUpdated:
          rawData.timeline?.lastUpdated || mockIncident.timeline.lastUpdated,
      },
    };

    return data;
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export interface IncidentMetric {
  id: string;
  incident_id: string;
  detected_time?: string;
  reported_time: string;
  acknowledged_time?: string;
  resolved_time?: string;
  closed_time?: string;
  time_to_report?: string;
  time_to_acknowledge?: string;
  time_to_resolve?: string;
  time_to_closure?: string;
  total_downtime?: string;
}

export const getIncidentMetrics = async (
  incidentId: string,
): Promise<IncidentMetric | null> => {
  try {
    const data = await apiClient.get<IncidentMetric>(
      `/incident/${incidentId}/metrics`,
    );
    return data;
  } catch (error) {
    // Check if it's a 404 error (no metrics found)
    if (error instanceof Error && error.message.includes('404')) {
      return null;
    }
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export const getSimilarIncidents = async (
  incidentId: string,
  topK: number = 5,
): Promise<SimilarIncidentsResponse> => {
  try {
    return await apiClient.get<SimilarIncidentsResponse>(
      `/incidents/${incidentId}/similar?top_k=${topK}`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

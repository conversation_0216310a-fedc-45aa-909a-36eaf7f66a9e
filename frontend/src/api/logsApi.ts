import { LogResponse, LogQueryParams } from '../types/logsType';
import { apiClient } from '../utils/apiClient';

export const fetchLogs = async (
  query: string,
  start: string,
  end: string,
  limit = 100,
  direction: 'backward' | 'forward' = 'backward',
  cursor?: string,
): Promise<LogResponse> => {
  const requestBody: LogQueryParams = {
    query,
    start,
    end,
    limit,
    direction,
  };

  if (cursor) {
    requestBody.cursor = cursor;
  }

  return await apiClient.post<LogResponse>('/logs/fetch', requestBody);
};

export const fetchLabels = async (
  start: string,
  end: string,
  query?: string,
): Promise<Record<string, string[]>> => {
  return await apiClient.post<Record<string, string[]>>('/logs/labels', {
    query,
    start,
    end,
  });
};

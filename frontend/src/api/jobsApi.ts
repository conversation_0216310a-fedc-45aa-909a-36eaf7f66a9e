import { Job, JobStatusResponse, PaginatedJobResponse } from '../types/JobType';
import { apiClient } from '../utils/apiClient';

export const importFromGithub = async (
  githubRepo: string,
  startTime?: string,
  endTime?: string,
): Promise<Job> => {
  try {
    const params = new URLSearchParams();
    params.append('github_repo', githubRepo);
    if (startTime) params.append('start_time', startTime);
    if (endTime) params.append('end_time', endTime);

    return await apiClient.post<Job>(`/jobs/import_github?${params}`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export const syncFromGithub = async (
  githubRepo: string,
  startTime?: string,
  endTime?: string,
  incidentType: string = 'both',
): Promise<Job> => {
  try {
    const params = new URLSearchParams();
    params.append('github_repo', githubRepo);
    if (startTime) params.append('start_time', startTime);
    if (endTime) params.append('end_time', endTime);
    params.append('incident_type', incidentType);

    return await apiClient.post<Job>(`/jobs/sync_github?${params}`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export const getJobStatus = async (
  jobId: string,
): Promise<JobStatusResponse> => {
  try {
    return await apiClient.get<JobStatusResponse>(`/jobs/status/${jobId}`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export const getJobs = async (
  offset: number = 0,
  limit: number = 10,
): Promise<PaginatedJobResponse> => {
  try {
    return await apiClient.get<PaginatedJobResponse>(
      `/jobs?offset=${offset}&limit=${limit}`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

export const getRunningJobs = async (): Promise<PaginatedJobResponse> => {
  try {
    return await apiClient.get<PaginatedJobResponse>('/jobs/running');
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

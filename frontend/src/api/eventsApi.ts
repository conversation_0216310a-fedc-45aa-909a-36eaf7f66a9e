import {
  IncidentEventsResponse,
  PaginationParams,
  EventType,
  EventDetails,
} from '../types/IncidentTimelineType';
import { apiClient } from '../utils/apiClient';

export const getIncidentEvents = async (
  incidentId: string,
  params: PaginationParams = {},
): Promise<IncidentEventsResponse> => {
  const { skip = 0, limit = 10, sort = 'asc' } = params;
  return await apiClient.get<IncidentEventsResponse>(
    `/incident/${incidentId}/events?skip=${skip}&limit=${limit}&sort=${sort}`,
  );
};

export const createIncidentEvent = async (
  incidentId: string,
  event_name: string,
  event_type: EventType,
  event_details: EventDetails,
) => {
  return await apiClient.post(`/incident/${incidentId}/events`, {
    event_name,
    event_type,
    event_details,
    incident_id: incidentId,
  });
};

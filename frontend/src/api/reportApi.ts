import { config } from '../utils/configService';
import { getAccessToken } from '../utils/authHelper';

/**
 * Generate and auto-download PDF report for an incident
 * @param incidentId - The ID of the incident to generate report for
 */
export const generateAndDownloadPdfReport = async (
  incidentId: string,
): Promise<void> => {
  try {
    const baseUrl = config.getApiUrl();
    const token = getAccessToken();

    // Make the GET request to generate PDF
    const response = await fetch(
      `${baseUrl}/incidents/${incidentId}/generate_pdf`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token || ''}`,
          Accept: 'application/pdf',
        },
      },
    );

    if (!response.ok) {
      throw new Error(
        `Failed to generate PDF: ${response.status} ${response.statusText}`,
      );
    }

    // Get the PDF blob
    const blob = await response.blob();

    // Create a download link and trigger download
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `incident-${incidentId}-report.pdf`;

    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the object URL
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error generating PDF report:', error);
    throw error instanceof Error
      ? error
      : new Error(
          'An unexpected error occurred while generating the PDF report',
        );
  }
};

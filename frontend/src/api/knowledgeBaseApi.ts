import {
  Document,
  DocumentCreate,
  DocumentUpdate,
  DocumentUploadResponse,
  KnowledgeBase,
  KnowledgeBaseCreate,
  KnowledgeBaseUpdate,
  PaginatedDocumentResponse,
} from '../types/KnowledgeBaseTypes';
import { apiClient } from '../utils/apiClient';

// Knowledge Base operations
export const getKnowledgeBases = async (
  projectId: string,
): Promise<KnowledgeBase[]> => {
  try {
    return await apiClient.get<KnowledgeBase[]>(
      `/knowledge-bases?project_id=${projectId}`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error(
          'An unexpected error occurred while fetching knowledge bases',
        );
  }
};

export const getKnowledgeBaseById = async (
  kbId: string,
): Promise<KnowledgeBase> => {
  try {
    return await apiClient.get<KnowledgeBase>(`/knowledge-bases/${kbId}`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while fetching knowledge base');
  }
};

export const createKnowledgeBase = async (
  data: KnowledgeBaseCreate,
): Promise<KnowledgeBase> => {
  try {
    return await apiClient.post<KnowledgeBase>('/knowledge-bases', data);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while creating knowledge base');
  }
};

export const updateKnowledgeBase = async (
  kbId: string,
  data: KnowledgeBaseUpdate,
): Promise<KnowledgeBase> => {
  try {
    return await apiClient.put<KnowledgeBase>(`/knowledge-bases/${kbId}`, data);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while updating knowledge base');
  }
};

export const deleteKnowledgeBase = async (kbId: string): Promise<void> => {
  try {
    await apiClient.delete(`/knowledge-bases/${kbId}`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while deleting knowledge base');
  }
};

// Document operations within knowledge bases
export const getKnowledgeBaseDocuments = async (
  kbId: string,
  offset: number = 0,
  limit: number = 10,
): Promise<PaginatedDocumentResponse> => {
  try {
    return await apiClient.get<PaginatedDocumentResponse>(
      `/knowledge-bases/${kbId}/documents?offset=${offset}&limit=${limit}`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while fetching documents');
  }
};

export const getKnowledgeBaseDocument = async (
  kbId: string,
  docId: string,
): Promise<Document> => {
  try {
    return await apiClient.get<Document>(
      `/knowledge-bases/${kbId}/documents/${docId}`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while fetching document');
  }
};

export const createDocument = async (
  kbId: string,
  data: DocumentCreate,
): Promise<DocumentUploadResponse> => {
  try {
    const formData = new FormData();

    if (data.file) {
      formData.append('file', data.file);
    }
    if (data.external_url) {
      formData.append('external_url', data.external_url);
    }
    if (data.content) {
      formData.append('content', data.content);
    }
    if (data.name) {
      formData.append('name', data.name);
    }
    if (data.description) {
      formData.append('description', data.description);
    }

    return await apiClient.postFormData<DocumentUploadResponse>(
      `/knowledge-bases/${kbId}/documents`,
      formData,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while creating document');
  }
};

export const updateDocument = async (
  kbId: string,
  docId: string,
  data: DocumentUpdate,
): Promise<Document> => {
  try {
    const formData = new FormData();

    if (data.file) {
      formData.append('file', data.file);
    }
    if (data.external_url) {
      formData.append('external_url', data.external_url);
    }
    if (data.content) {
      formData.append('content', data.content);
    }
    if (data.name) {
      formData.append('name', data.name);
    }
    if (data.description) {
      formData.append('description', data.description);
    }

    return await apiClient.putFormData<Document>(
      `/knowledge-bases/${kbId}/documents/${docId}`,
      formData,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while updating document');
  }
};

export const deleteDocument = async (
  kbId: string,
  docId: string,
): Promise<void> => {
  try {
    await apiClient.delete(`/knowledge-bases/${kbId}/documents/${docId}`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while deleting document');
  }
};

export const downloadDocument = async (
  kbId: string,
  docId: string,
): Promise<Blob> => {
  try {
    return await apiClient.getBlob(
      `/knowledge-bases/${kbId}/documents/${docId}/download`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while downloading document');
  }
};

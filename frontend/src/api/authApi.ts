import { getRefreshToken, setTokens, clearTokens } from '../utils/authHelper';
import { apiClient } from '../utils/apiClient';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

/**
 * Login API endpoint - calls the token endpoint for authentication
 */
export const loginApi = async (
  credentials: LoginCredentials,
): Promise<LoginResponse> => {
  try {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'password');
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);

    const data = await apiClient.postForm<LoginResponse>(
      '/auth/token',
      formData,
      { requireAuth: false }, // Don't require auth for login
    );

    // Store tokens in localStorage
    setTokens(data.access_token, data.refresh_token);

    return data;
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred');
  }
};

/**
 * Refresh token API endpoint
 */
export const refreshTokenApi = async (): Promise<LoginResponse> => {
  const refreshToken = getRefreshToken();
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  try {
    const data = await apiClient.post<LoginResponse>(
      '/auth/refresh',
      { refresh_token: refreshToken },
      { requireAuth: false }, // Don't require auth for refresh
    );

    // Store new tokens in localStorage
    setTokens(data.access_token, data.refresh_token);

    return data;
  } catch (error) {
    // Clear tokens if refresh fails
    clearTokens();
    throw error instanceof Error ? error : new Error('Token refresh failed');
  }
};

export const logoutApi = async (): Promise<void> => {
  clearTokens();
  return Promise.resolve();
};

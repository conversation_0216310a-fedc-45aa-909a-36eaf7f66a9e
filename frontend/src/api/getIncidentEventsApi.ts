import { config } from '../utils/configService';
import {
  IncidentEventsResponse,
  PaginationParams,
} from '../types/IncidentTimelineType';
import { TOKEN_KEY } from '../utils/authHelper';

const API_BASE_URL = config.getApiUrl();

export const getIncidentEvents = async (
  incidentId: string,
  params: PaginationParams = {},
): Promise<IncidentEventsResponse> => {
  const { skip = 0, limit = 10, sort = 'asc' } = params;
  const token = localStorage.getItem(TOKEN_KEY);
  const response = await fetch(
    `${API_BASE_URL}/incident/${incidentId}/events?skip=${skip}&limit=${limit}&sort=${sort}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch incident events: ${response.statusText}`);
  }

  const data: IncidentEventsResponse = await response.json();
  return data;
};

import {
  PaginatedProjectResponse,
  Project,
  ProjectCreate,
  ProjectUpdate,
} from '../types/KnowledgeBaseTypes';
import { apiClient } from '../utils/apiClient';

export const getProjects = async (
  offset: number = 0,
  limit: number = 10,
): Promise<PaginatedProjectResponse> => {
  try {
    return await apiClient.get<PaginatedProjectResponse>(
      `/projects?offset=${offset}&limit=${limit}`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while fetching projects');
  }
};

export const getProjectById = async (projectId: string): Promise<Project> => {
  try {
    return await apiClient.get<Project>(`/projects/${projectId}`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while fetching project');
  }
};

export const createProject = async (data: ProjectCreate): Promise<Project> => {
  try {
    return await apiClient.post<Project>('/projects', data);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while creating project');
  }
};

export const updateProject = async (
  projectId: string,
  data: ProjectUpdate,
): Promise<Project> => {
  try {
    return await apiClient.put<Project>(`/projects/${projectId}`, data);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while updating project');
  }
};

export const deleteProject = async (projectId: string): Promise<void> => {
  try {
    await apiClient.delete(`/projects/${projectId}`);
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while deleting project');
  }
};

export const getAllProjects = async (
  offset: number = 0,
  limit: number = 10,
): Promise<PaginatedProjectResponse> => {
  try {
    return await apiClient.get<PaginatedProjectResponse>(
      `/projects/all?offset=${offset}&limit=${limit}`,
    );
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error('An unexpected error occurred while fetching all projects');
  }
};

# Vector Database Module

This module provides a unified interface for vector database operations across different vector database providers. It follows an abstract base class pattern to ensure consistency and enable easy extension to new vector database implementations.

## Architecture

### Class Hierarchy

```
VectorDBConnector (ABC)           # Abstract base class
    └── QdrantConnector         # Qdrant implementation
    ├── PineconeConnector       # Future: Pinecone implementation
    ├── WeaviateConnector       # Future: Weaviate implementation

VectorSearchService              # High-level service for vector operations
CollectionType (Enum)           # Type-safe collection identifiers
```

### Key Components

- **`VectorDBConnector`** - Abstract base class defining the low-level interface
- **`QdrantConnector`** - Concrete implementation for Qdrant vector database
- **`VectorSearchService`** - High-level service for business logic operations
- **`CollectionType`** - Enum for type-safe collection management (INCIDENTS, DOCUMENTS)
- **`generate_embedding`** - Embedding generation utility using Gemini text-embedding-004

## Interface Definition

The `VectorDBConnector` abstract base class defines the following low-level methods that all implementations must provide:

### Core Methods

- `_upsert_vector(collection_type, vector_id, embedding, metadata) -> bool`
- `_delete_vector(collection_type, vector_id) -> bool`
- `_find_similar_vectors(collection_type, embedding, top_k) -> List[Dict]`
- `_create_collections_if_not_exist() -> None`

### High-Level Service Methods

The `VectorSearchService` provides business logic methods:

- `search_similar_incidents_by_text(db, text, top_k, exclude_ids) -> List[Dict]`
- `search_similar_incidents_by_id(db, incident_id, top_k, exclude_ids) -> List[Dict]`
- `search_similar_documents_by_text(text, top_k, exclude_ids) -> List[Dict]`
- `upsert_incident(db, incident_id) -> bool`
- `upsert_document(db, document_id) -> bool` (Not yet implemented)
- `delete_incident_vector(incident_id) -> bool`
- `delete_document_vector(document_id) -> bool`

## Usage Examples

### Using the High-Level Service (Recommended)

```python
from vector_db.search_service import VectorSearchService
from database.core import DbSession

# Initialize the service
vector_service = VectorSearchService()

# Search for similar incidents by text
with DbSession() as db:
    similar_incidents = vector_service.search_similar_incidents_by_text(
        db=db,
        text="Database connection failed",
        top_k=5,
        exclude_ids=["current-incident-id"]
    )

# Search for similar incidents by existing incident ID
with DbSession() as db:
    similar_incidents = vector_service.search_similar_incidents_by_id(
        db=db,
        incident_id="existing-incident-id",
        top_k=5,
        exclude_ids=["current-incident-id"]
    )

# Upsert an incident into the vector database
with DbSession() as db:
    success = vector_service.upsert_incident(db, "incident-id")
```

### Using the Low-Level Connector Interface

```python
from vector_db.qdrant_connector import QdrantConnector
from vector_db.models import CollectionType
from vector_db.embeddings import generate_embedding

# Initialize connector
connector = QdrantConnector(
    collections={
        CollectionType.INCIDENTS: "incidents",
        CollectionType.DOCUMENTS: "documents",
    }
)

# Generate embedding and upsert manually
embedding = generate_embedding("Database connection failed")
success = connector._upsert_vector(
    collection_type=CollectionType.INCIDENTS,
    vector_id=UUID("incident-id"),
    embedding=embedding,
    metadata={"incident_id": "incident-id"}
)

# Search for similar vectors
results = connector._find_similar_vectors(
    collection_type=CollectionType.INCIDENTS,
    embedding=embedding,
    top_k=5
)
```

### Using Type Hints with the Interface

```python
from vector_db.base_connector import VectorDBConnector
from vector_db.qdrant_connector import QdrantConnector

def process_vectors(connector: VectorDBConnector):
    # This function works with any VectorDBConnector implementation
    results = connector._find_similar_vectors(
        CollectionType.INCIDENTS,
        [0.1, 0.2, ...],
        top_k=5
    )
    return results

# Use with Qdrant
connector = QdrantConnector({
    CollectionType.INCIDENTS: "incidents",
    CollectionType.DOCUMENTS: "documents"
})
results = process_vectors(connector)
```

## Adding New Vector Database Implementations

### Step 1: Implement the Abstract Base Class

```python
from vector_db.base_connector import VectorDBConnector
from vector_db.models import CollectionType
from typing import Dict, List, UUID
import os

class MyVectorConnector(VectorDBConnector):
    def __init__(self, collections: Dict[CollectionType, str]):
        super().__init__(collections)
        self.client = MyVectorDatabase(
            url=os.getenv("MY_VECTOR_DB_URL"),
            api_key=os.getenv("MY_VECTOR_DB_API_KEY")
        )
        self._create_collections_if_not_exist()

    def _create_collections_if_not_exist(self):
        """Create collections in your vector database."""
        for collection_type, collection_name in self._collections.items():
            # Implementation specific to your vector database
            self.client.create_collection_if_not_exists(collection_name)

    def _upsert_vector(self, collection_type: CollectionType,
                      vector_id: UUID, embedding: List[float],
                      metadata: Dict) -> bool:
        try:
            collection_name = self._collections[collection_type]
            self.client.upsert(
                collection=collection_name,
                id=str(vector_id),
                vector=embedding,
                metadata=metadata
            )
            return True
        except Exception as e:
            logger.error(f"Failed to upsert vector: {e}")
            return False

    def _delete_vector(self, collection_type: CollectionType,
                      vector_id: UUID) -> bool:
        try:
            collection_name = self._collections[collection_type]
            self.client.delete(
                collection=collection_name,
                id=str(vector_id)
            )
            return True
        except Exception as e:
            logger.error(f"Failed to delete vector: {e}")
            return False

    def _find_similar_vectors(self, collection_type: CollectionType,
                             embedding: List[float], top_k: int = 5) -> List[Dict]:
        try:
            collection_name = self._collections[collection_type]
            results = self.client.search(
                collection=collection_name,
                vector=embedding,
                limit=top_k
            )
            return [{"payload": hit.metadata, "score": hit.score} for hit in results]
        except Exception as e:
            logger.error(f"Failed to search vectors: {e}")
            return []
```

### Step 2: Add to Module Exports

Update `backend/app/vector_db/__init__.py`:

```python
# Import your new connector
from vector_db.my_connector import MyVectorConnector

# Add to exports
__all__ = [
    "VectorDBConnector",
    "QdrantConnector",
    "MyVectorConnector",  # Add your connector
    "VectorSearchService",
]
```

### Step 3: Update VectorSearchService (Optional)

If you want to make your connector the default, update `VectorSearchService`:

```python
# In vector_db/search_service.py
from vector_db.my_connector import MyVectorConnector

class VectorSearchService:
    def __init__(self):
        self.connector = MyVectorConnector({
            CollectionType.INCIDENTS: "incidents",
            CollectionType.DOCUMENTS: "documents"
        })
```

## Implementation Requirements

When implementing a new vector database connector, ensure:

### Error Handling

- Handle connection failures gracefully
- Log all operations with appropriate log levels
- Return `False` for failed operations instead of raising exceptions
- Return empty lists for failed searches

### Data Consistency

- Validate embedding dimensions match collection configuration
- Use atomic operations where possible
- Handle duplicate IDs appropriately (upsert behavior)
- Ensure operations are idempotent where specified

### Performance Considerations

- Batch operations when possible
- Use connection pooling if supported
- Implement appropriate timeouts
- Consider implementing async variants for high-throughput scenarios

### Logging Requirements

- Log all significant operations (upsert, delete, search)
- Include context information (IDs, counts, errors)
- Use structured logging where possible
- Never log sensitive data or embeddings

## Configuration

### Environment Variables

The current implementation supports these environment variables:

```bash
# Qdrant Configuration
QDRANT_URL=http://qdrant:6333          # Qdrant server URL
QDRANT_API_KEY=your-qdrant-key         # Optional API key for authentication
```

### Collection Management

All implementations should:

- Create collections automatically using `_create_collections_if_not_exist()`
- Support dual collections (incidents and documents) via `CollectionType` enum
- Use consistent vector size (768 dimensions for Gemini text-embedding-004)
- Use cosine similarity as the default distance metric

### Embedding Generation

The module provides a centralized embedding service:

- **Model**: `gemini/text-embedding-004`
- **Dimensions**: 768
- **Supports**: Plain text strings and structured dictionaries
- **Usage**: `generate_embedding(text_or_dict) -> List[float]`

## Current Implementation Details

### QdrantConnector Features

- Environment-based configuration (QDRANT_URL, QDRANT_API_KEY)
- Automatic collection creation with proper vector configuration
- Comprehensive error handling and logging
- Atomic operations with wait=True for consistency
- Supports both incidents and documents collections

### VectorSearchService Features

- High-level business logic abstraction
- Automatic data retrieval from database
- Built-in embedding generation
- Result processing with similarity scores
- Support for exclusion lists in searches
- Integration with incident database services

## Testing

When implementing a new connector, ensure you test:

### Unit Tests

- Each abstract method implementation with valid inputs
- Error handling for invalid inputs
- Connection failure scenarios
- Empty collection scenarios

### Integration Tests

- End-to-end workflows with real vector data via VectorSearchService
- Performance with large datasets
- Concurrent operation handling
- Data persistence and retrieval accuracy

### Example Test Structure

```python
import pytest
from vector_db.my_connector import MyVectorConnector
from vector_db.models import CollectionType

@pytest.fixture
def connector():
    return MyVectorConnector({
        CollectionType.INCIDENTS: "test_incidents",
        CollectionType.DOCUMENTS: "test_documents"
    })

def test_upsert_and_search(connector):
    # Test implementation
    pass

def test_error_handling(connector):
    # Test error scenarios
    pass
```

## Migration Guide

To migrate from one vector database to another:

1. **Implement the new connector** following the steps above
2. **Export existing data** from the current vector database
3. **Transform data format** if necessary for the new database
4. **Import data** into the new vector database using the new connector
5. **Update VectorSearchService** to use the new connector
6. **Run integration tests** to verify functionality
7. **Deploy with monitoring** to ensure smooth transition

## Best Practices

### Development

- Use type hints consistently
- Follow the abstract base class interface exactly
- Implement comprehensive logging
- Handle all edge cases gracefully
- Write thorough tests for all methods

### Production

- Monitor vector database performance and health
- Implement proper backup and recovery procedures
- Use appropriate hardware for vector operations
- Monitor embedding generation costs and usage

### Maintenance

- Keep vector database client libraries updated
- Monitor deprecated features in your chosen database
- Benchmark performance regularly
- Review and optimize embedding strategies (currently using Gemini)
- Document any database-specific optimizations

## Current Limitations

1. **Document Support**: Document operations are not yet fully implemented
2. **Pre-configured Instances**: No singleton instances are exported (use `VectorSearchService`)
3. **Embedding Model**: Fixed to Gemini text-embedding-004 (768 dimensions)
4. **Collection Names**: Hard-coded in the service layer

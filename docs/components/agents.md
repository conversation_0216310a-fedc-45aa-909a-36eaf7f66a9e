# AI Agents System

The incident management system uses Google Agent Development Kit (ADK) to create specialized AI agents that handle different aspects of incident resolution.

## Architecture Overview

### Agent Hierarchy

The system follows a hierarchical agent structure:

```
Coordinator Agent (Main)
├── Time Agent (Sub-agent)
├── Preference Agent (Sub-agent)
└── Specialized Agents (Tools)
    ├── Runbook Generator Agent
    ├── Root Cause Analyzer
    ├── Log Analytics Agent
    ├── Incident Manager Agent
    └── Report Agent
```

### Core Components

#### Coordinator Agent (`main_agent.py`)
- **Role**: Central orchestrator for all incident management tasks
- **Model**: `gemini/gemini-2.0-flash`
- **Description**: AI-based incident assistant that delegates tasks to specialized agents
- **Features**:
  - Intelligent task routing
  - Context management
  - User interaction handling
  - Guardrail enforcement

#### Specialized Agents
Each agent is designed for specific incident management tasks:

1. **Runbook Generator Agent**: Creates and manages incident response runbooks
2. **Root Cause Analyzer**: Analyzes incidents to identify root causes
3. **Log Analytics Agent**: Processes and analyzes system logs
4. **Incident Manager Agent**: Handles incident lifecycle management
5. **Report Agent**: Generates incident reports and documentation

### Agent Configuration

#### Model Selection
```python
# Current model configuration
AGENT_MODEL = "gemini/gemini-2.0-flash"
# Alternative models available:
# AGENT_MODEL = "ollama/qwen3:4b"
# AGENT_MODEL = "gemini/gemini-2.5-flash-preview-05-20"
```

#### Agent Tools
Agents are configured as tools for the coordinator:
```python
tools=[
    AgentTool(agent=runbook_generator_agent, skip_summarization=False),
    AgentTool(agent=root_cause_analyzer, skip_summarization=False),
    AgentTool(agent=log_analytics_agent, skip_summarization=False),
    AgentTool(agent=incident_manager_agent, skip_summarization=False),
    AgentTool(agent=report_agent, skip_summarization=False),
]
```

## Development & Debugging

### Prerequisites
- Google Agent Development Kit (ADK)
- Python 3.10+
- Required API keys (Google AI, etc.)

### Development Commands

#### CLI Mode
Run agents standalone in command line interface:
```bash
cd backend
uv run adk run app/agents
```

#### Web Interface
Launch the ADK web interface for interactive debugging:
```bash
uv run adk web app
```

#### API Server
Start the ADK API server:
```bash
uv run adk api_server app
```

### Debugging Best Practices

1. **Use CLI Mode** for quick testing and debugging
2. **Web Interface** for interactive agent development
3. **API Server** for integration testing
4. **Logging** - All agents include comprehensive logging
5. **Guardrails** - Test guardrail functionality regularly

## Agent Development Guidelines

### Creating New Agents

1. **Define Purpose**: Clearly define the agent's specific role
2. **Choose Model**: Select appropriate LLM model for the task
3. **Write Instructions**: Create detailed, specific instructions
4. **Add Tools**: Include necessary tools and functions
5. **Test Thoroughly**: Use ADK debugging tools

### Best Practices

#### Instruction Writing
- Be specific and detailed
- Include examples where helpful
- Define expected input/output formats
- Specify error handling requirements

#### Tool Integration
- Use AgentTool wrapper for sub-agents
- Configure summarization appropriately
- Handle tool failures gracefully
- Implement proper error logging

#### Performance Optimization
- Choose appropriate models for task complexity
- Optimize prompt length and structure
- Use caching where applicable
- Monitor token usage and costs

## Limitations & Constraints

### Google ADK Limitations

#### Built-in Tool Restrictions
Only one built-in tool is supported per agent when used with other tools:
```python
# NOT SUPPORTED
root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    tools=[custom_function],
    executor=[BuiltInCodeExecutor]  # Cannot mix with tools
)
```

#### Sub-agent Constraints
Built-in tools cannot be used within sub-agents:
```python
# NOT SUPPORTED as sub-agents
search_agent = Agent(
    model='gemini-2.0-flash',
    name='SearchAgent',
    tools=[google_search],  # Built-in tool
)

# SUPPORTED as tools
root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    tools=[AgentTool(agent=search_agent)]  # Wrapped as tool
)
```

### Workarounds

1. **Use AgentTool Wrapper**: Convert sub-agents to tools
2. **Separate Concerns**: Create dedicated agents for built-in tools
3. **Hierarchical Design**: Use coordinator pattern for complex workflows

## Integration with Incident Management

### Workflow Integration
Agents integrate with the incident management workflow:

1. **Incident Analysis**: Coordinator routes to appropriate analyzer
2. **System Identification**: Agents identify affected systems
3. **Log Collection**: Log analytics agent processes relevant logs
4. **Historical Context**: Vector database integration for similar incidents
5. **Documentation Retrieval**: Knowledge base integration
6. **Root Cause Analysis**: Specialized analysis agents
7. **Resolution Guidance**: Runbook generation and execution
8. **Iterative Support**: Continuous feedback and refinement

### API Integration
Agents are exposed through FastAPI endpoints:
- `/api/v1/agents/chat` - Main agent interaction
- `/api/v1/agents/status` - Agent status and health
- `/api/v1/agents/history` - Conversation history

## Monitoring & Maintenance

### Performance Monitoring
- Track agent response times
- Monitor token usage and costs
- Measure resolution success rates
- Analyze user satisfaction metrics

### Maintenance Tasks
- Regular model updates
- Prompt optimization
- Tool integration updates
- Performance tuning
- Error handling improvements

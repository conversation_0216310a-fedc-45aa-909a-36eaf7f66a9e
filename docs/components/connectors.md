# Incident Management Connectors

This package provides connectors to integrate with external systems and sync them with our incident management system. Currently, GitHub integration is fully implemented. The connectors fetch issues and convert them into a standardized incident format, which is then processed by Celery tasks for asynchronous processing.

## Architecture Overview

### Core Components

#### 1. Connectors Module (`connectors/`)
- **Base Connector** (`base_connector.py`):
  - Abstract base class defining the interface for all connectors
  - Handles database operations and data transformation
  - Provides standardized methods for incident creation and updates
  - Manages database sessions and error handling

- **Specific Connectors** (e.g., `github_connector.py`):
  - Implements the BaseConnector interface for specific platforms
  - Handles platform-specific API interactions
  - Maps platform-specific data to our incident format
  - Uses AI (Gemini) for intelligent field mapping

#### 2. Celery Tasks (`tasks/`)
- **GitHub Tasks**:
  - `github_import`: Imports issues from a GitHub repository into the incident management system
  - `incident_sync`: Syncs GitHub issues with existing incidents, updating them as needed

- **Task Flow**:
  1. Connector fetches issues from external system (e.g., GitHub)
  2. Issues are converted to standardized incident format
  3. Celery tasks (`github_import`, `incident_sync`) process incidents asynchronously
  4. Results are stored in the database and task status is updated

#### 3. API Routes & Service Layer
- **Routes (`routes/jobs/controller.py`)**:
  - `/jobs/import_github`: Triggers the `github_import` Celery task to import issues from a GitHub repository
  - `/jobs/sync_github`: Triggers the `incident_sync` Celery task to sync issues with existing incidents
  - `/jobs/status/{job_id}`: Retrieves the status and result of a submitted job
- **Service Layer (`routes/jobs/service.py`)**:
  - Handles job creation, status tracking, and database operations for job records
  - Provides abstraction between API routes and database logic, ensuring clean separation of concerns

## Testing the System

### Prerequisites

- Ensure `GITHUB_ACCESS_TOKEN` is added in the `.env` file.
- Ensure all dependencies from `requirements.txt` are installed.
- Ensure a Celery worker service is defined in `docker-compose.yml`.

### How to Use

1. Access Swagger UI:
   - Open `http://localhost:8000/docs` in your browser
   - Authenticate using the admin credentials

2. Test Connector Sync:
   - Navigate to `/api/v1/connectors/sync` endpoint
   - Use the following payload:
   ```json
   {
     "connector_type": "github",
     "config": {
       "repo_name": "pytorch/pytorch",
       "since": "2024-01-01T00:00:00Z",
       "until": "2024-03-20T00:00:00Z"
     }
   }
   ```
   - This will trigger the Celery task for syncing

3. Monitor Task Status:
   - Use `/api/v1/tasks/{task_id}` endpoint
   - Check task status and results
   - View any errors or warnings

4. View Synced Incidents:
   - Use `/api/v1/incidents` endpoint
   - Filter and view the synced incidents
   - Verify the data mapping and formatting

### Task Status Codes

- `PENDING`: Task is waiting for execution
- `STARTED`: Task has been started
- `SUCCESS`: Task completed successfully
- `FAILURE`: Task failed with an error
- `RETRY`: Task is being retried

### Error Handling

- Failed tasks are automatically retried (configurable)
- Error details are logged and available via task status
- Connector errors are captured and reported in task results

## Configuration

### Connector Configuration
- Modify `config.py` for connector-specific settings
- Adjust batch sizes and retry policies
- Configure API rate limits and timeouts

### Celery Configuration
- Task retry policies
- Worker concurrency
- Result backend settings
- Task time limits

## Monitoring and Maintenance

1. Task Monitoring:
   - Use Flower UI (`http://localhost:5555`) for Celery monitoring
   - View task history and statistics
   - Monitor worker status

2. Logging:
   - Check application logs for connector operations
   - Monitor Celery worker logs
   - Review task execution logs

3. Maintenance:
   - Regular cleanup of completed tasks
   - Monitoring of task queue size
   - Health checks for connectors

## GitHub Connector

### Authentication
The GitHub connector supports two authentication methods:
1.  **Personal Access Token (PAT)**: A simple method for individual users to grant access. The connector will use this method if a `GITHUB_ACCESS_TOKEN` is found in the environment variables.
2.  **GitHub App**: The recommended method for organizations or for providing granular permissions. The connector falls back to this method if a PAT is not provided.

### Environment Variables
Based on your chosen authentication method, you will need to configure the following environment variables.

**For Personal Access Token (PAT) Authentication:**
```
GITHUB_ACCESS_TOKEN=your_github_personal_access_token
```

**For GitHub App Authentication:**
```
GITHUB_APP_ID=your_github_app_id
GITHUB_PRIVATE_KEY_PATH=path/to/your/private-key.pem
GITHUB_INSTALLATION_ID=your_installation_id
```

### Features
- Fetches issues from GitHub repositories
- Maps GitHub issue fields to incident format
- Handles pagination for large repositories
- Supports date range filtering
- Intelligent field mapping using AI
- Rate limit handling and monitoring

### API Rate Limits
GitHub API has rate limits that vary by authentication method:
- **Personal Access Token**: 5,000 requests per hour
- **GitHub App**: 15,000 requests per hour per installation

The connector implements rate limit handling:
- Checks remaining API calls before operations
- Warns when less than 20% of rate limit remains
- Stops operations when less than 10 calls remain
- Logs rate limit status and reset times

## LLM Connector (via LiteLLM)

### LLM Support
- The system now uses [LiteLLM](https://github.com/BerriAI/litellm), which provides a unified interface to a wide range of LLM providers and models (including OpenAI, Gemini, Azure, Anthropic, Cohere, and more).
- Any LLM supported by LiteLLM can be used as the backend for this connector.

### Features
- Flexible model selection via LiteLLM configuration
- Automatic handling of provider-specific rate limits and retries (as supported by LiteLLM)
- Unified API for prompt completion, chat, and other LLM tasks

### Configuration
- See the LiteLLM documentation for details on configuring providers, API keys, and model selection.

## Environment Variables
Required environment variables:
```
GITHUB_ACCESS_TOKEN=your_github_pat
GEMINI_API_KEY=your_gemini_api_key
```

## Error Handling
Both connectors implement comprehensive error handling:
- Rate limit errors
- Authentication errors
- Network errors
- Invalid response errors

All errors are logged with appropriate context for debugging.

## Rate Limits

### GitHub API Rate Limits

#### Primary rate limit for unauthenticated users
You can make unauthenticated requests if you are only fetching public data. Unauthenticated requests are associated with the originating IP address, not with the user or application that made the request.

The primary rate limit for unauthenticated requests is 60 requests per hour.

#### Primary rate limit for authenticated users
You can use a personal access token to make API requests. Additionally, you can authorize a GitHub App or OAuth app, which can then make API requests on your behalf.

All authenticated requests count toward your personal rate limit of 5,000 requests per hour. Requests made by a GitHub App on behalf of a user also count toward the user's rate limit.

## Best Practices

### Development
- Always test connectors with small datasets first
- Implement proper error handling and logging
- Use appropriate retry strategies
- Follow the abstract base class interface

### Production
- Monitor API rate limits and usage
- Implement proper authentication and security
- Use background tasks for large operations
- Set up proper monitoring and alerting

### Maintenance
- Keep connector libraries updated
- Monitor deprecated API features
- Review and optimize data mapping strategies
- Document any platform-specific configurations

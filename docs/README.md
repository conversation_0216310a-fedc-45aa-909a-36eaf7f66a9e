# Incident Management System Documentation

Welcome to the comprehensive documentation for the AI-powered Incident Management System. This documentation provides detailed information about the system architecture, components, deployment, and usage.

## 📋 Table of Contents

### 🏗️ [Architecture](./architecture/)
System design and architectural overview
- [System Overview](./architecture/overview.md) - High-level system architecture

### 🔌 [API Documentation](./api/)
REST API endpoints and integration guides
- [API Reference](./api/endpoints.md) - API overview and Swagger documentation links

### 🧩 [Components](./components/)
Detailed documentation for each system component

#### Core Components
- [**AI Agents**](./components/agents.md) - Google ADK-based AI agents for incident resolution
- [**Vector Database**](./components/vector-database.md) - Semantic search and similarity matching
- [**Connectors**](./components/connectors.md) - Integration with external systems (GitHub, Jira, etc.)
- [**Frontend Application**](./components/frontend.md) - React-based user interface

#### Knowledge Base & RAG
- [**Knowledge Base Architecture**](./components/knowledge-base/RAG_architecture.md) - RAG implementation details
- [**Knowledge Base Implementation**](./components/knowledge-base/RAG_implementation.md) - Technical implementation

#### Specialized Integrations
- [**GitHub App Integration**](./components/connectors-github-app.md) - GitHub App setup and configuration

### 🚀 [Deployment](./deployment/)
Installation, configuration, and deployment guides
- [**Setup Guide**](./deployment/setup.md) - Complete installation instructions
- [**Log Collection**](./deployment/log-collectors/) - Log forwarding and collection setup
- [**Loki Configuration**](./deployment/loki.md) - Grafana Loki setup for log storage

### 👥 [User Guides](./user-guides/)
End-user documentation and tutorials
- [**Getting Started**](./user-guides/getting-started.md) - Quick start guide for new users

### 📄 [Templates](./templates/)
Document templates and examples
- [**Incident Report Template**](./templates/incident_report.md) - Standard incident report format

### 🛠️ [Development](./development/)
Developer resources and contribution guidelines
- [**Contributing Guide**](./development/contributing.md) - How to contribute to the project
- [**Roadmap**](./development/roadmap.md) - Future planned features and improvements

## 🚀 Quick Start

### For Users
1. Start with the [Getting Started Guide](./user-guides/getting-started.md)
2. Learn about [Incident Management](./user-guides/incident-management.md)
3. Explore the [API Documentation](./api/endpoints.md) for integrations

### For Developers
1. Review the [Architecture Overview](./architecture/overview.md)
2. Follow the [Setup Guide](./deployment/setup.md)
3. Read the [Contributing Guide](./development/contributing.md)

### For System Administrators
1. Check the [Deployment Guide](./deployment/setup.md)
2. Configure [Log Collection](./deployment/log-collectors/)
3. Set up [Environment Configuration](./deployment/environment.md)

## 🔧 Key Features Documented

### AI-Powered Resolution
- **Multi-Agent System**: Specialized AI agents for different aspects of incident management
- **Intelligent Routing**: Automatic task delegation to appropriate agents
- **Iterative Resolution**: Step-by-step guidance with feedback loops

### Integration Capabilities
- **External Systems**: GitHub, Jira, ServiceNow integration
- **Log Collection**: Centralized log aggregation and analysis
- **Vector Search**: Semantic similarity search for historical incidents

### Modern Architecture
- **Microservices**: Scalable, maintainable component architecture
- **Async Processing**: Celery-based background task processing
- **Real-time Updates**: WebSocket support for live updates

## 📚 Documentation Standards

This documentation follows these principles:
- **Comprehensive**: Covers all aspects of the system
- **Up-to-date**: Regularly updated to reflect current codebase
- **Practical**: Includes examples and real-world usage scenarios
- **Accessible**: Clear language and logical organization

## 🔍 Finding Information

### By Role
- **End Users**: Start with [User Guides](./user-guides/)
- **Developers**: Begin with [Architecture](./architecture/) and [Components](./components/)
- **DevOps/SRE**: Focus on [Deployment](./deployment/) and [API](./api/)
- **Contributors**: Review [Development](./development/) section

### By Component
- **Frontend**: [Frontend Documentation](./components/frontend.md)
- **Backend**: [API Documentation](./api/) and [Components](./components/)
- **AI Agents**: [Agents Documentation](./components/agents.md)
- **Database**: [Vector Database](./components/vector-database.md)
- **Integrations**: [Connectors](./components/connectors.md)

### By Task
- **Setup**: [Deployment Guide](./deployment/setup.md)
- **Integration**: [Connectors](./components/connectors.md) and [API](./api/)
- **Troubleshooting**: [Log Collection](./deployment/log-collectors/) and component docs
- **Development**: [Contributing Guide](./development/contributing.md)

## 📞 Support

For additional support:
- Check the relevant component documentation
- Review the [API Documentation](./api/) for integration issues
- Consult the [Troubleshooting sections](./deployment/) in deployment docs
- Open an issue on the repository for bugs or feature requests

## 📝 Contributing to Documentation

We welcome documentation improvements! Please see our [Contributing Guide](./development/contributing.md) for:
- Documentation standards and style guide
- How to submit documentation updates
- Review process for documentation changes

---

**Last Updated**: January 2025
**Version**: 1.0.0
**Maintained by**: Abilytics Inc.

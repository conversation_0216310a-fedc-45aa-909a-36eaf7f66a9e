livedebugging {
  enabled = true
}

local.file_match "system_logs" {
  path_targets = [{
      "__path__" = "/temp/logs/syslog",
      "job"       = "system-logs",
      "hostname"  = "log-server-1", },
      {
      "__path__" = "/temp/logs/alternatives.log",
      "job"       = "alternatives-logs",
      "hostname"  = "log-server-1",
    },
    {
      "__path__" = "/temp/logs/dmesg",
      "job"       = "dmesg-logs",
      "hostname"  = "log-server-1",
    },
     {
      "__path__" = "/temp/logs/dpkg.log",
      "job"       = "dpkg-logs",
      "hostname"  = "log-server-1",
    },
     {
      "__path__" = "/temp/logs/kern.log",
      "job"       = "kern-logs",
      "hostname"  = "log-server-1",
    },
    {
      "__path__" = "/temp/logs/lastlog",
      "job"       = "last-logs",
      "hostname"  = "log-server-1",
    }
      ]
  sync_period = "5s"
}


loki.source.file "system_logs_source" {
  targets    = local.file_match.system_logs.targets
  forward_to = [loki.write.loki_receiver.receiver]
}


loki.write "loki_receiver" {
  endpoint {
    url = "http://host.docker.internal:3100/loki/api/v1/push"   //Replace with the URL of the loki service hosted by InciDoc
  }
}

http {
  listen_address = "0.0.0.0:12345"  // Exposes HTTP endpoints on port 12345
}

prometheus.exporter.self "self_metrics" {}

prometheus.scrape "scrape_self" {
  targets    = prometheus.exporter.self.self_metrics.targets
  forward_to = [prometheus.remote_write.metrics_receiver.receiver]
  scrape_interval = "5s"
}

prometheus.remote_write "metrics_receiver" {
  endpoint {
    url = "http://host.docker.internal:9090/api/v1/write"     //Replace with the URL of the prometheus service hosted by InciDoc
  }
}

# Alloy Log Forwarding Template

This setup allows any system to forward its local log files to the Loki instance used by this project, using [<PERSON><PERSON>](https://grafana.com/docs/alloy/latest/). This is especially useful for collecting and centralizing logs from other hosts or subsystems.

---

## 🔧 What to Change

To use this template with your own log files, follow the steps below:

---

###  1. Modify the `config.alloy` File

Edit the `local.file_match` block in your `config.alloy` file to point to the log files on your system:

```
local.file_match "system_logs" {
  path_targets = [{
    __path__ = "/absolute/path/to/your/logfile.log",  // Change this path
    job       = "your-job-name",                      // Optional: Change job label
  }]
  start_at = "beginning"   // Optional: Read logs from the beginning ("beginning") or "end". (default: end)
  sync_period = "5s"      // Optional: Frequency of checking for new logs. (default: 1s)
  max_wait = "10s"        // Optional: max time to wait before sending even if batch is small. (default: 10s)
  labels = {              // Optional: static extra labels for all logs in this block. Purpose is to allow better filtering.
    environment = "test"
    team        = "devops"
  }
}
```

### 2. Update the Docker Compose Bind Mount

In the docker-compose.yml file for Alloy, update the volume mapping to ensure the log file is accessible inside the container:

```
services:
  alloy:
    image: grafana/alloy:v1.10.0
    ...
    volumes:
      - /absolute/path/to/your/logfile.log:/temp/logs/your_logfile.log:ro
```

Then update your config.alloy to match the internal path:
```
__path__ = "/temp/logs/your_logfile.log"
```

### 3. Ensure Loki Endpoint Matches
Send your Alloy logs to our incidoc loki endpoint:
```
loki.write "loki_receiver" {
  endpoint {
    url = "<IP Allotted by indicdoc>>:3100/loki/api/v1/push"
  }
}
```

# Grafana Loki

## Logs from Dock<PERSON>

Loki can read logs from Docker containers directly through docker plugins.
Docker plugins are not supported on Windows

### Docker plugin setup

The Docker plugin must be installed on each Docker host that will be running containers you want to collect logs from.
`docker plugin install grafana/loki-docker-driver:3.5.0-arm64 --alias loki --grant-all-permissions`
Use `-arm64` for arm64 architecture and `-amd64` for amd64 architecture.

To check if it got installed, run `docker plugin ls`

To uninstall, run `docker plugin disable loki && docker plugin rm loki`

For more info see this https://grafana.com/docs/loki/latest/send-data/docker-driver/

### Using Docker plugin

Add these options to all services to while running docker run command

```
docker run --log-driver=loki \
    --log-opt loki-url="https://incident-management-loki/loki/api/v1/push" \
    --log-opt loki-retries=2 \
    --log-opt loki-max-backoff=800ms \
    --log-opt loki-timeout=1s \
    --log-opt keep-file="true" \
    --log-opt mode="non-blocking" \
    <any image>
```

or if we are using a docker compose file, add options like these

```
services:
  <any service>:
    image: <any image>
    logging:
      driver: loki
      options:
        loki-url: "https://incident-management-loki/loki/api/v1/push"
        loki-retries: 2
        loki-max-backoff: 800ms
        loki-timeout: 1s
        keep-file: "true"
        mode: "non-blocking"
```

# Deployment Setup Guide

This guide provides comprehensive instructions for setting up the Incident Management System in various environments.

## Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+), macOS, or Windows with WSL2
- **Memory**: Minimum 8GB RAM (16GB recommended for production)
- **Storage**: Minimum 50GB free space
- **CPU**: 4+ cores recommended

### Required Software
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Git**: For repository cloning
- **Python**: 3.10+ (for development)
- **Node.js**: 18+ (for frontend development)

### API Keys & External Services
- **Google AI API Key**: For Gemini LLM services
- **GitHub Access Token**: For GitHub integration (optional)
- **Other service credentials**: As needed for integrations

## Quick Start (Docker Compose)

### 1. Clone the Repository
```bash
git clone https://bitbucket.org/abilyticsworkspace/incidentmanagement.git
cd incidentmanagement
```

### 2. Environment Configuration
```bash
# Copy the development environment file
cp .env.dev .env

# Edit the environment file with your configuration
nano .env
```

### 3. Start the Services
```bash
# Start all services
./start.sh

# Or manually with docker-compose
docker-compose up -d
```

### 4. Verify Installation
- **Frontend**: http://localhost:2000
- **Backend API**: http://localhost:2000/api
- **Qdrant**: http://localhost:6333/dashboard

## Environment Configuration

### Core Environment Variables

```bash
# Database Configuration
DATABASE_URL=****************************************/incident_management
POSTGRES_USER=incident_user
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=incident_management

# Redis Configuration
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# AI Services
GEMINI_API_KEY=your_gemini_api_key_here
LITELLM_LOG=INFO

# Vector Database
QDRANT_URL=http://qdrant:6333
QDRANT_COLLECTION=incidents
QDRANT_API_KEY=optional_for_cloud_deployment

# Log Storage (Optional - if Loki is enabled)
LOKI_BASE_URL=http://loki:3100/loki

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# External Integrations (Optional)
GITHUB_ACCESS_TOKEN=your_github_token
```

### Development vs Production

#### Development Environment (.env.dev)
```bash
# Development settings
DEBUG=true
LOG_LEVEL=DEBUG
ENVIRONMENT=development
CORS_ORIGINS=["http://localhost:2000", "http://localhost:5173"]

# Development database
DATABASE_URL=postgresql://dev_user:dev_pass@localhost:5432/incident_dev
```

#### Production Environment (.env.prod)
```bash
# Production settings
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production
CORS_ORIGINS=["https://your-domain.com"]

# Production database with connection pooling
DATABASE_URL=***********************************************/incident_prod?sslmode=require
```

## Service Configuration

### 1. PostgreSQL Database
```yaml
# docker-compose.yml excerpt
postgres:
  image: postgres:14
  environment:
    POSTGRES_USER: ${POSTGRES_USER}
    POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    POSTGRES_DB: ${POSTGRES_DB}
  volumes:
    - postgres_data:/var/lib/postgresql/data
    - ./init.sql:/docker-entrypoint-initdb.d/init.sql
  ports:
    - "5432:5432"
```

### 2. Redis Cache & Message Broker
```yaml
redis:
  image: redis:7-alpine
  command: redis-server --appendonly yes
  volumes:
    - redis_data:/data
  ports:
    - "6379:6379"
```

### 3. Qdrant Vector Database
```yaml
qdrant:
  image: qdrant/qdrant:latest
  volumes:
    - qdrant_data:/qdrant/storage
  ports:
    - "6333:6333"
  environment:
    QDRANT__SERVICE__HTTP_PORT: 6333
```

### 4. Loki Log Storage
```yaml
loki:
  image: grafana/loki:2.9.0
  ports:
    - "3100:3100"
  volumes:
    - ./loki/config:/etc/loki
    - loki_data:/loki
  command: -config.file=/etc/loki/local-config.yaml
```

## Application Deployment

### Backend Deployment
```yaml
backend:
  build:
    context: ./backend
    dockerfile: Dockerfile
  environment:
    - DATABASE_URL=${DATABASE_URL}
    - REDIS_URL=${REDIS_URL}
    - GEMINI_API_KEY=${GEMINI_API_KEY}
  depends_on:
    - postgres
    - redis
    - qdrant
  ports:
    - "8000:8000"
  volumes:
    - ./logs:/app/logs
```

### Frontend Deployment
```yaml
frontend:
  build:
    context: ./frontend
    dockerfile: Dockerfile
  environment:
    - API_URL=http://localhost:8000/api
  ports:
    - "2000:2000"
  depends_on:
    - backend
```

### Celery Workers
```yaml
celery-worker:
  build:
    context: ./backend
    dockerfile: Dockerfile
  command: celery -A utils.celery_worker worker --loglevel=info
  environment:
    - DATABASE_URL=${DATABASE_URL}
    - CELERY_BROKER_URL=${CELERY_BROKER_URL}
    - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
  depends_on:
    - postgres
    - redis
  volumes:
    - ./logs:/app/logs
```

## Production Deployment

### 1. Docker Swarm Deployment
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.prod.yml incident-management
```

### 2. Kubernetes Deployment
```yaml
# kubernetes/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: incident-management

---
# kubernetes/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: incident-management
data:
  DATABASE_URL: "************************************/incident_management"
  REDIS_URL: "redis://redis:6379/0"
```

### 3. Load Balancer Configuration
```nginx
# nginx.conf
upstream backend {
    server backend-1:8000;
    server backend-2:8000;
    server backend-3:8000;
}

server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        proxy_pass http://frontend:2000;
        proxy_set_header Host $host;
    }
}
```

## Database Setup

### 1. Initial Migration
```bash
# Run database migrations
docker-compose exec backend alembic upgrade head

# Create initial admin user
docker-compose exec backend python -c "
from db_services.users import create_user
from database.core import DbSession
with DbSession() as db:
    create_user(db, '<EMAIL>', 'admin123', 'admin')
"
```

### 2. Backup & Restore
```bash
# Backup database
docker-compose exec postgres pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > backup.sql

# Restore database
docker-compose exec -T postgres psql -U ${POSTGRES_USER} ${POSTGRES_DB} < backup.sql
```

## Monitoring & Health Checks

### 1. Health Check Endpoints
- **Backend**: `GET /health`
- **Database**: Connection test via backend
- **Redis**: Connection test via backend
- **Celery**: Worker status via `celery -A utils.celery_worker inspect ping`

### 2. Log Monitoring
```bash
# View application logs
docker-compose logs -f backend

# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f celery-worker
```

### 3. Performance Monitoring
```bash
# Monitor resource usage
docker stats

# Monitor specific container
docker stats incident-management_backend_1
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database status
docker-compose ps postgres

# Check database logs
docker-compose logs postgres

# Test connection
docker-compose exec backend python -c "
from database.core import engine
print(engine.execute('SELECT 1').scalar())
"
```

#### 2. Redis Connection Issues
```bash
# Check Redis status
docker-compose ps redis

# Test Redis connection
docker-compose exec redis redis-cli ping
```

#### 3. Celery Worker Issues
```bash
# Check worker status
docker-compose ps celery

# View worker logs
docker-compose logs celery

# Restart workers
docker-compose restart celery
```

### Performance Tuning

#### 1. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_incidents_created_at ON incidents(created_at);
CREATE INDEX idx_incidents_status ON incidents(status);
CREATE INDEX idx_incidents_priority ON incidents(priority);
```

#### 2. Redis Configuration
```bash
# Optimize Redis memory usage
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

#### 3. Application Scaling
```bash
# Scale services
docker-compose up -d --scale celery=3 --scale backend=2
```

## Security Considerations

### 1. Network Security
- Use internal Docker networks
- Expose only necessary ports
- Implement proper firewall rules
- Use TLS/SSL in production

### 2. Data Security
- Encrypt sensitive environment variables
- Use secrets management systems
- Regular security updates
- Backup encryption

### 3. Access Control
- Implement proper authentication
- Use role-based access control
- Regular access reviews
- Audit logging

## Maintenance

### 1. Regular Updates
```bash
# Update Docker images
docker-compose pull
docker-compose up -d

# Update application code
git pull origin main
docker-compose build
docker-compose up -d
```

### 2. Cleanup
```bash
# Remove unused Docker resources
docker system prune -a

# Clean up old logs
find ./logs -name "*.log" -mtime +30 -delete
```

### 3. Backup Strategy
- Daily database backups
- Weekly full system backups
- Test restore procedures
- Offsite backup storage

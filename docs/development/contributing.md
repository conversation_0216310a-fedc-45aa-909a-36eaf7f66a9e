# Contributing Guide

Thank you for your interest in contributing to the Incident Management System! This guide will help you get started with contributing code, documentation, and other improvements.

## Getting Started

### Prerequisites

Before you begin, ensure you have:
- **Git**: For version control
- **Docker & Docker Compose**: For local development
- **Python 3.10+**: For backend development
- **Node.js 18+**: For frontend development
- **Code Editor**: VS Code recommended with extensions

### Development Environment Setup

1. **Fork and Clone**
   ```bash
   # Fork the repository on Bitbucket
   git clone https://bitbucket.org/your-username/incidentmanagement.git
   cd incidentmanagement
   ```

2. **Set Up Environment**
   ```bash
   # Copy development environment
   cp .env.dev .env

   # Edit with your configuration
   nano .env
   ```

3. **Start Development Services**
   ```bash
   # Start all services
   ./start.sh

   # Or start specific services
   docker-compose up -d postgres redis qdrant loki
   ```

4. **Install Dependencies**
   ```bash
   # Backend dependencies
   cd backend
   pip install -r requirements-dev.txt

   # Frontend dependencies
   cd ../frontend
   pnpm install
   ```

## Development Workflow

### Branch Strategy

We use a feature branch workflow:

1. **Main Branch**: `main` - Production-ready code
2. **Feature Branches**: `feature/description` - New features
3. **Bug Fixes**: `fix/description` - Bug fixes
4. **Documentation**: `docs/description` - Documentation updates

### Creating a Feature Branch

```bash
# Create and switch to feature branch
git checkout -b feature/add-slack-integration

# Make your changes
# ... code changes ...

# Commit your changes
git add .
git commit -m "feat: add Slack integration for incident notifications"

# Push to your fork
git push origin feature/add-slack-integration
```

### Commit Message Convention

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```bash
feat(agents): add Slack notification agent
fix(api): resolve database connection timeout
docs(readme): update installation instructions
test(incidents): add unit tests for incident creation
```

## Code Standards

### Backend (Python)

#### Code Style
- **Formatter & Linter**: Ruff (replaces Black, Flake8, isort)
- **Type Hints**: Required for all functions
- **Pre-commit**: Automated code quality checks

#### Code Quality Tools
```bash
# Format & Lint code
ruff format backend/app/

# Type checking
mypy backend/app/
```

#### Best Practices
- Use type hints for all function parameters and return values
- Write docstrings for all public functions and classes
- Follow PEP 8 style guidelines
- Use meaningful variable and function names
- Keep functions small and focused

#### Example Code Structure
```python
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

class IncidentCreate(BaseModel):
    """Model for creating new incidents."""
    title: str
    description: str
    priority: str

def create_incident(
    incident_data: IncidentCreate,
    user_id: UUID
) -> IncidentResponse:
    """
    Create a new incident.

    Args:
        incident_data: Incident creation data
        user_id: ID of the user creating the incident

    Returns:
        Created incident data

    Raises:
        HTTPException: If creation fails
    """
    # Implementation here
    pass
```

### Frontend (TypeScript/React)

#### Code Style
- **Formatter**: Prettier
- **Linter**: ESLint with TypeScript rules
- **Type Checking**: Strict TypeScript configuration

#### Code Quality Tools
```bash
# Format code
pnpm format

# Lint code
pnpm lint

# Type checking (via build)
pnpm build
```

#### Best Practices
- Use TypeScript for all components and utilities
- Follow React Hooks best practices
- Use meaningful component and variable names
- Keep components small and focused
- Use proper error boundaries

#### Example Component Structure
```typescript
import React from 'react';
import { Button, Card, Text } from '@mantine/core';

interface IncidentCardProps {
  incident: Incident;
  onUpdate: (id: string) => void;
}

export const IncidentCard: React.FC<IncidentCardProps> = ({
  incident,
  onUpdate
}) => {
  const handleUpdate = () => {
    onUpdate(incident.id);
  };

  return (
    <Card shadow="sm" padding="lg">
      <Text size="lg" weight={500}>
        {incident.title}
      </Text>
      <Button onClick={handleUpdate}>
        Update
      </Button>
    </Card>
  );
};
```

## Testing

### Backend Testing

#### Test Structure
```
backend/tests/
├── conftest.py              # Test configuration
├── routes/                  # API endpoint tests
│   ├── test_incidents.py
│   └── test_auth.py
├── services/                # Business logic tests
│   └── test_incident_service.py
└── utils/                   # Utility tests
    └── test_helpers.py
```

#### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/routes/test_incidents.py

# Run specific test
pytest tests/routes/test_incidents.py::test_create_incident
```

#### Writing Tests
```python
import pytest
from fastapi.testclient import TestClient

def test_create_incident(client: TestClient, auth_headers):
    """Test incident creation."""
    incident_data = {
        "title": "Test Incident",
        "description": "Test description",
        "priority": "High"
    }

    response = client.post(
        "/incidents/create",
        json=incident_data,
        headers=auth_headers
    )

    assert response.status_code == 201
    assert response.json()["title"] == "Test Incident"
```

### Frontend Testing

#### Test Structure
```
frontend/src/
├── components/
│   └── __tests__/
│       └── IncidentCard.test.tsx
├── hooks/
│   └── __tests__/
│       └── useIncident.test.ts
└── utils/
    └── __tests__/
        └── helpers.test.ts
```

#### Running Tests
```bash
# Run all tests
pnpm test

# Run in watch mode
pnpm test:watch

# Run with coverage
pnpm test --coverage
```

#### Writing Tests
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { IncidentCard } from '../IncidentCard';

const mockIncident = {
  id: '1',
  title: 'Test Incident',
  priority: 'High'
};

test('renders incident card with title', () => {
  const onUpdate = jest.fn();

  render(
    <IncidentCard
      incident={mockIncident}
      onUpdate={onUpdate}
    />
  );

  expect(screen.getByText('Test Incident')).toBeInTheDocument();
});

test('calls onUpdate when button clicked', () => {
  const onUpdate = jest.fn();

  render(
    <IncidentCard
      incident={mockIncident}
      onUpdate={onUpdate}
    />
  );

  fireEvent.click(screen.getByText('Update'));
  expect(onUpdate).toHaveBeenCalledWith('1');
});
```

## Documentation

### Documentation Standards

- **Clear and Concise**: Write for your audience
- **Examples**: Include practical examples
- **Up-to-date**: Keep documentation current with code
- **Structured**: Use consistent formatting and organization

### Types of Documentation

1. **API Documentation**: Endpoint descriptions and examples
2. **Component Documentation**: Usage and props
3. **Architecture Documentation**: System design and patterns
4. **User Guides**: End-user instructions
5. **Developer Guides**: Technical implementation details

### Writing Documentation

```markdown
# Component Name

Brief description of what the component does.

## Usage

```typescript
import { ComponentName } from './ComponentName';

<ComponentName
  prop1="value1"
  prop2={value2}
  onAction={handleAction}
/>
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| prop1 | string | Yes | Description of prop1 |
| prop2 | number | No | Description of prop2 |

## Examples

### Basic Usage
[Example code here]

### Advanced Usage
[Advanced example here]
```

## Pull Request Process

### Before Submitting

1. **Test Your Changes**
   ```bash
   # Backend tests
   cd backend && pytest

   # Frontend tests
   cd frontend && pnpm test

   # Integration tests
   docker-compose up -d && ./run-integration-tests.sh
   ```

2. **Code Quality Checks**
   ```bash
   # Backend
   ruff format backend/app/
   ruff check backend/app/

   # Frontend
   pnpm lint
   pnpm format
   ```

3. **Update Documentation**
   - Update relevant documentation
   - Add or update tests
   - Update CHANGELOG.md if applicable

### Submitting a Pull Request

1. **Create Pull Request**
   - Use descriptive title and description
   - Reference related issues
   - Include screenshots for UI changes
   - Add reviewers

2. **Pull Request Template**
   ```markdown
   ## Description
   Brief description of changes

   ## Type of Change
   - [ ] Bug fix
   - [ ] New feature
   - [ ] Documentation update
   - [ ] Refactoring

   ## Testing
   - [ ] Unit tests pass
   - [ ] Integration tests pass
   - [ ] Manual testing completed

   ## Screenshots (if applicable)
   [Add screenshots here]

   ## Checklist
   - [ ] Code follows style guidelines
   - [ ] Self-review completed
   - [ ] Documentation updated
   - [ ] Tests added/updated
   ```

### Review Process

1. **Automated Checks**: CI/CD pipeline runs tests and quality checks
2. **Code Review**: Team members review code and provide feedback
3. **Address Feedback**: Make requested changes
4. **Approval**: Get approval from maintainers
5. **Merge**: Maintainer merges the pull request

## Issue Reporting

### Bug Reports

Use the bug report template:

```markdown
## Bug Description
Clear description of the bug

## Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See error

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- OS: [e.g., Ubuntu 20.04]
- Browser: [e.g., Chrome 96]
- Version: [e.g., 1.0.0]

## Additional Context
Any other relevant information
```

### Feature Requests

Use the feature request template:

```markdown
## Feature Description
Clear description of the feature

## Use Case
Why is this feature needed?

## Proposed Solution
How should this feature work?

## Alternatives Considered
Other approaches considered

## Additional Context
Any other relevant information
```

## Community Guidelines

### Code of Conduct

- Be respectful and inclusive
- Welcome newcomers and help them learn
- Focus on constructive feedback
- Respect different viewpoints and experiences

### Communication

- **Issues**: For bug reports and feature requests
- **Pull Requests**: For code contributions
- **Discussions**: For general questions and ideas
- **Email**: For security issues or private matters

## Getting Help

### Resources

- **Documentation**: Check the docs directory
- **API Reference**: `/docs` endpoint when running locally
- **Examples**: Look at existing code for patterns
- **Tests**: Review test files for usage examples

### Support Channels

- **GitHub Issues**: For bugs and feature requests
- **Pull Request Comments**: For code-specific questions
- **Team Chat**: For real-time discussions (if available)

## Recognition

Contributors are recognized in:
- **CONTRIBUTORS.md**: List of all contributors
- **Release Notes**: Major contributions highlighted
- **Documentation**: Author attribution where appropriate

Thank you for contributing to the Incident Management System!

# System Architecture Overview

The Incident Management System is built with a modern, scalable microservices architecture designed to handle complex incident resolution workflows through AI-powered automation and intelligent routing.

## High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React Frontend]
        WS[WebSocket Client]
    end

    subgraph "API Gateway Layer"
        API[FastAPI Backend]
        AUTH[Authentication]
        CORS[CORS Middleware]
    end

    subgraph "AI Agent Layer"
        COORD[Coordinator Agent]
        SUB1[Time Agent]
        SUB2[Preference Agent]
        TOOL1[Runbook Generator]
        TOOL2[Root Cause Analyzer]
        TOOL3[Log Analytics Agent]
        TOOL4[Incident Manager]
        TOOL5[Report Agent]
    end

    subgraph "Processing Layer"
        CELERY[Celery Workers]
        REDIS[Redis Queue]
        TASKS[Background Tasks]
    end

    subgraph "Data Layer"
        PG[(PostgreSQL)]
        QDRANT[(Qdrant Vector DB)]
        LOKI[(Loki Logs)]
    end

    subgraph "External Integrations"
        GITHUB[GitHub API]
        JIRA[Jira API]
        SNOW[ServiceNow API]
        GEMINI[Google AI]
    end

    UI --> API
    WS --> API
    API --> AUTH
    API --> COORD
    COORD --> SUB1
    COORD --> SUB2
    COORD --> TOOL1
    COORD --> TOOL2
    COORD --> TOOL3
    COORD --> TOOL4
    COORD --> TOOL5
    API --> CELERY
    CELERY --> REDIS
    CELERY --> TASKS
    API --> PG
    TOOL1 --> QDRANT
    TOOL3 --> LOKI
    TASKS --> GITHUB
    TASKS --> JIRA
    TASKS --> SNOW
    COORD --> GEMINI
```

## Core Components

### 1. Frontend Layer
- **React Application**: Modern TypeScript-based UI
- **Mantine UI**: Comprehensive component library
- **React Query**: Server state management
- **WebSocket**: Real-time updates and notifications

### 2. API Gateway Layer
- **FastAPI**: High-performance Python web framework
- **Authentication**: JWT-based user authentication
- **CORS**: Cross-origin resource sharing configuration
- **Middleware**: Request/response processing and logging

### 3. AI Agent Layer
- **Google ADK**: Agent Development Kit for AI orchestration
- **Coordinator Agent**: Central intelligence for task routing
- **Sub-agents**: Specialized agents for specific functions
- **Tool Agents**: Task-specific AI tools for incident resolution

### 4. Processing Layer
- **Celery**: Distributed task queue for async processing
- **Redis**: Message broker and result backend
- **Background Tasks**: Long-running operations and integrations

### 5. Data Layer
- **PostgreSQL**: Primary relational database
- **Qdrant**: Vector database for semantic search
- **Loki**: Log aggregation and storage

### 6. External Integrations
- **GitHub**: Issue tracking and repository integration
- **Jira**: Enterprise issue management
- **ServiceNow**: ITSM platform integration
- **Google AI**: LLM services for agent intelligence

## Design Principles

### 1. Microservices Architecture
- **Separation of Concerns**: Each component has a specific responsibility
- **Loose Coupling**: Components communicate through well-defined interfaces
- **Independent Scaling**: Components can be scaled independently
- **Technology Diversity**: Best tool for each job

### 2. Event-Driven Architecture
- **Async Processing**: Non-blocking operations for better performance
- **Event Sourcing**: Track all changes and state transitions
- **Message Queues**: Reliable message delivery and processing
- **Real-time Updates**: WebSocket-based live notifications

### 3. AI-First Design
- **Intelligent Routing**: AI determines the best agent for each task
- **Context Awareness**: Agents maintain context across interactions
- **Learning Capability**: System improves through usage patterns
- **Human-in-the-Loop**: AI augments human decision-making

### 4. Scalability & Performance
- **Horizontal Scaling**: Add more instances to handle load
- **Caching Strategy**: Redis for session and query caching
- **Database Optimization**: Proper indexing and query optimization
- **CDN Integration**: Static asset delivery optimization

## Data Flow

### 1. Incident Creation Flow
```
User Input → Frontend → API → Database → Vector DB → AI Analysis → Response
```

### 2. AI Agent Processing Flow
```
Request → Coordinator → Route to Specialist → Process → Generate Response → Store Results
```

### 3. Background Task Flow
```
Trigger → Celery Queue → Worker → External API → Database Update → Notification
```

### 4. Log Analysis Flow
```
Log Ingestion → Loki Storage → AI Analysis → Pattern Recognition → Alert Generation
```

## Security Architecture

### 1. Authentication & Authorization
- **JWT Tokens**: Stateless authentication
- **Role-Based Access**: Granular permission system
- **API Key Management**: Secure external service authentication
- **Session Management**: Secure session handling

### 2. Data Protection
- **Encryption at Rest**: Database and file encryption
- **Encryption in Transit**: TLS/SSL for all communications
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Parameterized queries

### 3. Network Security
- **CORS Configuration**: Controlled cross-origin access
- **Rate Limiting**: API abuse prevention
- **Firewall Rules**: Network-level access control
- **VPN Access**: Secure administrative access

## Deployment Architecture

### 1. Containerization
- **Docker**: Application containerization
- **Docker Compose**: Local development orchestration
- **Multi-stage Builds**: Optimized container images
- **Health Checks**: Container health monitoring

### 2. Orchestration
- **Kubernetes**: Production container orchestration
- **Service Mesh**: Inter-service communication
- **Load Balancing**: Traffic distribution
- **Auto-scaling**: Dynamic resource allocation

### 3. Monitoring & Observability
- **Logging**: Centralized log aggregation
- **Metrics**: Performance and business metrics
- **Tracing**: Distributed request tracing
- **Alerting**: Proactive issue detection

## Performance Characteristics

### 1. Response Times
- **API Endpoints**: < 200ms average response time
- **AI Agent Processing**: 2-5 seconds for complex analysis
- **Database Queries**: < 50ms for optimized queries
- **Vector Search**: < 1 second for similarity searches

### 2. Throughput
- **Concurrent Users**: 1000+ simultaneous users
- **API Requests**: 10,000+ requests per minute
- **Background Tasks**: 100+ concurrent task processing
- **Log Processing**: 1M+ log entries per hour

### 3. Scalability Limits
- **Database**: Handles millions of incidents
- **Vector Database**: Supports millions of embeddings
- **Agent Processing**: Scales with worker instances
- **Storage**: Petabyte-scale log storage capability

## Technology Stack

### Backend
- **Python 3.13**: Core programming language
- **FastAPI**: Web framework
- **SQLAlchemy**: ORM and database toolkit
- **Celery**: Distributed task queue
- **Pydantic**: Data validation and serialization

### Frontend
- **React 19**: UI framework
- **TypeScript**: Type-safe JavaScript
- **Vite**: Build tool and development server
- **Mantine**: UI component library
- **TanStack Query**: Server state management

### Infrastructure
- **PostgreSQL 14+**: Primary database
- **Redis**: Caching and message broker
- **Qdrant**: Vector database
- **Loki**: Log storage
- **Docker**: Containerization

### AI & ML
- **Google ADK**: Agent development framework
- **Gemini**: Large language model
- **LiteLLM**: Multi-provider LLM interface
- **Text Embeddings**: Semantic search capabilities

## Current Limitations

### Known Constraints
- **Single Region**: Currently deployed in single region
- **Synchronous Processing**: Some operations are synchronous
- **Manual Scaling**: Requires manual intervention for scaling
- **Text-only AI**: AI agents currently handle text input only

### Performance Considerations
- Monitor database connection pool usage
- Vector database performance depends on embedding dimensions
- Celery worker scaling based on queue depth
- Log storage grows over time and requires management

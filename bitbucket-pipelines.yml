image: atlassian/default-image:4
definitions:
  caches:
    pnpm: $BITBUCKET_CLONE_DIR/.pnpm-store
pipelines:
  branches:
    main:
      - parallel:
          - step:
              name: Unit testing of backend
              image: python:3.11
              caches:
                - pip
              script:
                - cd backend
                - pip install -r requirements-dev.txt
                - pytest

          - step:
              name: Unit testing of frontend
              image: node:22
              caches:
                - pnpm
              script:
                - cd frontend
                - npm install -g pnpm
                - pnpm config set store-dir $BITBUCKET_CLONE_DIR/.pnpm-store
                - pnpm i
                - pnpm run test

      - parallel:
          - step:
              name: Docker Build of Backend
              caches:
                - docker
              script:
                - cd backend
                - docker build -t backend:test .
              services:
                - docker

          - step:
              name: Docker Build of frontend
              caches:
                - docker
              script:
                - cd frontend
                - docker build -t frontend:test .
              services:
                - docker

    develop:
      - parallel:
          - step:
              name: Unit testing of backend
              image: python:3.11
              caches:
                - pip
              script:
                - cd backend
                - pip install -r requirements-dev.txt
                - pytest

          - step:
              name: Unit testing of frontend
              image: node:22
              caches:
                - pnpm
              script:
                - cd frontend
                - npm install -g pnpm
                - pnpm config set store-dir $BITBUCKET_CLONE_DIR/.pnpm-store
                - pnpm i
                - pnpm run test

      - step:
          name: Docker Build of Backend and push to ECR
          oidc: true
          caches:
            - docker
          script:
            - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
            - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
            - cd backend
            - docker build -t agentic-ai/incident-management:backend_$BITBUCKET_BUILD_NUMBER .

            - pipe: atlassian/aws-ecr-push-image:2.6.0
              variables:
                AWS_OIDC_ROLE_ARN: $AWS_ROLE_ARN
                AWS_DEFAULT_REGION: $AWS_REGION
                IMAGE_NAME: agentic-ai/incident-management
                TAGS: "backend_$BITBUCKET_BUILD_NUMBER"

          services:
            - docker

      - step:
          name: Docker Build of frontend and push to ECR
          oidc: true
          caches:
            - docker
          script:
            - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
            - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
            - cd frontend
            - docker build -t agentic-ai/incident-management:frontend_$BITBUCKET_BUILD_NUMBER .

            - pipe: atlassian/aws-ecr-push-image:2.6.0
              variables:
                AWS_OIDC_ROLE_ARN: $AWS_ROLE_ARN
                AWS_DEFAULT_REGION: $AWS_REGION
                IMAGE_NAME: agentic-ai/incident-management
                TAGS: "frontend_$BITBUCKET_BUILD_NUMBER"

          services:
            - docker

  pull-requests:
    "**":
      - parallel:
          - step:
              name: Unit testing of backend
              image: python:3.11
              caches:
                - pip
              script:
                - cd backend
                - pip install -r requirements-dev.txt
                - pytest

          - step:
              name: Unit testing of frontend
              image: node:22
              caches:
                - pnpm
              script:
                - cd frontend
                - npm install -g pnpm
                - pnpm config set store-dir $BITBUCKET_CLONE_DIR/.pnpm-store
                - pnpm i
                - pnpm run test

      - parallel:
          - step:
              name: Docker Build of Backend
              caches:
                - docker
              script:
                - cd backend
                - docker build -t backend:test .
              services:
                - docker

          - step:
              name: Docker Build of frontend
              caches:
                - docker
              script:
                - cd frontend
                - docker build -t frontend:test .
              services:
                - docker

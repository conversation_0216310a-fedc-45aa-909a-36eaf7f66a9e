# Celery
CELERY_BROKER_URL="redis://localhost:6379/0"
CELERY_RESULT_BACKEND="redis://localhost:6379/0"
# Database
DATABASE_URL="ENTER_YOUR_DATABASE_URL_HERE"
POSTGRES_USER="ADD_YOUR_POSTGRES_USER"
POSTGRES_PASSWORD="ADD_YOUR_POSTGRES_PASSWORD"
POSTGRES_DB="ADD_YOUR_POSTGRES_DB_NAME"
# Gemini LLM
GEMINI_API_KEY="ADD_YOUR_KEY_HERE"
GOOGLE_GENAI_USE_VERTEXAI=false
# Github Connector via Access Token (Required - for Github Import/Sync and Github MCP Agent)
GITHUB_ACCESS_TOKEN="ADD_YOUR_GITHUB_TOKEN"
# Github Connector via Github App (Optional - for Github Import/Sync use only if no PAT available)
GITHUB_APP_ID="APP_ID"
GITHUB_APP_CLIENT_ID="APP_CLIENT_ID"
GITHUB_APP_CLIENT_SECRET="APP_CLIENT_SECRET"
GITHUB_APP_PRIVATE_KEY="PRIVATE_KEY"
# Logs Connector
LOKI_BASE_URL="http://loki:3100/loki"
# OpenAI LLM (Optional)
OPENAI_API_KEY="ADD_YOUR_KEY_HERE"
# User Authentication
SECRET_KEY='ENTER_YOUR_SECRET_KEY_HERE'
ALGORITHM='HS256'
ACCESS_TOKEN_EXPIRE_MINUTES=30
# Vector DB
QDRANT_URL="http://qdrant:6333"
# Slack (Optional - for Slack MCP Agent)
SLACK_BOT_TOKEN="xoxb-ENTER_YOUR_TOKEN_HERE"
SLACK_TEAM_ID="ENTER_YOUR_TEAM_ID_HERE"
# Grafana (Optional - for Grafana MCP Agent)
GRAFANA_URL="http://grafana:3000"
GRAFANA_API_KEY="ENTER_GRAFANA_SERVICE_TOKEN_HERE"

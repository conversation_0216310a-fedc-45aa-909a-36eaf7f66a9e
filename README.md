# Incident Management System

A comprehensive AI-powered incident management system designed to help Site Reliability Engineers (SREs) efficiently resolve system issues through intelligent analysis, automated workflows, and contextual recommendations.

## Features

- **AI-Powered Incident Analysis**: Leverage advanced AI agents to analyze incidents and provide resolution recommendations
- **Multi-Source Integration**: Connect with GitHub, Jira, ServiceNow, and other incident sources
- **Vector Database**: Find similar past incidents for faster resolution
- **Knowledge Base**: Maintain and retrieve relevant documentation
- **Log Analysis**: Collect and analyze logs from various sources
- **Runbook Automation**: Execute predefined runbooks for common issues
- **Iterative Resolution**: Step-by-step guidance with feedback loops

## Architecture

The system is built with a modern microservices architecture:

- **Backend**: FastAPI-based Python backend with Celery for async tasks
- **Frontend**: React-based UI with modern components
- **Database**: PostgreSQL for relational data
- **Vector Database**: Qdrant for semantic search
- **Log Storage**: Loki for log aggregation and search
- **Agentic AI Integration**: Google ADK with Sequential/Parallel/LoopAgents

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Python 3.10+
- Node.js 18+
- PostgreSQL 14+
- API keys for external services (GitHub, Google AI, etc.)

### Installation

1. Clone the repository:
   ```bash
   git clone https://bitbucket.org/abilyticsworkspace/incidentmanagement.git
   cd incidentmanagement
   ```

2. Set up environment variables:
   ```bash
   cp .env.dev .env
   # Edit .env with your configuration
   ```

3. Start the services:
   ```bash
   ./start.sh
   ```

4. Access the application:
   - Frontend: http://localhost:2000
   - Backend API: http://localhost:2000/api
   - Backend Docs: http://localhost:8000/docs
   - Celery Flower: http://localhost:5555

## Documentation

Comprehensive documentation is available in the [docs](./docs) directory:

- [Architecture Overview](./docs/architecture/overview.md)
- [API Documentation](./docs/api/endpoints.md)
- [Component Documentation](./docs/components)
  - [Vector Database](./docs/components/vector-database.md)
  - [Connectors](./docs/components/connectors.md)
  - [Agents](./docs/components/agents.md)
- [Deployment Guide](./docs/deployment/setup.md)
- [User Guides](./docs/user-guides)
- [Templates](./docs/templates)
- [Development Guide](./docs/development/contributing.md)

## Contributing

We welcome contributions! Please see our [Contributing Guide](./docs/development/contributing.md) for details.

## License

This project is owned by Abilytics Inc. - see the LICENSE file for details.

## Support

For support, please open an issue on the repository or contact the maintainers.

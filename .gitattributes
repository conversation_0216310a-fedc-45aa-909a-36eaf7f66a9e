# Set the default behavior, in case people don't have core.autocrlf set.
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.py text eol=lf
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.txt text eol=lf
*.sql text eol=lf
*.sh text eol=lf
*.html text eol=lf
*.css text eol=lf
*.xml text eol=lf
*.csv text eol=lf
*.log text eol=lf
*.ini text eol=lf
*.conf text eol=lf
*.config text eol=lf
Dockerfile text eol=lf
*.dockerfile text eol=lf

# Declare files that will always have CRLF line endings on checkout.
# (None in this case, but kept for reference)

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pyc binary
*.pem binary
*.key binary
*.p12 binary
*.p7b binary
*.p7r binary
*.src binary
*.crt binary
*.der binary
*.jks binary
*.pfx binary
*.p12 binary
